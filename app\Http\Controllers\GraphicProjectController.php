<?php

namespace App\Http\Controllers;

use Exception;
use Illuminate\Http\Request;
use App\Models\DesignCategory;
use App\Models\GraphicProject;
use App\Models\LeadGraphicPlan;
use App\Models\GraphicLeadBrand;
use Aws\S3\Exception\S3Exception;
use App\Models\GraphicProjectChat;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Crypt;
use App\Models\GraphicFileDeliverable;
use App\Models\GraphicProjectAttachment;
use Illuminate\Database\QueryException;
use Illuminate\Support\Facades\Validator;

class GraphicProjectController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index($id = null)
    {
        $data['sort']  = request('sort') == 'DESC' ? 'ASC' : 'DESC'; // Default sort order
        
        try {
            if ($search = request('search')) {
                $data['search'] = $search;
                $data['plans'] = LeadGraphicPlan::where('lead_id', Auth::id())
                    ->orderBy('id', 'desc')
                    ->get();

                $data['projects'] = GraphicProject::where('lead_id', Auth::id())
                    ->where(function ($query) use ($search) {
                        $query->where('title', 'like', "%{$search}%")
                            ->orWhere('description', 'like', "%{$search}%")
                            ->orWhere('ts', 'like', "%{$search}%");
                    })
                    ->get();

                return view('userpages.projects.graphic.search', $data);
            }

            $data['plan'] = $plan = $id
                ? LeadGraphicPlan::where('id', $id)->where('lead_id', Auth::id())->first()
                : LeadGraphicPlan::where('lead_id', Auth::id())->orderBy('id', 'desc')->first();

            if (!$plan) {
                return $id
                    ? redirect()->back()->with('warning', 'Invalid Request')
                    : view('userpages.projects.graphic.emptyproject');
            }
            $data['id'] = $plan->id;

            DB::beginTransaction();

            $data['plan'] =  LeadGraphicPlan::where('lead_id', Auth::id())->orderBy('id', 'desc')->with('plan')->get();
            if ($data["plan"] != null) {
                foreach ($data["plan"] as $plan) {
                    $data['activeprojects'][$plan->id] = GraphicProject::where('lead_id', Auth::id())
                        ->orderBy('ts', $data['sort'])
                        ->where('lead_graphic_plan_id', (int) $plan->id)
                        ->whereBetween('status', [0, 1, 2, 3, 5])
                        ->paginate(10, '*', 'project_active')->withQueryString();
                }
            }
            if ($id == null) {
                $id = $data['plan'][0]->id;
            }
            $data['current'] = LeadGraphicPlan::where('id', $id)->first();

            $baseQuery = GraphicProject::where('lead_id', Auth::id())
                ->orderBy('ts', $data['sort'])
                ->where('lead_graphic_plan_id', (int) $id);

            $data['projects'] = (clone $baseQuery)->paginate(2, '*', 'allProject')->withQueryString();
            $data['allprojects'] = (clone $baseQuery)->paginate(10, '*', 'project_all')->withQueryString();
            $data['pending'] = (clone $baseQuery)->where('status', 3)->paginate(10, '*', 'project_complete')->withQueryString();
            $data['revision'] = (clone $baseQuery)->where('status', 5)->paginate(10, '*', 'project_complete')->withQueryString();
            $data['draftprojects'] = (clone $baseQuery)->whereBetween('status', [0, 1])->paginate(10, '*', 'project_draft')->withQueryString();
            $data['completeprojects'] = (clone $baseQuery)->where('status', 6)->paginate(10, '*', 'project_complete')->withQueryString();

            DB::commit();
        } catch (Exception $e) {
            DB::rollBack();
            return redirect()->back()->with('error', ErrMsg());
        }
        return view('userpages.projects.graphic.index', $data);
    }
    /*
    0 - Draft
    1 - Unassigned
    2 - Assigned
    3 - QC Pending
    4 - Sent to Customer
    5 - Need Revision
    6 - Completed
     */

    /**
     * Show the form for creating a new resource.
     */
    public function create($id)
    {
        if ($plan = LeadGraphicPlan::where('id', $id)->where('lead_id', Auth::id())->first()) {
            if (!$plan->isActive()) {
                return redirect()->back()->with('warning', 'Oops! Plan Expired , Contact to your Account Manager');
            }
        } else {
            return redirect()->back()->with('warning', 'invalid Request');
        }
        DB::beginTransaction();

        $design_category = DesignCategory::get();
        $file_types = GraphicFileDeliverable::get();
        $brands = GraphicLeadBrand::where('lead_id', Auth::user()->id)->orderBy('ts', 'DESC')->get();
        DB::commit();
        return view("userpages.projects.graphic.Create", compact('design_category', 'plan', 'file_types', 'brands'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        try {
            $palnid =  Crypt::decryptString($request->key);
        } catch (Exception $e) {
            return redirect()->back()->with('warning', 'Error in fetching Data')->withInput();
        }
        $validator = Validator::make(
            $request->all(),
            [
                'name' => ['required'],
                'attachment' => 'max:10240',
                'description' => ['required'],
                'file_deliverable_id' => ['required'],
                'design_id' => ['required']
            ],
            [
                'name.required' => 'Project Name Required',
                'design_id.required' => 'Select Design Category.',
                'description.required' => 'Project Details Required So That We Can Provide the Best Service.',
                'file_deliverable_id.required' => 'Select Source File You Want get delivered',
            ]
        );
        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator) // send back all errors to the login form
                ->withInput();
        }

        ($files = $request->file('attachment'));
        try {
            $plan = LeadGraphicPlan::where('id', $palnid)->where('lead_id', Auth::id())->first();
        } catch (Exception $e) {
            return redirect()->back()->with('warning', 'invalid Request')->withInput();
        }
        try {
            DB::beginTransaction();

            $data = new GraphicProject;
            $data->graphic_lead_brand_id = $request->lead_brand_id;
            $data->lead_graphic_plan_id = $plan->id;
            $data->lead_id = Auth::id();
            $data->graphic_project_category_id = $request->design_id; //graphic_project_deliverable_id
            $data->graphic_project_deliverable_id = collect($request->file_deliverable_id)->implode(',');
            $data->status = 0;
            $data->title = $request->name;
            $data->description = $request->description;
            $data->dimensionHeight = $request->dimensions_height;
            $data->dimensionWidth = $request->dimensions_width;
            $data->dimensionType = $request->dimensions_type;
            $data->save();

            if ($files != null) {
                foreach ($files as $file) {
                    try {
                        $path = s3_fileUpload($file, 'graphic_project_attachment');
                    } catch (S3Exception $e) {
                        return redirect()->back()->with('error', $e->getMessage())->withInput();
                    }

                    $attach = new GraphicProjectAttachment;
                    $attach->graphic_project_id = $data->id;
                    $attach->file =  $path;
                    $attach->save();
                }
            }
            DB::commit();
        } catch (Exception $e) {
            DB::rollBack();
            return redirect()->back()->with('error', 'We apologize for any inconvenience, but we are currently unable to process your request due to a system error.')->withInput();
        } catch (QueryException $e) {
            DB::rollBack();
            return redirect()->back()->with('error', 'We apologize for any inconvenience, but we are currently unable to process your request due to a system error.')->withInput();
        }
        return redirect()->route('graphic.projects', ['id' => $plan->id])->with('success', 'Project Created Successfully,It Will Be Added into your Active List Soon.');
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        if ($project = GraphicProject::where('id', $id)->first()) {

            $chats = GraphicProjectChat::where('graphic_project_id', $project->id)->with('chatLead')->get();
            $chatsDateGrouped = $chats->groupBy(function ($post) {
                return date('d M Y', strtotime($post->ts));
            });
        } else {
            return redirect()->back()->with('warning', 'Invalid project ID');
        }
        return view("userpages.projects.graphic.Show", compact("project", 'chats', 'chatsDateGrouped'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $data = GraphicProject::find($id);
        return view("userpages.projects.graphic.Edit", compact("data"));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        $request->validate([
            "graphic_lead_brand_id" => "required|numeric",
            "lead_graphic_plan_id" => "required|numeric",
            "lead_id" => "required|numeric",
            "user_id" => "required|numeric",
            "graphic_project_category_id" => "required|numeric",
            "graphic_project_deliverable_id" => "required|numeric",
            "status" => 'required|boolean',
            "title" => 'required|string',
            "description" => 'required|string',
            "graphicsManagerRemark" => 'string|nullable',
            "customerRating" => 'boolean|nullable',
            "customerFeedback" => 'string|nullable',
            "dimensionHeight" => 'numeric|nullable',
            "dimensionWidth" => 'numeric|nullable',
            "dimensionType" => 'string|max:10|nullable',
        ]);
        $formdata = $request->all();
        $resultdata = GraphicProject::find($id);
        $result = $resultdata->update($formdata);
        if ($result) {
            return redirect()->back()->with("success", "data updated");
        } else {
            return redirect()->back()->with("error", "update failed");
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        $result = GraphicProject::find($id);
        $result->delete();
        return redirect()->back()->with("data deleted");
    }


    public function projectAttachments($project)
    {

        $images = GraphicProjectAttachment::where('graphic_project_id', $project)->with('project')->get();
        $project = GraphicProject::find($project);
        return view('userpages.projects.graphic.projectImages', compact('images', 'project'));
    }

    public function projcetChat(Request $request)
    {
        $prj_id = Crypt::decrypt($request->key);
        if (isset($request['file'])) {
            if ($files = $request->file('file')) {
                try {
                    $path = s3_fileUpload($files, 'graphic_project_attachment');
                } catch (S3Exception $e) {
                    return redirect()->back()->with('error', $e->getMessage())->withInput();
                }
                $attach = new GraphicProjectAttachment;
                $attach->graphic_project_id = $prj_id;
                $attach->by_lead_id = Auth::id();
                $attach->file = $path;
                $attach->save();
                $data = new GraphicProjectChat;
                $data->graphic_project_id = $prj_id;
                $data->graphic_project_attachment_id = $attach->id;
                $data->by_lead_id = Auth::id();
                $data->isApproved = 1;
                $data->save();
                $currentTime = date('H:i');
                return response()->json([
                    'message' => 'Success',
                    'text' => '<a href="' . url('graphic/project/image/' . $data->graphic_project_attachment_id) . '" target="_blank" ><div  style="max-width: 300px; "> <img src="' . s3_fileShow($attach->file, 'graphic_project_attachment') . '" alt="img"  class="img-fluid "></div></a>',
                    'imageUrl' => s3_fileShow($attach->file, 'graphic_project_attachment'),
                    'time' => $currentTime,
                    'lead_id' => $data->by_lead_id,
                    'sender' => auth()->user()->name,
                    'project_id' => $data->graphic_project_id,
                ]);
            }
        }
        if ($request->text != null) {

            $data = new GraphicProjectChat;
            $data->graphic_project_id = $prj_id;
            $data->by_lead_id = Auth::id();
            $data->isApproved = 1;
            $data->message = trim($request->text);
            $data->save();
            $currentTime = date('H:i');
            return response()->json(['message' => 'Success', 'time' => $currentTime, 'lead_id' => $data->by_lead_id, 'sender' => auth()->user()->name, 'project_id' => $data->graphic_project_id, 'text' => $data->message]);
        }
        return Response()->json([
            "success" => false,
            'message' => 'empty'
        ]);
    }


    public function image_view($id)
    {
        $comments = GraphicProjectChat::where('graphic_project_attachment_id', $id)->where('message', '!=', null)->where('isApproved', 1)->get();
        $image = GraphicProjectAttachment::find($id);
        $plan = ($image->project->plan);
        $totalProjects = $plan->projects->count();
        $count = $plan->noOfProjects - $totalProjects;
        return view('userpages.projects.graphic.imageview', compact('image', 'comments', 'count', 'id'));
    }

    public function markcomplete(Request $request)
    {
        $data = GraphicProject::find($request->project_id);
        $data->customerFeedback = $request->review;
        $data->customerRating = $request->rating ?? 5;
        $data->status = 6;
        $data->update();
        return redirect('/graphicProject/' . $request->project_id,)->with('success', 'your project marked Completed.');
    }

    public function sendcomment(Request $request)
    {

        try {
            $comment = new GraphicProjectChat;
            $comment->graphic_project_attachment_id = $request->imgid;
            $comment->message = $request->comment;
            $comment->isApproved = 1;
            // $comment->graphic_project_id = $request->project_id;
            $comment->save();
        } catch (QueryException $e) {
            return response()->json([
                "message" => "error",
                'div' => '<div class="card mb-3"><div class="d-flex justify-content-between"><div class="fs-4 px-3">You</div><div class="fw-light text-gray-600 pt-1 pe-5">Now</div></div><div class="separator my-1"></div><div class="card text-danger p-2">Message not Sent</div></div>'
            ]);
        } catch (\Throwable $e) {

            return response()->json([
                "message" => "error",
                'div' => '<div class="card mb-3"><div class="d-flex justify-content-between"><div class="fs-4 px-3">You</div><div class="fw-light text-gray-600 pt-1 pe-5">Now</div></div><div class="separator my-1"></div><div class="card text-danger p-2">Message not Sent</div></div>'
            ]);
        }

        return response()->json([
            "message" => "Success",
            'div' => ' <div class="d-flex justify-content-end mb-3"><div class="d-flex flex-column   align-items-end w-75 "><div class="fs-4 px-3"> ' . Auth::user()->name . '</div><div class=" bg-light-primary rounded-end-1 rounded-4 p-2 me-3 "><div class="">' .  $comment->message . '</div></div><div class="fw-light text-gray-600 pt-1 pe-5">Now</div></div></div>'
        ]);
    }
}
