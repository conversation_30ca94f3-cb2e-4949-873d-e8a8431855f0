<div id="toast-container" class="position-fixed end-0 p-3" style="z-index: 1500;top: 100px!important;">
    <?php
        $alertTypes = [
            'success' => ['icon' => 'fa-circle-check', 'color' => 'text-success', 'bg' => 'bg-success-subtle', 'title' => 'Success'],
            'warning' => ['icon' => 'fa-triangle-exclamation', 'color' => 'text-warning', 'bg' => 'bg-warning-subtle', 'title' => 'Warning'],
            'error' => ['icon' => 'fa-circle-xmark', 'color' => 'text-danger', 'bg' => 'bg-danger-subtle', 'title' => 'Error'],
            'info' => ['icon' => 'fa-circle-info', 'color' => 'text-info', 'bg' => 'bg-info-subtle', 'title' => 'Info'],
            'added_cart' => ['icon' => 'add-to-cart.png', 'color' => '', 'bg' => 'bg-primary-subtle', 'title' => 'Product Added', 'isImage' => true]
        ];
    ?>

    <?php $__currentLoopData = $alertTypes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $type => $alert): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <?php if(Session::has($type)): ?>
            <div class="toast show <?php echo e($alert['bg']); ?> border-0" role="alert" aria-live="assertive" aria-atomic="true">
                <div class="toast-header border-0 bg-transparent">
                    <div class="d-flex align-items-center me-auto">
                        <?php if(isset($alert['isImage']) && $alert['isImage']): ?>
                            <img src="<?php echo e(asset('media/icons/' . $alert['icon'])); ?>" alt="" class="me-2" style="width: 20px;">
                        <?php else: ?>
                            <i class="fa-regular <?php echo e($alert['icon']); ?> <?php echo e($alert['color']); ?> me-2"></i>
                        <?php endif; ?>
                        <strong class="<?php echo e($alert['color']); ?> "><?php echo e($alert['title']); ?></strong>
                    </div>
                    <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
                </div>
                <div class="fs-6 fw-normal toast-body">
                    <?php echo e(Session::get($type)); ?>

                </div>
            </div>
        <?php endif; ?>
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
</div>

<?php if(Session::has('success') ||
        Session::has('info') ||
        Session::has('warning') ||
        Session::has('error') ||
        Session::has('added_cart')): ?>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(function() {
                const toasts = document.querySelectorAll('.toast');
                toasts.forEach(toast => {
                    const bsToast = new bootstrap.Toast(toast);
                    bsToast.hide();
                });
            }, 10000);
        });
    </script>
<?php endif; ?>
<?php /**PATH C:\Users\<USER>\Desktop\live\websites_laravel\resources\views/components/core/message.blade.php ENDPATH**/ ?>