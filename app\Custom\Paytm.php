<?php // Code within app\Helpers\Helper.php

namespace App\Custom;

use Exception;
use App\Models\PaytmTxn;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Str;
use paytm\paytmchecksum\PaytmChecksum;

class Paytm
{
    public $paytmMKey;
    public $paytmMid;
    function __construct()
    {

        $hostData = hostData();
        $this->paytmMKey = $hostData->paytm_merchant_key;
        $this->paytmMid = $hostData->paytm_merchant_mid;
    }
    public function qr(int $amount, $lead, $invoice = null)
    {
        if ($amount <= 5) {
            return ['status' => false, 'message' => 'Amount is less then 5'];
        }
        $orderId = 'DF_' . time() . Str::random(3) . '_QR';
        $paytmParams['body'] = [
            'mid' => $this->paytmMid,
            'orderId' => $orderId,
            'amount' => $amount,
            'businessType' => 'UPI_QR_CODE',
            'posId' => 'S12_123',
            'orderDetails' => 'Payment for order',
            'displayName' => 'DUNES FACTORY',
        ];

        $checksum = PaytmChecksum::generateSignature(json_encode($paytmParams['body'], JSON_UNESCAPED_SLASHES), $this->paytmMKey); //hostData()->paytmMKey
        $paytmParams['head'] = [
            'clientId' => 'LEAD_' . $lead,
            'version' => 'v1',
            'signature' => $checksum,
        ];
        $post_data = json_encode($paytmParams, JSON_UNESCAPED_SLASHES);

        /* for Staging */
        // $url = "https://securegw-stage.paytm.in/paymentservices/qr/create";
        /* for Production */
        $url = 'https://secure.paytmpayments.com/paymentservices/qr/create';
        try {
            $ch = curl_init($url);
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $post_data);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
            $response = curl_exec($ch);
            $data = json_decode($response, true);
        } catch (Exception $e) {
            return ['status' => false, 'message' => 'Error while fetching data', 'qr' => null, 'devErr' => $data['body']['resultInfo']['resultMsg']];
        }
        if ($data == null || !key_exists('body', $data) || $data['body']['resultInfo']['resultStatus'] == 'FAILURE') {
            return ['status' => false, 'message' => 'Error while fetching data', 'qr' => null, 'devErr' => $data['body']['resultInfo']['resultMsg']];
        }
        $qrString = null;
        if (isset($data['body']['qrData'])) {
            $qrString = 'data:image/jpeg;base64,' . $data['body']['image'];
        } else {
            return ['status' => false, 'message' => 'System Error.', 'qr' => null];
        }
        if ($qrString != null) {
            try {
                $newpaytm_txn = new PaytmTxn();
                $newpaytm_txn->orderId = $orderId ?? null;
                $newpaytm_txn->qrCodeId = $data['body']['qrCodeId'] ?? null;
                $newpaytm_txn->lead_id = $lead ?? null;
                $newpaytm_txn->company_id = 1 ?? null;
                $newpaytm_txn->invoice_id = $invoice;
                $newpaytm_txn->response = $response;
                $newpaytm_txn->amount = $amount;
                $newpaytm_txn->save();
            } catch (Exception $e) {
                return ['status' => false, 'message' => 'System Error.', 'qr' => null];
            }
        }


        return ['status' => true, 'response' => $response, 'qr' => $qrString, 'orderIdQr' => $orderId];
    }
    public function gateway(int $amount, $lead, $invoice = null)
    {

        if ($amount <= 5) {
            return ['status' => false, 'message' => 'Amount is less then 5'];
            // return $e;
        }
        $orderId = 'DF_' . time() . Str::random(3) . '_GATE';
        try {
            $paytmKey = $this->paytmMKey;
            $paytmMid = $this->paytmMid;
            if ($invoice) {
                $callbackUrl = route('payment.captured', ['invoice_id' => Crypt::encryptString($invoice)]);
            } else {

                $callbackUrl = '/paytm/response';
            }
            $companyName = '';
            $paytmGW = [];
            $paytmGW['body'] = [
                'requestType' => 'Payment',
                'mid' => $paytmMid,
                'websiteName' => "$companyName",
                'orderId' => $orderId,
                'callbackUrl' => "$callbackUrl",
                'txnAmount' => [
                    'value' => $amount,
                    'currency' => 'INR',
                ],
                'userInfo' => [
                    'custId' => 'LEAD_' . $lead,
                ],
            ];
            $checksum2 = PaytmChecksum::generateSignature(json_encode($paytmGW['body'], JSON_UNESCAPED_SLASHES), $paytmKey);
            $paytmGW['head'] = [
                'signature' => $checksum2,
            ];

            $post_data = json_encode($paytmGW, JSON_UNESCAPED_SLASHES);

            /* for Staging */
            //$url2 = "https://securegw-stage.paytm.in/theia/api/v1/initiateTransaction?mid=" . hostData()->paytm_merchant_mid . "&orderId=" . $orderId;

            /* for Production */
            $url2 = 'https://secure.paytmpayments.com/theia/api/v1/initiateTransaction?mid=' . $paytmMid . '&orderId=' . $orderId;
            $ch = curl_init($url2);
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $post_data);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
            $response = curl_exec($ch);
            $response = json_decode($response, true);
        } catch (Exception $e) {
            return ['status' => false];
        }
        if ($response['body']['resultInfo']['resultStatus'] == 'F') {
            return ['status' => false, 'message' => 'Error while fetching data'];
        }

        if (array_key_exists('txnToken', $response['body'])) {

            try {
                $paytmTxnToken = $response['body']['txnToken'];
                $newpaytm_txn = new PaytmTxn();
                $newpaytm_txn->orderId = $orderId ?? null;
                $newpaytm_txn->company_id = 1 ?? null;
                $newpaytm_txn->lead_id = $lead ?? null;
                $newpaytm_txn->invoice_id = $invoice;
                $newpaytm_txn->amount = $amount;
                $newpaytm_txn->save();
            } catch (Exception $e) {
                return ['status' => false, 'message' => 'System Error.'];
            }
        } else {
            return ['status' => false, 'message' => 'Error while fetching data'];
        }


        $paytmTxnToken = $response['body']['txnToken'];
        return ['status' => true, 'response' => $response, 'paytmTxnToken' => $paytmTxnToken, 'orderIdGW' => $orderId];
    }

    public function verify($upi)
    {
        $mid = $this->paytmMid;
        $accData = $this->accessToken();
        $paytmParamsNew = array();

        $paytmParamsNew["body"] = array(
            "vpa" => $upi,
            "mid" => $mid
        );

        $paytmParamsNew["head"] = array(
            "tokenType" => "ACCESS",
            'token' => $accData['token']
        );

        $post_data = json_encode($paytmParamsNew, JSON_UNESCAPED_SLASHES);

        /* for Staging */
        // $url = "https://securegw-stage.paytm.in/theia/api/v1/vpa/validate?mid=$mid";

        /* for Production */
        $url = "https://securegw.paytm.in/theia/api/v1/vpa/validate?mid=" . $mid . "&referenceId=" . $accData['refId'];



        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $post_data);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
            "Content-Type: application/json"
        ));
        $response = curl_exec($ch);
        $upiResponce = json_decode($response, true);
        return $upiResponce['body'];
    }


    public function accessToken()
    {
        $mKey = $this->paytmMKey;
        $mid = $this->paytmMid;

        $refId = 'DF_Ref' . time();
        $paytmParams = array();

        $paytmParams["body"] = array(
            "mid" => $mid,
            "referenceId" => $refId,
        );
        /*
         * Generate checksum by parameters we have in body
         * Find your Merchant Key in your Paytm Dashboard at https://dashboard.paytm.com/next/apikeys
         */
        $checksum = PaytmChecksum::generateSignature(json_encode($paytmParams["body"], JSON_UNESCAPED_SLASHES), $mKey);

        $paytmParams["head"] = array(
            "tokenType" => "CHECKSUM",
            "token" => $checksum
        );

        $post_data = json_encode($paytmParams);
        /* for Staging */
        // $url = "https://securegw-stage.paytm.in/theia/api/v1/token/create?mid=$this->paytmMid&referenceId=$refId";

        /* for Production */
        // echo $mid;

        $url = "https://securegw.paytm.in/theia/api/v1/token/create?mid=$mid&referenceId=$refId";

        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $post_data);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array("Content-Type: application/json"));
        $response = curl_exec($ch);

        return [

            'token' => json_decode($response, true)['body']['accessToken'],
            'refId' => $refId,
            'mid' => $mid
        ];
    }
}
