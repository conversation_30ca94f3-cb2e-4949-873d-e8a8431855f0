@extends('userpages.layouts.projBrandMain')
@section('title', 'Brand')

@section('proj-pages')
    <div class="bg-white d-flex align-items-center justify-content-between gap-3 pt-2 pb-2 pe-3 ps-3 mb-3"
        style="border-radius: 7px;">
        <h4 class="heading-font">Graphic Brand</h4>
        <a aria-label="link" href="{{ route('brand.index') }}" class="btn text-white btn-sm rounded-pill"
            style="background-color: #194DAB;">
            View All
        </a>
    </div>
    <div class="card">
        <div class="card-header bg-white">
            <div class="d-flex justify-content-between">
                <div class="fs-5 ">
                    {{ $brand->name ?? 'N/A' }}
                </div>
                <div class="card-toolbar">
                    <form action="{{ route('brand.destroy', $brand->id) }}"
                        onsubmit="return confirm('Do you really want to delete Brand ?');" method="Post" id="delform">
                        @csrf
                        @method('DELETE')
                        <div class="d-flex gap-2">

                            <a aria-label="link" class="btn btn-primary btn-sm"
                                href="{{ route('brand.edit', Crypt::encryptString($brand->id ?? 'N/A')) }}">
                                <i class="bi bi-pen"></i>
                                Edit</a>
                            <button type="submit" id="delbtn" class="btn btn-danger btn-sm">
                                <i class="bi bi-trash3-fill"></i>
                                Delete</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <!--end::Details-->
        <div class="card-body">
            <div class="row mb-3 row-cols-lg-2 row-cols-1">
                <div class="col mb-3 pe-lg-6 pe-sm-3">
                    <div class="form-group">
                        <label for="" class="fs-5 form-label">Description of brand</label>
                        <div class="form-control h-75px overflow-x-scroll" readonly>
                            {!! trim($brand->description ?? 'N/A') !!}
                        </div>
                    </div>
                </div>

                <div class="col mb-3 ps-lg-6 ps-sm-3">
                    <div class="form-group">
                        <label for="" class="fs-5 form-label"> Brand guideline</label>
                        <div class="form-control h-75px overflow-x-scroll" readonly>
                            {!! trim($brand->guideline ?? 'N/A') !!}
                        </div>
                    </div>
                </div>
                <div class="col mb-3 pe-lg-6 pe-sm-3">
                    <div class="form-group">
                        <label for="" class="fs-5 form-label">Examples or refrence links</label>
                        <div readonly class="form-control h-50px overflow-x-scroll">{!! $brand->website ?? 'N/A' !!}</div>
                    </div>
                </div>
                <div class="col mb-3 ps-lg-6 ps-sm-3">
                    <div class="form-group">
                        <label for="" class="fs-5 form-label"> Brand colors guideline</label>
                        <div class="form-control h-75px overflow-x-scroll" readonly>
                            {{ trim($brand->colors ?? 'N/A') }}
                        </div>
                    </div>
                </div>
            </div>
            <div class="separator  "></div>
            <div class="w-100">
                <div class="card border-0 p-0">
                    <div class="card-header p-0 bg-white border-0">
                        <div class="card-title mb-0">
                            <h2 class="fs-2">Visual Inspiration</h2>
                        </div>
                    </div>
                    <div class="card-body p-2">
                        <div class="row row-cols-1 row-cols-lg-3 row-cols-md-2  ">
                            @if ($brand->logo)
                                <!--begin::Image-->
                                @php

                                    $info = pathinfo(s3_fileShow($brand->logo ?? '', 'graphic_lead_brand'));
                                    $ext = explode('?', $info['extension'])[0];
                                    $imgext = ['jpg', 'png', 'jpeg', 'gif'];
                                @endphp
                                @if (in_array($ext, $imgext))
                                    <div class=" col p-2">
                                        <div class=" card h-100 min-h-250px bg-gray-200 p-2 position-relative  ">
                                            <img class="img-fluid rounded-0 m-auto"
                                                src="{{ s3_fileShow($brand->logo, 'graphic_lead_brand') }}" alt="image"
                                                style="max-height: 240px;">

                                            <div class=" end-0 me-2 position-absolute">

                                                <div class="dropstart">
                                                    <button type="button"
                                                        class="btn btn-sm btn-secondary opacity-75 rounded-circle"
                                                        data-bs-toggle="dropdown" aria-expanded="false">
                                                        <i class="bi bi-three-dots"></i>
                                                    </button>
                                                    <ul class="dropdown-menu rounded-1 p-0 w-100 ">
                                                        <li class="p-0 m-0">
                                                            <a aria-label="link"
                                                                href="{{ s3_fileShow($brand->logo, 'graphic_lead_brand') }}"
                                                                download class="dropdown-item p-2">
                                                                <span class="menu-icon">
                                                                    <i class="bi bi-download"></i>
                                                                </span>
                                                                <span class="menu-title ">
                                                                    Download</span>
                                                            </a>
                                                        </li>
                                                    </ul>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                @endif
                            @endif
                            @if ($brand->attachments)
                                @foreach ($brand->attachments ?? 'N/A' as $item)
                                    @php

                                        $info = pathinfo(s3_fileShow($item->file, 'graphic_brand_attachment'));
                                        $ext = explode('?', $info['extension'])[0];
                                        $imgext = ['jpg', 'png', 'jpeg', 'gif'];
                                    @endphp
                                    <div class=" col p-2">
                                        <div class=" card mh-250px min-h-250px bg-gray-200 p-2 position-relative h-100">
                                            @if (in_array($ext, $imgext))
                                                <img src="{{ s3_fileShow($item->file, 'graphic_brand_attachment') }}"
                                                    alt="" class="img-fluid rounded-0 m-auto "
                                                    style="max-height: 240px">
                                            @else
                                                <a aria-label="link"
                                                    href="{{ s3_fileShow($item->file, 'graphic_brand_attachment') }}"
                                                    download class="fs-4 text-center m-auto">
                                                    <i class="bi bi-cloud-download d-block fs-2"></i>
                                                    Download {{ $ext }}
                                                </a>
                                            @endif
                                            <div class=" end-0 me-2 position-absolute">
                                                <!--begin::Menu-->
                                                <div class="dropstart">
                                                    <button type="button"
                                                        class="btn btn-sm btn-secondary opacity-75 rounded-circle"
                                                        data-bs-toggle="dropdown" aria-expanded="false">
                                                        <i class="bi bi-three-dots"></i>
                                                    </button>
                                                    <ul class="dropdown-menu rounded-1 w-100 p-0">
                                                        <li class="p-0 m-0">
                                                            <a aria-label="link"
                                                                href="{{ s3_fileShow($brand->logo, 'graphic_lead_brand') }}"
                                                                download class=" dropdown-item p-2 ">
                                                                <span class="menu-icon">
                                                                    <i class="bi bi-download text-primary"></i>
                                                                </span>
                                                                <span class="menu-title ">
                                                                    Download</span>
                                                            </a>

                                                        </li>
                                                        <li class="p-0 m-0">
                                                            <hr class="dropdown-divider m-0">
                                                        </li>
                                                        <li class="p-0 m-0"><a aria-label="link"
                                                                class="dropdown-item rounded">
                                                                <form id="deletForm"
                                                                    onsubmit="return confirm('Do you want to Delete attachent.')"
                                                                    action="{{ url('brand/attachment/delete/' . $item->id) }}"
                                                                    method="post">
                                                                    @csrf
                                                                    @method('delete')
                                                                    <button class="btn ps-0 w-100 text-start"
                                                                        type="submit">
                                                                        <span class="menu-icon">
                                                                            <span class="text-danger">
                                                                                <i class="bi bi-trash3"></i>
                                                                            </span>
                                                                            <span class="menu-title ">
                                                                                Delete
                                                                            </span>
                                                                        </span>
                                                                    </button>
                                                                </form>

                                                            </a></li>
                                                    </ul>
                                                </div>


                                                <!--end::Menu-->
                                            </div>
                                        </div>
                                    </div>
                                @endforeach
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
