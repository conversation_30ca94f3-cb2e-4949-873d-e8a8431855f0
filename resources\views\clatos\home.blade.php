@extends('components.clatos.main')

@section('webpage')
    {{-- Head Section Start --}}

    <section class="pt-lg-5  pt-md-5 pt-3 pb-4 pb-lg-0 mt-5 overflow-hidden theme-color-bg"
        style="background-color: #EDF0FE;background-image: url({{ asset('assets/clatos/images/home-bg.png') }});background-position: bottom left;background-repeat: no-repeat;">
        <div class="container-fluid pt-lg-5 pb-lg-5 pt-md-3 pb-md-3 pt-2">
            <div
                class="container-lg mt-4 mt-lg-0 d-flex align-items-center pt-lg-0 pt-3 flex-column flex-lg-row flex-res overflow-hidden">
                <div class="col-lg-6 pe-2">
                    <h1 class="mt-lg-4 mb-lg-4 mt-md-2 mb-md-2 mt-3 mb-3 home-heading" style="">Jack of
                        All Trades</h1>
                    <h1 class="lh-sm mt-lg-4 mt-md-2 mt-3 fw-bold sales-top-head">…and Sales</h1>
                    <hr class="red-line-hr mb-3">
                    <p class="lh-base fs-5 me-lg-5 mb-lg-4">An efficient Workspace that enables you to touch
                        new
                        horizon of Sales and master the art of Lead
                        management in most proficient way.</p>
                    @if (Auth::check())
                        @php
                            $content = 'rapbooster.plans.crmSoftware';
                        @endphp
                        <button class="blue-btn rounded-pill border-0 d-flex align-items-center pt-2 pb-2 pe-4 ps-4 gap-2"
                            data-bs-toggle="modal" data-bs-target="#crmSoftwarePlan">
                            Get started
                            <i class="fa-brands fa-telegram fa-2x" style="color: #FFF;"></i>
                        </button>
                    @else
                        <a aria-label="link" class="text-decoration-none" href="{{ route('login') }}">
                            <button
                                class="blue-btn rounded-pill border-0 d-flex align-items-center pt-2 pb-2 pe-4 ps-4 gap-2">
                                Get started
                                <i class="fa-brands fa-telegram fa-2x" style="color: #FFF;"></i>
                            </button>
                        </a>
                    @endif

                </div>
                <div class="col-lg-6 MT-3" data-aos="zoom-in" data-aos-duration="700" data-aos-easing="ease-in-sine">
                    <img class="img-fluid" src="{{ asset('assets/clatos/images/home-1.png') }}" alt="">
                </div>
            </div>
        </div>
    </section>
    <svg class="wave-head-svg" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 100" preserveAspectRatio="none">
        <path class="elementor-shape-fill" style="fill: #FFF;"
            d="M790.5,93.1c-59.3-5.3-116.8-18-192.6-50c-29.6-12.7-76.9-31-100.5-35.9c-23.6-4.9-52.6-7.8-75.5-5.3
                                                                    c-10.2,1.1-22.6,1.4-50.1,7.4c-27.2,6.3-58.2,16.6-79.4,24.7c-41.3,15.9-94.9,21.9-134,22.6C72,58.2,0,25.8,0,25.8V100h1000V65.3
                                                                    c0,0-51.5,19.4-106.2,25.7C839.5,97,814.1,95.2,790.5,93.1z" />
    </svg>

    {{-- Head Section End --}}

    {{-- Grid Section Start  --}}

    <div class="container-fluid overflow-hidden" style="margin-top: 5rem;">
        <div class="container-lg  overflow-hidden">
            <div class="text-center mb-5">
                <h1 class="home-sub-heading">Trusted by More Than 100,000+ Contractors</h1>
                <hr class="red-line-hr">
            </div>
            <div class="home-grid" style="margin-bottom: 8rem;">
                <div class="home-grid-items">
                    <img class="img-fluid" src="{{ asset('assets/clatos/images/home-logo-1.png') }}" alt="">
                </div>
                <div class="home-grid-items">
                    <img class="img-fluid" src="{{ asset('assets/clatos/images/home-logo-2.jpg') }}" alt="">
                </div>
                <div class="home-grid-items">
                    <img class="img-fluid" src="{{ asset('assets/clatos/images/home-logo-3.jpg') }}" alt="">
                </div>
                <div class="home-grid-items">
                    <img class="img-fluid" src="{{ asset('assets/clatos/images/home-logo-4.jpg') }}" alt="">
                </div>
                <div class="home-grid-items">
                    <img class="img-fluid" src="{{ asset('assets/clatos/images/home-logo-5.jpg') }}" alt="">
                </div>
                <div class="home-grid-items">
                    <img class="img-fluid" src="{{ asset('assets/clatos/images/home-logo-6.png') }}" alt="">
                </div>
            </div>
            <hr>
        </div>
    </div>

    {{-- Grid Section End  --}}

    {{-- Second Grid Section Start  --}}

    <div class="container-fluid overflow-hidden" style="margin-top: 5rem;">
        <div class="container-lg">
            <div class="text-center fw-bold">
                <h4 class="sales-sub-top-head">TOP REVIEW CATEGORY</h4>
                <h1 class="home-sub-heading">Versatalities Of Clatos</h1>
            </div>
            <div class="home-second-grid pb-5" style="margin-top: 2rem;">
                <div class="home-second-grid-items">
                    <img class="img-fluid" src="{{ asset('assets/clatos/images/home-logo-7.png') }}" alt="">
                    <h5 class="SG-heading">SALES</h5>
                </div>
                <div class="home-second-grid-items">
                    <img class="img-fluid" src="{{ asset('assets/clatos/images/home-logo-8.png') }}" alt="">
                    <h5 class="SG-heading">IT SECTOR</h5>
                </div>
                <div class="home-second-grid-items">
                    <img class="img-fluid" src="{{ asset('assets/clatos/images/home-logo-9.webp') }}" alt="">
                    <h5 class="SG-heading">INSURANCE</h5>
                </div>
                <div class="home-second-grid-items">
                    <img class="img-fluid" src="{{ asset('assets/clatos/images/home-logo-10.png') }}" alt="">
                    <h5 class="SG-heading">CONSULTING</h5>
                </div>
                <div class="home-second-grid-items">
                    <img class="img-fluid" src="{{ asset('assets/clatos/images/home-logo-11.png') }}" alt="">
                    <h5 class="SG-heading">MARKETING</h5>
                </div>
                <div class="home-second-grid-items">
                    <img class="img-fluid" src="{{ asset('assets/clatos/images/home-logo-12.jpg') }}" alt="">
                    <h5 class="SG-heading text-center mt-4">RETAIL INDUSTRY</h5>
                </div>
                <div class="hide-div"></div>
                <div class="home-second-grid-items">
                    <img class="img-fluid" src="{{ asset('assets/clatos/images/home-logo-13.png') }}" alt="">
                    <h5 class="SG-heading">BANKING</h5>
                </div>
                <div class="home-second-grid-items">
                    <img class="img-fluid" src="{{ asset('assets/clatos/images/home-logo-14.png') }}" alt="">
                    <h5 class="SG-heading">HOTELS</h5>
                </div>
                <div class="home-second-grid-items">
                    <img class="img-fluid" src="{{ asset('assets/clatos/images/home-logo-15.webp') }}" alt="">
                    <h5 class="SG-heading text-center">FINANCIAL SERVICES</h5>
                </div>
                <div class="home-second-grid-items">
                    <img class="img-fluid" src="{{ asset('assets/clatos/images/home-logo-16.jpg') }}" alt="">
                    <h5 class="SG-heading text-center">HEALTH SECTOR</h5>
                </div>
                <div class="hide-div"></div>
            </div>
            <div class="d-flex justify-content-center mb-5" style="margin: 0 auto;">
                @if (Auth::check())
                    <button class="blue-btn rounded-pill border-0 d-flex align-items-center pt-2 pb-2 pe-4 ps-4 gap-2"
                        data-bs-toggle="modal" data-bs-target="#crmSoftwarePlan">
                        Get started
                        <i class="fa-brands fa-telegram fa-2x" style="color: #FFF;"></i>
                    </button>
                @else
                    <a aria-label="link" class="text-decoration-none" href="{{ route('login') }}">
                        <button class="blue-btn rounded-pill border-0 d-flex align-items-center pt-2 pb-2 pe-4 ps-4 gap-2">
                            Get started
                            <i class="fa-brands fa-telegram fa-2x" style="color: #FFF;"></i>
                        </button>
                    </a>
                @endif
            </div>
        </div>
    </div>

    {{-- Second Grid Section End  --}}

    {{-- RFCE Section Start --}}

    <section id="svg-section" class="pt-lg-5 pt-md-5 pt-3 pb-4 pb-lg-0 mt-5 whole-bg">
        <div class="container-fluid pt-lg-5 pb-lg-5 pt-md-3 pb-md-3 pt-2">
            <div class="container-lg">
                <div
                    class="mt-4 mt-lg-0 d-flex gap-lg-5 gap-md-4 gap-4 align-items-center pt-lg-0 pt-md-0 pt-3 flex-column flex-md-row flex-lg-row flex-res timeline-bg">
                    <div class="col-lg-6 col-md-6" data-aos="zoom-in" data-aos-duration="700"
                        data-aos-easing="ease-in-sine">
                        <h1 class="home-sub-heading">Reason for Clatos Excellence</h1>
                        <p>Clatos goes one step further so you can free up time for more valuable
                            activities. Thanks to its Built-in intelligence. Have a crystal clear and perfect
                            view of dealings on Clatos.</p>
                        <img class="img-fluid" src="{{ asset('assets/clatos/images/home-2.webp') }}" alt="">
                    </div>
                    <div class="col-lg-6 col-md-6 pe-2">
                        <div class="animation-container">
                            <div class="row no-gutters align-items-center justify-content-around timeline-nodes ">
                                <div class="col-lg-2 col-md-2 px-lg-3 mt-lg-3 order-2 timeline-image text-center">
                                    <svg class="rh-svg-shape" stroke-width="4" stroke="#296BEF" fill="transparent"
                                        viewBox="0 0 79 79">
                                        <path d="M24.000,39.000 L36.000,49.000 L56.000,30.000"
                                            style="stroke-dashoffset: 170; stroke-dasharray: 170;" stroke-dashoffset="0">
                                            <animate attributeName="stroke-dashoffset" from="170" to="0"
                                                dur="2s" begin="0s" id="yourAnimationId" fill="freeze" />
                                        </path>
                                        <path
                                            d="M38.000,6.000 C55.673,6.000 70.000,20.327 70.000,38.000 C70.000,55.673 55.673,70.000 38.000,70.000 C20.327,70.000 6.000,55.673 6.000,38.000 C6.000,20.327 20.327,6.000 38.000,6.000 Z"
                                            style="stroke-dashoffset: 384; stroke-dasharray: 384;" stroke-dashoffset="0">
                                            <animate attributeName="stroke-dashoffset" from="384" to="0"
                                                dur="2s" begin="0.2s" id="yourAnimationId" fill="freeze" />
                                        </path>
                                    </svg>
                                </div>
                                <div class="col-lg-10 col-md-10 order-3 mt-5 align-self-center shape-margin"
                                    style="background: ##E6F5FF;">
                                    <h4 class="fw-medium">Multi-dimensional Integration</h4>
                                    <p class="para1 lh-base pl-0">Integration with major communication platforms,
                                        Clatos
                                        provides strong visibility to customers.</p>
                                </div>
                            </div>
                            <div class="row no-gutters justify-content-around align-items-center timeline-nodes ">
                                <div class="col-lg-2 col-md-2 px-lg-3 mt-lg-3 order-2 timeline-image text-center">
                                    <svg class="rh-svg-shape" stroke-width="4" stroke="#296BEF" fill="transparent"
                                        viewBox="0 0 79 79">
                                        <path d="M24.000,39.000 L36.000,49.000 L56.000,30.000"
                                            style="stroke-dashoffset: 170; stroke-dasharray: 170;" stroke-dashoffset="0">
                                            <animate attributeName="stroke-dashoffset" from="170" to="0"
                                                dur="2s" begin="0s" id="yourAnimationId" fill="freeze" />
                                        </path>
                                        <path
                                            d="M38.000,6.000 C55.673,6.000 70.000,20.327 70.000,38.000 C70.000,55.673 55.673,70.000 38.000,70.000 C20.327,70.000 6.000,55.673 6.000,38.000 C6.000,20.327 20.327,6.000 38.000,6.000 Z"
                                            style="stroke-dashoffset: 384; stroke-dasharray: 384;" stroke-dashoffset="0">
                                            <animate attributeName="stroke-dashoffset" from="384" to="0"
                                                dur="2s" begin="0.2s" id="yourAnimationId" fill="freeze" />
                                        </path>
                                    </svg>
                                </div>
                                <div class="col-lg-10 col-md-10 order-3 mt-5 align-self-center shape-margin"
                                    style="background: ##E6F5FF;">
                                    <h4 class="fw-medium">Quick Invoicing</h4>
                                    <p class="para1 lh-base pl-0">Create invoice there and then as you close the
                                        deal.
                                    </p>
                                </div>
                            </div>
                            <div class="row no-gutters justify-content-around align-items-center timeline-nodes ">
                                <div class="col-lg-2  col-md-2 px-lg-3 mt-lg-3 order-2 timeline-image text-center ">
                                    <svg class="rh-svg-shape" stroke-width="4" stroke="#296BEF" fill="transparent"
                                        viewBox="0 0 79 79">
                                        <path d="M24.000,39.000 L36.000,49.000 L56.000,30.000"
                                            style="stroke-dashoffset: 170; stroke-dasharray: 170;" stroke-dashoffset="0">
                                            <animate attributeName="stroke-dashoffset" from="170" to="0"
                                                dur="2s" begin="0s" id="yourAnimationId" fill="freeze" />
                                        </path>
                                        <path
                                            d="M38.000,6.000 C55.673,6.000 70.000,20.327 70.000,38.000 C70.000,55.673 55.673,70.000 38.000,70.000 C20.327,70.000 6.000,55.673 6.000,38.000 C6.000,20.327 20.327,6.000 38.000,6.000 Z"
                                            style="stroke-dashoffset: 384; stroke-dasharray: 384;" stroke-dashoffset="0">
                                            <animate attributeName="stroke-dashoffset" from="384" to="0"
                                                dur="2s" begin="0.2s" id="yourAnimationId" fill="freeze" />
                                        </path>
                                    </svg>
                                </div>
                                <div class="col-lg-10 col-md-10 order-3 mt-5 align-self-center shape-margin"
                                    style="background: ##E6F5FF;">
                                    <h4 class="fw-medium">Streamlined SOP</h4>
                                    <p class="para1 lh-base pl-0 ">Enlisting your leads to making invoices and
                                        taking
                                        approvals, we won’t let you skip a thing.</p>
                                </div>
                            </div>

                            <div class="row no-gutters justify-content-around align-items-center  timeline-nodes ">
                                <div class="col-lg-2  col-md-2 px-lg-3 mt-lg-3 order-2 timeline-image text-center ">
                                    <svg class="rh-svg-shape" stroke-width="4" stroke="#296BEF" fill="transparent"
                                        viewBox="0 0 79 79">
                                        <path d="M24.000,39.000 L36.000,49.000 L56.000,30.000"
                                            style="stroke-dashoffset: 170; stroke-dasharray: 170;" stroke-dashoffset="0">
                                            <animate attributeName="stroke-dashoffset" from="170" to="0"
                                                dur="2s" begin="0s" id="yourAnimationId" fill="freeze" />
                                        </path>
                                        <path
                                            d="M38.000,6.000 C55.673,6.000 70.000,20.327 70.000,38.000 C70.000,55.673 55.673,70.000 38.000,70.000 C20.327,70.000 6.000,55.673 6.000,38.000 C6.000,20.327 20.327,6.000 38.000,6.000 Z"
                                            style="stroke-dashoffset: 384; stroke-dasharray: 384;" stroke-dashoffset="0">
                                            <animate attributeName="stroke-dashoffset" from="384" to="0"
                                                dur="2s" begin="0.2s" id="yourAnimationId" fill="freeze" />
                                        </path>
                                    </svg>
                                </div>

                                <div class="col-lg-10 col-md-10 order-3 mt-5 align-self-center shape-margin"
                                    style="background: ##E6F5FF;">
                                    <h4 class="fw-medium">Unified Call Front</h4>
                                    <p class="para1 lh-base pl-0 ">Inbound and outbound calls with one id for your
                                        entire
                                        workspace</p>
                                </div>
                            </div>
                            <div class="row no-gutters justify-content-around align-items-center  timeline-nodes ">
                                <div class="col-lg-2 col-md-2 px-lg-3 mt-lg-3 order-2 timeline-image text-center ">
                                    <svg class="rh-svg-shape" stroke-width="4" stroke="#296BEF" fill="transparent"
                                        viewBox="0 0 79 79">
                                        <path d="M24.000,39.000 L36.000,49.000 L56.000,30.000"
                                            style="stroke-dashoffset: 170; stroke-dasharray: 170;" stroke-dashoffset="0">
                                            <animate attributeName="stroke-dashoffset" from="170" to="0"
                                                dur="2s" begin="0s" id="yourAnimationId" fill="freeze" />
                                        </path>
                                        <path
                                            d="M38.000,6.000 C55.673,6.000 70.000,20.327 70.000,38.000 C70.000,55.673 55.673,70.000 38.000,70.000 C20.327,70.000 6.000,55.673 6.000,38.000 C6.000,20.327 20.327,6.000 38.000,6.000 Z"
                                            style="stroke-dashoffset: 384; stroke-dasharray: 384;" stroke-dashoffset="0">
                                            <animate attributeName="stroke-dashoffset" from="384" to="0"
                                                dur="2s" begin="0.2s" id="yourAnimationId" fill="freeze" />
                                        </path>
                                    </svg>
                                </div>

                                <div class="col-lg-10 col-md-10 order-3 mt-5 align-self-center shape-margin"
                                    style="background: ##E6F5FF;">
                                    <h4 class="fw-medium">Numerous Payment Partners</h4>
                                    <p class="para1 lh-base pl-0 ">A Built-in intelligent set up for all your
                                        transactions.
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <div class="container-fluid pt-lg-5 pb-lg-5 pt-md-3 pb-md-3 pt-2 whole-bg overflow-hidden">
        <div class="container-lg">
            <div class="d-flex gap-lg-5 gap-md-4 gap-4 align-items-center pt-lg-0 pt-md-0 pt-3 flex-column flex-md-row flex-lg-row flex-res"
                style="margin-top: 5rem;">
                <div class="col-lg-6 col-md-6 pe-2">
                    <h1 class="home-sub-heading">Be Everywhere from Anywhere</h1>
                    <div class="d-flex gap-4 align-items-center mt-5"><i class="fa-solid fa-check fa-2xl fa-bold"
                            style="color: #296BEF;"></i>Monitor and
                        Manage Daily Official Tasks.</div>
                    <div class="d-flex gap-4 align-items-center mt-5"><i class="fa-solid fa-check fa-2xl fa-bold"
                            style="color: #296BEF;"></i>Multiple
                        Platform Integration, An Ease That Only Smart
                        Platform Can Provide.</div>
                    <div class="d-flex gap-4 align-items-center mt-5"><i class="fa-solid fa-check fa-2xl fa-bold"
                            style="color: #296BEF;"></i>Obtain
                        Complete Asset Visibility For a Practical,
                        All-Encompassing Service Solution</div>
                </div>
                <div class="col-lg-6 col-md-6" data-aos="zoom-in" data-aos-duration="700"
                    data-aos-easing="ease-in-sine">
                    <img class="img-fluid" src="{{ asset('assets/clatos/images/home-3.webp') }}" alt="">
                </div>
            </div>
        </div>
    </div>
    <div class="container-fluid pt-lg-5 pb-lg-5 overflow-hidden"
        style="background-color: transparent;
                background-image: linear-gradient(180deg,#ECF6FE 0%,#FFFFFFDE 100%);">
        <div class="container-lg">
            <div class="text-center" style="margin-top: 5rem; margin-bottom: 5rem;">
                <h1 class="home-sub-heading">Sales At First Sight!</h1>
                <p>We persistently provide a dynamic workspace in your expedition of linking your sales process
                    to
                    the
                    customer's buying process.</p>
            </div>
            <div class="mt-4 mt-lg-0 d-flex gap-lg-5 gap-md-4 gap-4 pt-lg-0 pt-md-0 pt-3 flex-column flex-lg-row flex-res">
                <div class="col-lg-6 pe-2" data-aos="zoom-in" data-aos-duration="700" data-aos-easing="ease-in-sine">
                    <img class="img-fluid" src="{{ asset('assets/clatos/images/home-4.png') }}" alt="">
                </div>
                <div class="col-lg-6 pe-2">
                    <ul class="nav nav-tabs flex-nav d-flex justify-content-around" id="myTab" role="tablist">
                        <li class="nav-item list-item list-item-width" role="presentation">
                            <button class="nav-link active" id="Keywords-tab" data-bs-toggle="tab"
                                data-bs-target="#Keywords" type="button" role="tab" aria-controls="Keywords"
                                aria-selected="true">Sales Supremacy</button>
                        </li>
                        <li class="nav-item list-item list-item-width" role="presentation">
                            <button class="nav-link" id="Bids-tab" data-bs-toggle="tab" data-bs-target="#Bids"
                                type="button" role="tab" aria-controls="Bids" aria-selected="false">Mastered
                                Monitoring</button>
                        </li>
                        <li class="nav-item list-item list-item-width" role="presentation">
                            <button class="nav-link" id="Ads-tab" data-bs-toggle="tab" data-bs-target="#Ads"
                                type="button" role="tab" aria-controls="Ads" aria-selected="false">Intelligent
                                Integration</button>
                        </li>
                    </ul>
                    <div class="tab-content" style="margin-top: 2rem;">
                        <div class="tab-pane show active" id="Keywords" role="tabpanel" aria-labelledby="Keywords-tab">
                            <p>Do everything you can to be effective, from acquiring new clients to keeping
                                existing
                                ones. Effortlessly Route, Prioritize, and Address Support Tickets. Additionally,
                                if
                                they're organized, you may handle several conversations at once and maintain
                                tabs on
                                what each client wants.</p>
                            <div class="max-width-btn">
                                @if (Auth::check())
                                    <button
                                        class="blue-btn rounded-pill border-0 d-flex align-items-center pt-2 pb-2 pe-4 ps-4 gap-2"
                                        data-bs-toggle="modal" data-bs-target="#crmSoftwarePlan">
                                        Get started
                                        <i class="fa-brands fa-telegram fa-2x" style="color: #FFF;"></i>
                                    </button>
                                @else
                                    <a aria-label="link" class="text-decoration-none" href="{{ route('login') }}">
                                        <button
                                            class="blue-btn rounded-pill border-0 d-flex align-items-center pt-2 pb-2 pe-4 ps-4 gap-2">
                                            Get started
                                            <i class="fa-brands fa-telegram fa-2x" style="color: #FFF;"></i>
                                        </button>
                                    </a>
                                @endif
                            </div>
                        </div>
                        <div class="tab-pane" id="Bids" role="tabpanel" aria-labelledby="Bids-tab">
                            <p>Clatos enables organizations to manage quality data by involving features like
                                contact details masking, aggregated metrics, and to establish compliance with
                                policies.
                                Monitoring and steering data facilitates quality improvement.</p>
                            <div class="max-width-btn">
                                @if (Auth::check())
                                    <button
                                        class="blue-btn rounded-pill border-0 d-flex align-items-center pt-2 pb-2 pe-4 ps-4 gap-2"
                                        data-bs-toggle="modal" data-bs-target="#crmSoftwarePlan">
                                        Get started
                                        <i class="fa-brands fa-telegram fa-2x" style="color: #FFF;"></i>
                                    </button>
                                @else
                                    <a aria-label="link" class="text-decoration-none" href="{{ route('login') }}">
                                        <button
                                            class="blue-btn rounded-pill border-0 d-flex align-items-center pt-2 pb-2 pe-4 ps-4 gap-2">
                                            Get started
                                            <i class="fa-brands fa-telegram fa-2x" style="color: #FFF;"></i>
                                        </button>
                                    </a>
                                @endif
                            </div>
                        </div>
                        <div class="tab-pane" id="Ads" role="tabpanel" aria-labelledby="Ads-tab">
                            <p>Multiple channel Integration can consolidate your customer relationship efforts
                                into
                                one channel which provide better customer service, boost customer retention,
                                drive
                                sales, and ultimately grow your business.</p>
                            <div class="max-width-btn">
                                @if (Auth::check())
                                    <button
                                        class="blue-btn rounded-pill border-0 d-flex align-items-center pt-2 pb-2 pe-4 ps-4 gap-2"
                                        data-bs-toggle="modal" data-bs-target="#crmSoftwarePlan">
                                        Get started
                                        <i class="fa-brands fa-telegram fa-2x" style="color: #FFF;"></i>
                                    </button>
                                @else
                                    <a aria-label="link" class="text-decoration-none" href="{{ route('login') }}">
                                        <button
                                            class="blue-btn rounded-pill border-0 d-flex align-items-center pt-2 pb-2 pe-4 ps-4 gap-2">
                                            Get started
                                            <i class="fa-brands fa-telegram fa-2x" style="color: #FFF;"></i>
                                        </button>
                                    </a>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    {{-- RFCE Section End --}}

    {{-- First Section Start  --}}

    <div class="container-fluid overflow-hidden" style="margin-top: 5rem;">
        <div class="container-lg">
            <div class="row d-flex align-items-center">
                <div class="col-lg-6 col-md-6">
                    <img class="img-fluid MB-3" src="{{ asset('assets/clatos/images/home-5.png') }}" alt="">
                </div>
                <div class="col-lg-6 col-md-6">
                    <h1 class="home-sub-heading">You won't have to miss a thing because we have everything at your
                        fingertips!
                    </h1>
                    <div class="d-flex gap-4 align-items-center mt-5"><i class="fa-solid fa-check fa-2xl fa-bold"
                            style="color: #296BEF;"></i>Analyses of
                        real-time performance</div>
                    <div class="d-flex gap-4 align-items-center mt-5"><i class="fa-solid fa-check fa-2xl fa-bold"
                            style="color: #296BEF;"></i>Follow-Up/Callback Alerts</div>
                    <div class="d-flex gap-4 align-items-center mt-5"><i class="fa-solid fa-check fa-2xl fa-bold"
                            style="color: #296BEF;"></i>Overview of the Interaction History</div>
                </div>
            </div>
        </div>
    </div>

    {{-- First Section End  --}}

    {{-- Testimonial Section Start  --}}

    <div class="container-fluid overflow-hidden" style="margin-top: 5rem; margin-bottom: 5rem;">
        <div class="container-lg timeline-bg">
            <div class="text-center mb-5">
                <h4 class="sales-sub-top-head">CUSTOMER TESTIMONIAL</h4>
                <h3 class="home-sub-heading">Our happy customer's reviews</h3>
                <hr class="red-line-hr">
            </div>
            <div class="testi-grid">
                <div class="testi-grid-items">
                    <img class="img-fluid" src="{{ asset('assets/clatos/images/testi-6.png') }}" alt="">
                    <div class="testi-grid-content d-flex flex-column justify-content-between h-75">
                        <div class="testi-grid-inside">
                            <p>Clatos works flawlessly and is very easy to use. It gives you the effective result of your
                                inputs & A user-friendly interface that makes it easy to get started and use.</p>
                            <div class="">
                                <h4 class="testi-name">Ankita Sharma</h4>
                                <h6 class="testi-job">Login Radius</h6>
                            </div>
                        </div>
                        <i class="fa-solid fa-quote-left d-flex justify-content-end"
                            style="color: #247DFF; font-size: 4em;"></i>
                    </div>
                </div>
                <div class="testi-grid-items">
                    <img class="img-fluid" src="{{ asset('assets/clatos/images/testi-7.png') }}" alt="">
                    <div class="testi-grid-content d-flex flex-column justify-content-between h-75">
                        <div class="testi-grid-inside">
                            <p>Works like a charm. I love the ease of the software and would highly recommend purchasing
                                their yearly plan to create a perfect selling opportunity.</p>
                            <div class="">
                                <h4 class="testi-name">Neha Swami</h4>
                                <h6 class="testi-job">Firstcry</h6>
                            </div>
                        </div>
                        <i class="fa-solid fa-quote-left d-flex justify-content-end"
                            style="color: #247DFF; font-size: 4em;"></i>
                    </div>
                </div>
                <div class="testi-grid-items">
                    <img class="img-fluid" src="{{ asset('assets/clatos/images/testi-8.png') }}" alt="">
                    <div class="testi-grid-content d-flex flex-column justify-content-between h-75">
                        <div class="testi-grid-inside">
                            <p>I like how easy Clatos is to use. I like that you can quickly change things, but keep your
                                list. If I wanted to change my pop-up or change my broadcasts.</p>
                            <div class="">
                                <h4 class="testi-name">Pooja Gupta</h4>
                                <h6 class="testi-job">Lapinoz</h6>
                            </div>
                        </div>
                        <i class="fa-solid fa-quote-left d-flex justify-content-end"
                            style="color: #247DFF; font-size: 4em;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    {{-- Testimonial Section End  --}}

    {{-- Ready to see Section Start --}}

    <div class="container-fluid center-image-home overflow-hidden"
        style="background-image: url({{ asset('assets/clatos/images/home-6.jpg') }});background-repeat: no-repeat;background-size: cover;">
        <div class="container-lg text-center RTS-flex" style="padding-bottom: 7rem;padding-top: 7rem;">
            <h1 class="mb-4 fw-bold" style="color: #262578">Ready to see<br>Review it in action?</h1>
            <p class="mb-4 RTS-para">Know the exhilarating features of Clatos</p>
            <div class="max-width-btn">
                @if (Auth::check())
                    <button class="blue-btn rounded-pill border-0 d-flex align-items-center pt-2 pb-2 pe-4 ps-4 gap-2"
                        data-bs-toggle="modal" data-bs-target="#crmSoftwarePlan">
                        Get started
                        <i class="fa-brands fa-telegram fa-2x" style="color: #FFF;"></i>
                    </button>
                @else
                    <a aria-label="link" class="text-decoration-none" href="{{ route('login') }}">
                        <button class="blue-btn rounded-pill border-0 d-flex align-items-center pt-2 pb-2 pe-4 ps-4 gap-2">
                            Get started
                            <i class="fa-brands fa-telegram fa-2x" style="color: #FFF;"></i>
                        </button>
                    </a>
                @endif
            </div>
        </div>
    </div>

    {{-- Ready to see Section End --}}

    {{-- Blog Section Start --}}

    {{-- <div class="container-fluid" style="margin-top: 5rem;">
        <div class="container-lg">
            <div class="blog-grid">
                <div class="blog-head">
                    <h4 class="sales-sub-top-head">STAY TURNED ON</h4>
                    <h2 class="mb-4 home-sub-heading">Check our Latest News
                        from Our Blog</h2>
                    <a aria-label="link" class="text-decoration-none" href="{{ route('clatos.blog') }}">
<button class="blue-btn rounded-pill border-0 d-flex align-items-center pt-2 pb-2 pe-4 ps-4 gap-2">
    Read More
    <i class="fa-brands fa-telegram fa-2x" style="color: #FFF;"></i>
</button>
</a>
</div>
<a aria-label="link" href="{{ route('clatos.blog') }}" class="text-decoration-none">
    <div class="blog-grid-items">
        <div class="blog-grid-inside">
            <p class="blog-p">TECHNOLOGIES</p>
            <h3 class="blog-heading">Gutencon Review with <br>Autocontents</h3>
            <p class="blog-color-p">This is Gutenberg Autocontents layout. It generates autocontents in
                floating panel when<br>...
            </p>
            <i class="fa-solid fa-arrow-right blog-rotation"></i>
        </div>
    </div>
</a>
<a aria-label="link" href="{{ route('clatos.blog') }}" class="text-decoration-none">
    <div class="blog-grid-items">
        <div class="blog-grid-inside">
            <p class="blog-p">CAMERAS</p>
            <h3 class="blog-heading">Post with GutenSlider</h3>
            <p class="blog-color-p" style="margin-top: 1.8rem;">Tincidunt nunc pulvinar
                sapien et. Eu consequat ac felis donec et pellentesque. ...</p>
            <i class="fa-solid fa-arrow-right blog-rotation" style="margin-top: 1.8rem;"></i>
        </div>
    </div>
</a>
</div>
</div>
</div> --}}

    {{-- Blog Section End --}}

    {{-- FAQ Section Start --}}

    @include('components.clatos.FAQ')

    {{-- FAQ Section End --}}

    {{-- Support of People Start --}}

    <div class="container-fluid overflow-hidden home-PT">
        <div class="container-lg">
            <div class="text-center">
                <h1 class="home-sub-heading">Supported by real people</h1>
                <p>Our team of expert developers works to make your work simpler and provide support across the zones.</p>
            </div>
        </div>
    </div>
    <div class="container-fluid overflow-hidden">
        <div class="container-lg"
            style="background-image: url({{ asset('assets/clatos/images/SP-bg.png') }});background-repeat: no-repeat;background-size:cover;background-position: center;">
            <div class="image-container" style="position:relative">
                <svg id="svg" viewBox="0 0 989 103" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M1 45.5L230.5 101.5L478 1.5L709 73.5L988 1.5" stroke="#E2E2E2" stroke-width="2"
                        stroke-linecap="round" stroke-linejoin="round"
                        style="stroke-dashoffset: 0; stroke-dasharray: none;"></path>
                </svg>
                <div data-aos="zoom-in" data-aos-duration="700" data-aos-easing="ease-in-sine">
                    <img class="img-fluid SP-img-1" src="{{ asset('assets/clatos/images/testi-1.png') }}"
                        alt="Image 1">
                    <img class="img-fluid SP-img-2" src="{{ asset('assets/clatos/images/testi-2.png') }}"
                        alt="Image 2">
                    <img class="img-fluid SP-img-3" src="{{ asset('assets/clatos/images/testi-3.png') }}"
                        alt="Image 3">
                    <img class="img-fluid SP-img-4" src="{{ asset('assets/clatos/images/testi-4.png') }}"
                        alt="Image 4">
                    <img class="img-fluid SP-img-5" src="{{ asset('assets/clatos/images/testi-5.png') }}"
                        alt="Image 5">
                </div>
            </div>
        </div>
    </div>

    {{-- Support of People End --}}

    <div class="container-fluid" style="margin-top: 3rem;margin-bottom: 5rem;">
        <div class="container-lg center-image"
            style="border-radius:30px;padding-top: 6rem;padding-bottom:6rem;background-image: url({{ asset('assets/clatos/images/home-last-bg.png') }});background-repeat: no-repeat;background-size: cover;">
            <div class="text-center text-white fw-bold">
                <p>CLATOS CRM</p>
                <h1 class="CRM-heading">Download the Application Now</h1>
            </div>
            <div class="d-flex justify-content-center gap-5 mt-4 CRM-flex">
                <a href="https://play.google.com/store/apps/details?id=crm.clatos.app">
                    <img class="home-download-app" src="{{ asset('assets/clatos/images/home-google-play.png') }}"
                        alt="app link">
                </a>
                {{-- <img class="home-download-app" src="{{ asset('assets/clatos/images/home-app-store.png') }}"
            alt="image"> --}}
            </div>
        </div>
    </div>
@endsection
