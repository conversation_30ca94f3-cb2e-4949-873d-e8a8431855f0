@extends('userpages.main')
@section('title', 'Subscriptions')

@section('userpagesection')

<div class=" ms-lg-2 m-0">
    <div class="bg-white pt-2 pb-2 pe-3 ps-3 mb-3" style="border-radius: 7px;">
        <h4 class="heading-font">Subscriptions</h4>
    </div>
    
    <div class="overflow-auto change-scroll" style="border-radius: 7px; height: 62vh;">
        <div class="licence-grid">
            @foreach ($data as $licenceDetails)

            <div class="licence-grid-items p-2">
                <div class="d-flex justify-content-between licence-label position-relative">
                    <div class="d-flex align-items-center gap-2">
                        <h6 class="m-0">Software: </h6>
                        <p class="m-0 gray-dark-text">{{ $licenceDetails->nameOnSoftware }}</p>
                    </div>
                    <div>
                        <input type="hidden" name="id" value="{{ $licenceDetails }}">
                    </div>

                </div>
                <div class="d-flex align-items-center gap-2">
                    <h6 class="m-0 ">License key: </h6>
                    <p class="m-0 key gray-dark-text">{{ $licenceDetails->key ?? ' - ' }}</p>
                    <button class="btn p-0 licencecopybtn" data-bs-toggle="tooltip" data-bs-title="Copied" data-bs-trigger="focus" data-key="{{ $licenceDetails->key }}">
                        <div class="pe-1 ps-1" style="background-color: #E2E2E2; border-radius: 5px;">
                            <i class="fa-solid fa-copy text-secondary"></i>
                        </div>
                    </button>
                </div>
                <div class="version-control">
                    <div class="d-flex align-items-center">
                        <h6 class="m-0">Your version: </h6>
                        <p class="m-0 gray-dark-text">
                            &nbsp;
                            {{ $licenceDetails->product->version }}
                        </p>
                    </div>
                    <div class="d-flex align-items-center gap-2">
                        <div class="d-flex align-items-center">
                            <h6 class="m-0">Latest version: </h6>
                            <p class="m-0 gray-dark-text"> {{ $licenceDetails['product']->version }}</p>
                        </div>
                        <div class="pe-1 ps-1 " style="background-color: #E2E2E2; border-radius: 5px;">
                            <a href=" {{ $licenceDetails->product->downloadLink }}" download target="blank">
                                <i class="fa-solid fa-download text-secondary"></i>
                            </a>
                        </div>
                    </div>
                </div>
                <div class="d-flex align-items-center gap-2">
                    <h6 class="m-0">Remarks: </h6>
                    <div class="editable text-break" data-id="{{ $licenceDetails->id }}" data-column="remarks" style="text-decoration:underline dotted #0078E3 2px; color: #0078E3;">
                        {{ $licenceDetails->remarks }}
                    </div>
                </div>
                <div class="d-flex justify-content-between licence-label ">
                    <div class="d-flex align-items-center gap-2 text-danger">
                        <h6 class="m-0">{{ $licenceDetails->lead_id }} Expiry: </h6>
                        <p class="m-0 text-danger"> {{ $licenceDetails->expiryDateTime!==null? humanDate($licenceDetails->expiryDateTime) :'N/A' }}</p>
                    </div>
                </div>
                <hr>
                <div class="d-flex align-items-center gap-2">

                    @if ($licenceDetails->pendings->count() != 0)
                    <div class="btn rounded-pill ps-2 pe-2 pt-1 pb-1 position-relative" style="background-color:#D6EEEC;color: #0B8E85;font-size: 14px;">
                        <i class="fa-solid fa-hourglass-start"></i>
                        Hardware ID Reset in Progress
                        @if ($licenceDetails->resets->count())
                        <span class="badge position-absolute top-0 start-100 translate-middle rounded-circle" style="transform: translate(-50%, -50%);background-color:#0B8E85;">
                            {{ $licenceDetails->resets->count() }}
                        </span>
                        @endif
                    </div>
                    @else
                    <a href="{{ route('hardware.reset', ['key' => $licenceDetails->id]) }}" class="btn rounded-pill ps-2 pe-2 pt-1 pb-1 position-relative" style="background-color:#E2E2E2;color: #000;font-size: 14px;">
                        <i class="bi bi-arrow-clockwise"></i>
                        Reset Hardware ID
                        @if ($licenceDetails->resets->count())
                        <span class="badge bg-dark position-absolute top-0 start-100 translate-middle rounded-circle" style="transform: translate(-50%, -50%);">
                            {{ $licenceDetails->resets->count() }}
                        </span>
                        @endif
                    </a>
                    @endif

                    @if ($licenceDetails->resets->count() == 0)
                    <a href="{{ route('cart.add', ['product_id' => $licenceDetails->product_id]) }}" class="rounded-pill ps-2 pe-2 pt-1 pb-1" style="background-color: #194DAB;color: #FFF;font-size: 14px;">
                        <i class="fa-solid fa-clock"></i>
                        Renew
                    </a>
                    @endif

                </div>

            </div>
            @endforeach
        </div>
        @if ($data->count() == 0)
        <div class="d-flex align-items-center flex-column bg-white justify-content-center  ">
            <img src="{{ asset('assets/userpages/images/billing.png') }}" alt="blank_page_img" class="img-fluid">

            <div class="bg-transparent fs-4 fw-medium mb-4 border border-0 border-bottom " style="color: #194dab;">
                Currently No Subscriptions Available.</div>

        </div>
        @endif
    </div>

    <x-showPagination :data=$data />

</div>
@endsection
@section('PAGE-script')

<script>
    $(document).ready(function() {
        $('.licencecopybtn').click(function() {
            var key = $(this).data('key');
            // Create a temporary input field
            var tempInput = $('<input>');
            $('body').append(tempInput);
            tempInput.val(key).select();
            document.execCommand('copy');
            tempInput.remove();
        });
    });
</script>

<script>
    $('.editable').on('click', function() {

        var id = $(this).data('id');
        var column = $(this).data('column');
        var value = $(this).text();

        var input = $('<input/>', {
            'type': 'text',
            'name': column,
            'value': $.trim(value),
            'class': "form-control p-1",
            'id': "inlineInput",
            'maxlength': 50
        });

        $(this).html(input);
        input.focus();
        input.click(function(event) {
            event.stopPropagation();
            input.val('');
            input.val($.trim(value));
        });
        input.blur(function() {
            // console.log(input);
            var newValue = input.val();
            console.log(newValue.length > 0);
            if (newValue.length > 0 && newValue.length <= 50) {
                if (newValue !== value) {
                    $.ajax({
                        url: "{{ route('licence.update.inline') }}",
                        method: 'POST',
                        data: {
                            id: id,
                            value: newValue,
                            _token: '{{ csrf_token() }}'
                        },
                        success: function(response) {
                            // Update the view
                            if (response.success) {
                                input.parent().html(newValue);
                                $(this).text(newValue);
                            } else {
                                console.log(response);
                            }
                        }
                    });
                } else {
                    $(this).text(value);
                    input.parent().html(value);
                }
            } else {
                $(this).text(value);
                input.parent().html(value);
                alert("The length of the remark cannot be null and must not exceed 50 words.")
            }
        });
    });
</script>

<script>
    function fillLicenceData(licId, key) {
        $('#licId').val(licId);
        $('#licData').val(key);
    }
</script>

@endsection
