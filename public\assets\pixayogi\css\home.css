/*------------------------------------------------- FIRST SECTION CSS START --------------------------------------------------------- */
/* .heading {
    font-size: 48px;
} */

h1 {
    font-size: 70px;
    font-weight: 700;
}

@media screen and (max-width: 1199px) {
    h1 {
        font-size: 60px;
    }
}

@media screen and (max-width: 992px) {
    h1 {
        font-size: 48px;
    }
}

h2 {
    font-size: 60px;
    line-height: 1.2;
}

h3 {
    font-weight: 700;
    margin: 0;
    font-size: 50px;
}

@media screen and (max-width: 992px) {
    h2 {
        font-size: 40px !important;
    }
}

@media only screen and (max-width: 425px) {

    h1 {
        font-size: 40px;
    }

    h2 {
        font-size: 45px;
    }

    h3 {
        font-size: 35px;
    }
}

.bg-img-fixed {
    background-repeat: no-repeat;
    background-size: cover;
    background-attachment: fixed;
}

@media screen and (max-width: 992px) {

    .bg-img-fixed {
        background-size: 0px;

    }
}

#first-p {
    color: #7E7887;
}

#tick-1 {
    width: 33px;
    height: 33px;
}

.first-button {
    background-color: #44c9f5;
    padding: 11px 23px;
    font-size: 13px;
}

.first-button:hover {
    background: linear-gradient(to bottom, #44c9f5, #78ebff);

}

.dmsans {}

.section1-left {
    padding-top: 112px;
}

@media screen and (max-width: 1199px) {
    .section1-left {
        padding-top: 10px;
    }
}

@media screen and (min-width: 992px) {


    .slider-heading {
        width: 75%;
    }

    .fourth-2-p {
        width: 75%;
    }

    .first-button {
        width: 50%;
    }

    .second-sec-button {
        width: 41%;

    }

    .third-sec-h4 {
        color: #77827A;
        font-weight: 400;
        font-size: 20px;
        width: 629px;
    }

    #third-upper-column {
        padding-right: 0px;
        padding-left: 0px;
    }



}

@media screen and (max-width: 992px) {

    .slider-heading {
        font-size: 40px;
    }


    .third-strong {
        font-size: 43px;
        font-weight: 600;
    }

    .third-strong {
        font-size: 50px;
        font-weight: 600;
    }

    /* 
    .card-heading {
        font-size: 28px;
    } */

    .fourth-2-p {
        font-size: 21px;
    }

}

.bg-img-right {
    background-position: right;
}

@media screen and (max-width: 767px) {
    .bg-img-right {
        background-position: 0%;
    }
}

/*------------------------------------------------- FIRST SECTION CSS END --------------------------------------------------------- */
/*------------------------------------------------- second SECTION CSS START --------------------------------------------------------- */
.second-container {
    background-color: #282B30;
}

#section2-img {
    width: 636.09px;
    height: 353.02px;
}

.second-sec-button {
    background-color: #44c9f5;
    padding: 11px 23px;
    font-size: 18px;
}

.third-sec-button {
    background-color: #44c9f5;
    padding: 11px 23px;
    font-size: 18px;
    max-width: fit-content;
}

.second-sec-button:hover {
    background: linear-gradient(to bottom, #44c9f5, #78ebff);
}

.third-sec-button:hover {
    background: linear-gradient(to bottom, #44c9f5, #78ebff);
}

.second-heading {
    font-size: 40px;
}



/*------------------------------------------------- FIRST SECTION CSS END --------------------------------------------------------- */
/*------------------------------------------------- third SECTION CSS START --------------------------------------------------------- */
.third-container {
    /* background-color: #F4F4F4;
     */
    background-image: url('../assets/pixayogi/images/home3bg.jpg');
    padding-bottom: 13px;
}



.third-sec-h4 {
    color: #77827A;
    font-weight: 400;
    font-size: 20px;
}


.card {
    border-radius: 30px;
    padding: 40px;
}

.card-heading {
    font-size: 25px;
}

.third-strong {
    font-size: 40px;
    font-weight: 600;
}



/*------------------------------------------------- third SECTION CSS END --------------------------------------------------------- */
/*------------------------------------------------- fourth SECTION CSS START --------------------------------------------------------- */
.fourth-2-p {
    color: #7E7887;
    font-size: 21px;

}

.third-sec-h4 {
    color: #77827A;
}



.card {
    border-radius: 30px;
    padding: 40px;
}



.card-p {
    font-size: 17px;
    color: #7E7887;
}



/*------------------------------------------------- fourth SECTION CSS end --------------------------------------------------------- */
/*------------------------------------------------- fifth SECTION CSS start --------------------------------------------------------- */
.slider-heading {
    font-size: 48px;
}

.swiper-button-prev {
    color: rgb(255 255 255 / 40%);
    background: #0000008a;
}

.swiper-button-next {
    color: rgba(255, 255, 255, 0.404);
}

:root {
    --swiper-navigation-size: 12px;
}

.swiper-button-next,
.swiper-button-prev {
    position: absolute;
    background: #0000008a;
    top: var(--swiper-navigation-top-offset, 50%);
    width: calc(var(--swiper-navigation-size)/ 44 * 27);
    height: var(--swiper-navigation-size);
    padding: 18px;
    margin-top: calc(0px - (var(--swiper-navigation-size)/ 2));
    z-index: 10;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
}

.swiper {
    width: 100%;
    height: 100%;
}

.swiper-slide {
    text-align: center;
    font-size: 18px;
    background: #fff;
    display: flex;
    justify-content: center;
    align-items: center;

}

.swiper-slide img {
    display: block;
    width: 431px;
    height: 431px;
    object-fit: cover;
}

.fifth-sec-img {
    width: 70px;
    height: 56px;
}

.fifth-sec-heading {
    font-size: 28px;
}

.fifth-p {
    color: #7E7887;
    font-size: 20px;
    line-height: 25px;
}

/*------------------------------------------------- fifth SECTION CSS END --------------------------------------------------------- */
/*------------------------------------------------- sixth SECTION CSS start --------------------------------------------------------- */
.sixth-container {
    background-color: #44C9F5;
    padding-bottom: 10px;
}

.number {
    font-size: 70px;
    color: white;
    font-weight: 100;
}

.sixth-sec-heading {
    font-size: 25px;
    color: white;
    margin-bottom: 0;
    font-weight: 700;
    padding-left: 20px;
}

.sixth-sec-p {
    font-size: 20px;
    padding-left: 30px;
}

.div-collapse {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
}

.div-collapse.show {
    max-height: auto;
    /* Set an appropriate value */
}

.div-collapse div {
    color: #7E7887;
    font-size: 15px;

}

.col-lg-11 a:hover {
    color: linear-gradient(to bottom, #44c9f5, #78ebff);

}
.robo-heading {
    font-size: 48px;
}

#robo-img {
    width: 244px;
    height: 322px;
}




/*------------------------------------------------- sixth SECTION CSS END --------------------------------------------------------- */
/*------------------------------------------------- eight SECTION CSS start --------------------------------------------------------- */
.last-img {
    width: 586px;
    height: 600px;
    padding-top: 71px;
    padding-left: 76px;
    margin-left: 26px;
}

.last-sec-p {
    color: #7E7887;
    font-size: 21px;

}

.expert-left {
    padding-top: 100px;
}

.last-number {
    color: #44c9F5;
    font-size: 36px;
}

.separator {
    border-top: 1px solid black;
    width: 80px;
    position: relative;
    top: 20px;
}

.last_button {
    background-color: #44c9f5;
    padding: 16px 23px;
    font-size: 20px;
    /* position: relative;
    top: 40px; */
    width: 290px;
    font-weight: 700;
    text-decoration: none;
}

.last_button:hover {
    background: linear-gradient(to bottom, #44c9f5, #78ebff);
}

.contact-heading {
    font-size: 18px;

}

.contact-top-heading {
    font-size: 48px;
}

/*------------------------------------------------- eighth SECTION CSS END --------------------------------------------------------- */
@media only screen and (max-width: 992px) {
    .flex-container1 {
        display: flex;
        flex-direction: column;
    }

    .last-img {

        padding-top: 0px;
        padding-left: 0px;
        margin-left: 26px;
    }

    .expert-left {
        padding-top: 0px;
    }

    .number {
        display: none;
    }

    #robo-img {
        width: 150px;
        height: 197px;
    }

    .robo-heading {
        font-size: 47px;
    }

    .section1-left {
        margin-top: 0px !important;
    }

    .first-button {
        background-color: #44c9f5;
        padding: 11px 23px;
        font-size: 13px;
        margin-bottom: 0px !important;
        max-width: fit-content;
    }

    .slider-heading {
        font-size: 40px;
    }

    .fifth-sec-heading {
        font-size: 20px;
    }

    .fifth-p {
        font-size: 18px;
    }

    .contact-top-heading {
        font-size: 42px;
    }

    .contact-img-column {
        display: flex;
        justify-content: center;
    }

    .last_button {
        background-color: #44c9f5;
        padding: 16px 23px;
        font-size: 20px;
        position: relative;
        top: 40px;
        width: 290px;
        font-weight: 700;
        text-decoration: none;
    }


}

@media only screen and (max-width: 767px) {
    .swiper-slide .img-fluid {
        height: 300px;
    }

    .last-sec-p {
        font-size: 17px;
    }

    .last_button {
        margin-bottom: 30px;
    }

    .contact-top-heading {
        font-size: 44px;
    }

    .upper-separator {
        margin-bottom: 30px;
    }

    .contact-heading {
        margin-bottom: 0px;
    }

    .slider-heading {
        font-size: 32px;
    }

    .fourth-2-p {
        font-size: 20px;
    }


    .contact-numbers {
        padding-left: 10px;
    }

    .blue-desc {
        justify-content: center;
    }

    .sixth-sec-heading {
        text-align: center;
    }

    .heading {
        line-height: 50px;
        font-size: 57px;
    }



    .fourth-2-p {
        width: 100%;
    }

    .third-sec-button {
        background-color: #44c9f5;
        padding: 11px 23px;
        font-size: 18px;
        width: 100%;
    }

}

.second-sec-buttons {
    display: flex;
    align-items: center;
}

@media screen and (max-width: 1200px) {
    .second-sec-buttons {
        flex-direction: column;
        align-items: flex-start;
    }
}

@media screen and (max-width: 767px) {
    .second-sec-buttons {
        flex-direction: row;
        align-items: center;
        margin-top: 5px;
    }
}

@media screen and (max-width: 537px) {
    .second-sec-buttons {
        flex-direction: column;
        align-items: flex-start;
    }
}

@media screen and (min-width: 992px) {

    .heading {
        font-size: 40px;
        line-height: 60px;
        margin-top: 30px;

    }


}

@media only screen and (max-width:425px) {

    .first-button {
        margin-bottom: 30px;
    }

    .second-sec-button {
        width: 100%;
    }

    .third-strong {
        font-size: 30px;
        font-weight: 600;
        line-height: 40px;
    }

    .third-sec-h4 {
        font-size: 18px;
    }

    .cards-container {
        flex-direction: column;
        gap: 30px;
    }

    .swiper-slide .img-fluid {
        height: 200px;
    }

    .contact-top-heading {
        margin-top: 30px;
        font-size: 34px;
        line-height: 40px;
    }

    .last-img {
        height: 450px;
        margin-left: 0px;
        margin-top: 30px;
    }

    .sixth-sec-heading {
        padding-left: 0px;
    }

    .sixth-sec-p {
        padding-left: 0px;
    }

    .robo-heading {
        font-size: 30px;
    }

    .heading {
        line-height: 50px;
        font-size: 30px;
        line-height: 39px;
    }

    .sixth-sec-p {
        font-size: 18px;
    }

    .robo-heading {
        font-size: 40px;
        line-height: 44px;
    }




}

@media (min-width: 992px) and (max-width: 1199px) {
    .second-sec-button {
        background-color: #44c9f5;
        padding: 11px 23px;
        font-size: 18px;
        width: 40%;
    }

    .second-sec-button {
        background-color: #44c9f5;
        padding: 11px 23px;
        font-size: 18px;
        width: 50%;
    }

    .card-wrapper {
        margin-right: 0px !important;
        margin-left: 0px !important;
    }

}

@media (min-width: 426px) and (max-width: 770px) {
    .cards-container {
        flex-direction: column;
        gap: 50px;
    }

    .last-img {
        padding-top: 0px;
        padding-left: 0px;
        margin-left: 0px;
    }

    .last-img {
        width: 500px;
        height: 530px;
        padding-top: 20px;
        padding-left: 0px;
        margin-left: 0px;
    }

    .contact-top-heading {
        margin-top: 30px;
        font-size: 34px;

    }

    .third-strong {
        font-size: 35px;
        line-height: 47px;
        font-weight: 600;
    }


}

@media only screen and (max-width:522px) {
    .first-button {
        background-color: #44c9f5;
        padding: 11px 23px;
        font-size: 13px;
        margin-bottom: 0px !important;
        width: 100%;
    }


    .second-heading {
        font-size: 32px;
    }

    .slider-heading {
        font-size: 21px;
    }

    .fourth-2-p {
        font-size: 15px;
    }

    .contact-top-heading {
        margin-top: 30px;
        font-size: 29px;
        line-height: 40px;
    }

    .last-img {
        height: 374px;
        margin-left: 0px;
        margin-top: 30px;
    }
}

/* Blog CSS Start */

@media only screen and (max-width: 1024px) {
    .px-small-pad {
        padding-left: 0.5rem !important;
        padding-right: 0.5rem !important;
    }
}

/* Blog CSS End */