body {
    color: #555;
}

.h1,
.h2,
.h3,
.h4,
.h5,
.h6,
h1,
h2,
h3,
h4,
h5,
h6 {
    margin-top: 0;
    margin-bottom: 0.5rem;
    font-weight: 500;
    line-height: 1.2;
    color: black;
}

.sub-heading {
    font-size: 24px;
}

@media only screen and (max-width:991px) {
    .sub-heading {
        font-size: 18px;
    }
}

@media only screen and (max-width:425px) {
    .sub-heading {
        font-size: 13px;
    }
}

.video-heading {
    font-size: 40px;
}

@media only screen and (max-width:991px) {
    .video-heading {
        font-size: 22px;
    }
}

/*------------------------------------------------- navbar css Starts --------------------------------------------------------- */
.navbar {
    will-change: inherit;
    transform: translateZ(0);
}

.sticky-navbar {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    background-color: #ffffff;
    transition: background-color 0.3s ease-in-out;
}

.navbar-brand {
    display: flex;
    align-items: center;
}

.navbar-brand img {
    max-height: 100%;
    margin-right: 10px;
}

#navbar {
    width: 100%;
    position: relative;
    display: flex;
    align-items: center;
    position: fixed;
    top: 2rem;
    left: 0;
    background-color: aliceblue;
    z-index: 1020;
    justify-content: space-between;
}

@media only screen and (max-width: 1399px) {
    .nav-item a {
        margin-left: 0.5rem !important;
        margin-right: 0.5rem !important;
    }
}

@media only screen and (max-width: 1199px) {
    .nav-item a {
        margin-left: 0 !important;
        margin-right: 0 !important;
    }
}

@media only screen and (max-width: 991px) {
    .horzontal-line {
        border: 1px solid #edebeb;
    }

    .dropdown-content {
        position: relative !important;
        background-color: transparent !important;
    }
}

@media only screen and (max-width: 768px) {
    .mobile-nav {
        background-color: aliceblue;
        /* height: 0px; */
    }

    .contact-bg-res {
        background-color: transparent;
    }
}

.height-mobile-nav {
    height: 50px;
}

#logo img {
    height: 45px;
}

.links {
    text-decoration: none;
    font-weight: 500;
    color: #525260;
    font-size: 16px;
}

.links:hover {
    color: #286fb4;
    text-decoration: none;
}

.hover-underline-animation {
    display: inline-block;
    position: relative;
    text-decoration: none;
}

.hover-underline-animation:after {
    content: "";
    position: absolute;
    width: 100%;
    transform: scaleX(0);
    height: 2px;
    bottom: 0;
    left: 0;
    background-color: #286fb4;
    transform-origin: bottom right;
    transition: transform 0.25s ease-out;
    text-decoration: none;
}

.hover-underline-animation:hover:after {
    transform: scaleX(1);
    transform-origin: bottom left;
}

.hover-underline-animation:active {
    color: #286fb4;
}

.dropdown-content {
    display: none;
    left: 20px;
    position: absolute;
    background-color: white;
    right: 0;
    min-width: 250px;
    /* box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.2); */
    z-index: 1;
    padding: 15px 10px;
    border-radius: 4px;
}

.dropdown-content a {
    color: #525260;
    font-weight: 500;
    padding: 5px 16px;
    text-decoration: none;
    display: block;
}

.dropdown-content a:hover {
    color: #286fb4;
}

.dropdown:hover .dropdown-content {
    display: block;
}

.header-nav>.header-nav-text {
    font-size: 1.3rem !important;
}

.schedule-nav>.schedule-text {
    font-size: 1.3rem !important;
}

/*------------------------------------------------- NAVBAR CSS Ends --------------------------------------------------------- */

/*------------------------------------------------- FOOTER CSS STARTS --------------------------------------------------------- */
ul {
    list-style-type: none;
    margin: 0;
    padding: 0;
}

ul>li {
    /* padding: 4px; */
    color: black;
}

.footer:hover {
    color: #286fb4;
    cursor: pointer;
}

hr {
    border-width: 3px;
}

/* .card {
    padding: 2% 7%
} */

.social>i {
    padding: 1%;
    font-size: 15px;
}

.social>i:hover {
    color: #286fb4;
    cursor: pointer;
}

.policy>div {
    padding: 4px;
}

.heading {
    color: black;
}

.divider {
    border-top: 1px solid #e0e0e2;
}

.logo {
    height: 50px;
}

.icon-height {
    height: 40px;
}

.flex {
    display: flex;
    padding-top: 15px;
}

.padding-left {
    padding: 0px 0px 0px 11px;
}

.height {
    height: 30px;
}

.font-size20 {
    font-size: 20px;
}

@media only screen and (max-width: 991px) {
    .footer-flex {
        display: flex;
        flex-direction: column;
    }

    .footer-JC {
        justify-content: flex-start !important;
    }
}

/*------------------------------------------------- FOOTER CSS ENDS --------------------------------------------------------- */

/*------------------------------------------------- HOME CSS STARTS --------------------------------------------------------- */

.header-show-class {
    padding-top: 5rem;
}

.img-height {
    height: 520px;
    /* margin-top: -85px; */
}

.talk-to-human-btn-img {
    height: 35px;
}

@media only screen and (max-width: 778px) {
    .talk-to-human-btn-img {
        height: 26px;
    }
}

@media only screen and (max-width: 1024px) {
    .img-height {
        height: 425px;
    }
}

@media only screen and (max-width: 1140px) {
    .img-height {
        height: 425px;
    }
}

@media only screen and (max-width: 992px) {
    .img-height {
        height: 425px;
    }
}

@media only screen and (max-width: 768px) {
    .img-height {
        height: 425px;
    }

    .talk-to-human-btn-img {
        height: 23px;
    }
}

@media only screen and (max-width: 576px) {
    .img-height {
        height: 425px;
    }
}

@media only screen and (max-width: 768px) {
    .img-height {
        height: 315px;
    }
}

@media only screen and (max-width: 425px) {
    .talk-to-human-btn-img {
        height: 23px;
    }
}

/* .icon-height{
    height: 50px;
} */

.main-btn {
    background-color: #99e24c;
    padding: 5px 13px 5px 13px;
}

.main-btn:focus {
    background-color: #99e24c;

}

.main-btn:hover {
    background-color: #99e24c;

}

.main-btn:active {
    background-color: #99e24c;

}

@media only screen and (max-width: 778px) {
    .main-btn {
        font-size: 13px;
    }
}

.section-padding {
    padding-top: 150px;
    padding-bottom: 50px;
}

.primary-h {
    font-weight: 570;
    font-size: 55px;
    color: black;
}

@media only screen and (max-width: 768px) {
    .primary-h {
        font-weight: 570;
        font-size: 35px;
    }
}

@media only screen and (max-width: 425px) {
    .primary-h {
        font-weight: 570;
        font-size: 25px;
    }
}

.secondry-h {
    font-weight: 570;
    font-size: 44px;
    color: black;
}

.para1 {
    font-size: 19px;
    color: #675f72;
}

.bolt-headings {
    font-size: 30px;
}

@media only screen and (max-width:425px) {
    .bolt-headings {
        font-size: 19px;
    }
}

@media only screen and (max-width:991px) {
    .capsule-mp {
        margin: 0rem 0rem 0rem 0rem;
        padding: 1rem;
    }
}

@media only screen and (max-width:991px) {
    .para1 {
        font-size: 15px;
    }
}

@media only screen and (max-width:425px) {
    .para1 {
        font-size: 13px;
    }
}



/* @media only screen and (max-width: 768px) {
    .para {
        font-size: 12px;}
  } */
.blue-btn,
.blue-btn:focus,
.blue-btn:active,
.blue-btn:hover{
    -webkit-background: linear-gradient(182deg, #2572be, #2280e1d4);
    /* Safari/Chrome */
    -moz-background: linear-gradient(182deg, #2572be, #2280e1d4);
    /* Firefox */
    -ms-background: linear-gradient(182deg, #2572be, #2280e1d4);
    /* Internet Explorer */
    -o-background: linear-gradient(182deg, #2572be, #2280e1d4);
    /* Opera */
    background: linear-gradient(182deg, #2572be, #2280e1d4) ;
    box-shadow: 0 6px 18px #8dc4fb;
    display: flex;
    border: 0px;
    border-radius: 50rem !important;
    align-items: center;
    gap: .5rem !important;
    color: #ffffff !important;
    width: max-content;
    font-size: 15px;
    font-weight: normal;
}

.green-btn,
.green-btn:hover,
.green-btn:active,
.green-btn:focus{
    background-color: #99e24c !important;
    box-shadow: 0 6px 18px #b3fb8d !important;
    display: flex;
    border: 0px !important;
    border-radius: 50rem;
    align-items: center;
    gap: .5rem !important;
    color: #ffffff !important;
    width: max-content;
    font-size: 15px;
    font-weight: normal;
}

.btn-icon-circle {
    height: 50px;
    width: 50px;
    background-color: gray;
}

.white-btn {
    background-color: white;
}

.f-s-18 {
    font-size: 18px;
}

.center-items {
    align-items: center;
}

.align-baseline {
    align-items: baseline;
}

.capsule-div {
    padding: 90px 31px 90px 31px;
    align-items: center;
}

@media only screen and (max-width:1299px) {
    .capsule-div {
        padding: 50px 20px 50px 20px;
    }
}

.capsule-width {
    width: 280px;
}

.capsule-div-mr {
    margin: 80px 0px -80px 0px;
    padding: 80px 31px 80px 31px;
}

@media only screen and (max-width:1199px) {
    .capsule-div-mr {
        padding: 60px 9px 60px 9px;
    }

    .capsule-div {
        padding: 60px 9px 60px 9px;
        align-items: center;
    }
}

@media only screen and (max-width:1024px) {
    .capsule-div-mr {
        padding: 50px 9px 50px 9px;
    }

    .capsule-div {
        padding: 50px 9px 50px 9px;
        align-items: center;
    }
}

@media only screen and (max-width:991px) {
    .capsule-div-mr {
        margin: 0px 0px 0px 0px;
        padding: 50px 9px 50px 9px;
    }

    .capsule-div {
        padding: 50px 9px 50px 9px;
        align-items: center;
    }
}

@media only screen and (max-width: 1440px) {
    .capsule-width {
        width: 280px;
    }
}

@media only screen and (max-width: 1199px) {
    .capsule-width {
        width: 250px;
    }
}

@media only screen and (max-width: 1024px) {
    .capsule-width {
        width: 225px;
    }
}

@media only screen and (max-width: 991px) {
    .capsule-width {
        width: 350px;
        /* height: 43vh; */
    }

    .header-items {
        align-items: flex-start !important;
        padding-bottom: 15px !important;
        gap: 15px !important;
    }
}

@media only screen and (max-width: 425px) {
    .capsule-width {
        width: 350px;
    }
}

@media only screen and (max-width: 375px) {
    .capsule-width {
        width: 345px;
    }
}

@media only screen and (max-width: 320px) {
    .capsule-width {
        width: 220px;
    }
}

.section-padding-minus {
    padding-top: 30px;
    padding-bottom: 50px;
}

.laptop-img {
    margin-top: -18%;
}

.center {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.no-gutters {
    overflow: hidden;
}

.circle-5 {
    animation: LeftRight 5s linear infinite;
    position: absolute;
    background-color: transparent;
    background-image: linear-gradient(150deg, #0052f5 0%, #0d00ff 100%);
    border-radius: 50% 50% 50% 50%;
    box-shadow: -1px 10px 17px 4px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    /* position: relative; */
    z-index: -1;
    transition: 2s ease-out;
    opacity: 0.5;
}

@keyframes LeftRight {

    0%,
    100% {
        right: 100px;
    }

    50% {
        right: 300px;
    }
}

.circle-6 {
    animation: RightTop 12s linear infinite;
    position: absolute;
    background-color: transparent;
    background-image: linear-gradient(150deg, #bbff35 0%, #5fcc92 100%);
    border-radius: 50% 50% 50% 50%;
    box-shadow: -1px 10px 17px 4px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    /* position: relative; */
    z-index: -1;
    transition: 2s ease-out;
}

@keyframes RightTop {

    0%,
    100% {
        right: 100px;
        top: 100px;
    }

    50% {
        top: 750px;
        right: 750px;
    }
}

.circle-7 {
    animation: LeftTop 8s linear infinite;
    position: absolute;
    background-image: linear-gradient(120deg, #89f7fe 0%, #66a6ff 100%);
    border-radius: 50% 50% 50% 50%;
    box-shadow: -1px 10px 17px 4px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    /* position: relative; */
    width: 40px;
    height: 40px;
    z-index: -1;
    transition: 2s ease-out;
}

@keyframes LeftTop {

    0%,
    100% {
        left: 150px;
        top: 150px;
    }

    50% {
        top: 350px;
        left: 350px;
    }
}

@media (max-width: 450px) {
    @keyframes LeftTop {

        0%,
        100% {
            left: 50px;
            top: 50px;
        }

        50% {
            top: 250px;
            left: 250px;
        }
    }
}

.circle-8 {
    animation: UpDown 5s linear infinite;
    position: absolute;
    background-color: transparent;
    background-image: linear-gradient(to right,
            #f78ca0 0%,
            #f9748f 19%,
            #fd868c 60%,
            #fe9a8b 100%);
    border-radius: 50% 50% 50% 50%;
    box-shadow: -1px 10px 17px 4px rgba(0, 0, 0, 0.1);
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: -1;
}

@keyframes UpDown {

    0%,
    100% {
        bottom: 0;
    }

    50% {
        bottom: 250px;
    }
}

.circle-9 {
    position: absolute;
    background: linear-gradient(180deg,
            rgba(34, 128, 225, 0.59) 0%,
            rgba(185, 217, 250, 0) 100%);
    border-radius: 50% 50% 50% 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0.5;
    bottom: 67px;
    right: 357px;
}

.circle-10 {
    position: absolute;
    background: linear-gradient(180deg,
            #8cdb51 0%,
            rgba(103, 206, 85, 0.25) 100%);
    border-radius: 50% 50% 50% 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0.5;
    bottom: -304px;
    left: 208px;
}

@media only screen and (max-width:991px) {
    .circle-10 {
        left: 27px;
    }
}

.circle-11 {
    position: absolute;
    background: linear-gradient(180deg,
            #8cdb51 0%,
            rgba(103, 206, 85, 0.25) 100%);
    border-radius: 50% 50% 50% 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0.5;
    bottom: -190px;
    right: 127px;
}

.circle-12 {
    position: absolute;
    background: linear-gradient(180deg,
            #8cdb51 0%,
            rgba(103, 206, 85, 0.25) 100%);
    border-radius: 50% 50% 50% 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0.5;
    bottom: -2000px;
    right: 450px;
}

.circle-13 {
    position: absolute;
    background: linear-gradient(180deg,
            rgba(34, 128, 225, 0.59) 0%,
            rgba(185, 217, 250, 0) 100%);
    border-radius: 50% 50% 50% 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0.5;
    bottom: -2400px;
    right: 650px;
}

.circle-14 {
    position: absolute;
    background: linear-gradient(180deg,
            rgba(34, 128, 225, 0.59) 0%,
            rgba(185, 217, 250, 0) 100%);
    border-radius: 50% 50% 50% 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0.5;
    bottom: -2900px;
    right: 850px;
}

.circle-15 {
    position: absolute;
    background: linear-gradient(180deg,
            #8cdb51 0%,
            rgba(103, 206, 85, 0.25) 100%);
    border-radius: 50% 50% 50% 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0.5;
    bottom: -3600px;
    right: 150px;
}

.circle-16 {
    position: absolute;
    background: linear-gradient(180deg,
            #8cdb51 0%,
            rgba(103, 206, 85, 0.25) 100%);
    border-radius: 50% 50% 50% 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0.5;
    bottom: -3270px;
    left: 150px;
}

.circle-17 {
    position: absolute;
    background: linear-gradient(180deg,
            #8cdb51 0%,
            rgba(103, 206, 85, 0.25) 100%);
    border-radius: 50% 50% 50% 50%;
    box-shadow: 0 6px 18px #b3fb8d;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0.5;
    bottom: -3500px;
    left: 150px;
}

.circle-18 {
    position: absolute;
    background: linear-gradient(180deg,
            rgba(34, 128, 225, 0.59) 0%,
            rgba(185, 217, 250, 0) 100%);
    border-radius: 50% 50% 50% 50%;
    box-shadow: 0 6px 18px #8dc4fb;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0.5;
    bottom: -3800px;
    right: 150px;
}

/*----------------------------------------------------------- TIMELINE CSS STARTS ---------------------------------------------------*/

:root {
    --white: #ffffff;
    --black: #000000;
    --blue: #0288d1;
    --gray: #ebebeb;
    --box-shadow1: 0px 0px 18px 2px rgba(10, 55, 90, 0.15);
}

:focus {
    outline: 0px solid transparent !important;
}

.timeline {
    /* padding: 50px 0; */
    position: relative;
}

.timeline-nodes {
    /* padding-bottom: 25px; */
    position: relative;
}

.timeline-nodes:nth-child(even) {
    flex-direction: row-reverse;
}

.timeline h3,
.timeline p {
    padding: 5px 15px;
}

.timeline h3 {
    color: #000000;
}

.timeline::before {
    content: "";
    display: block;
    position: absolute;
    top: 0;
    left: 50%;
    width: 0;
    border-left: 1px solid rgb(173, 173, 173);
    height: 98%;
    transform: translateX(-50%);
}

.timeline-content {
    border: 1px solid var(--blue);
    position: relative;
    border-radius: 0 0 10px 10px;
    box-shadow: 0px 3px 25px 0px rgba(10, 55, 90, 0.2);
}

.timeline-nodes:nth-child(odd) h3,
.timeline-nodes:nth-child(odd) p {
    text-align: left;
}

.timeline-nodes:nth-child(odd) .timeline-date {
    text-align: left;
}

.timeline-nodes:nth-child(even) .timeline-date {
    text-align: right;
}

.timeline-nodes:nth-child(odd) .timeline-content::after {
    content: "";
    position: absolute;
    top: 5%;
    left: 100%;
    width: 0;
    border-left: 10px solid var(--blue);
    border-top: 10px solid transparent;
    border-bottom: 10px solid transparent;
}

.timeline-nodes:nth-child(even) .timeline-content::after {
    content: "";
    position: absolute;
    top: 5%;
    right: 100%;
    width: 0;
    border-right: 10px solid var(--blue);
    border-top: 10px solid transparent;
    border-bottom: 10px solid transparent;
}

.timeline-image {
    position: relative;
}

.timeline-image::before {
    content: "";
    width: 50px;
    height: 50px;
    display: block;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    /* background-image: linear-gradient(to right, #2f7cca , #4593e4);    */
    background: #8cdb51;
}

.timeline-image img {
    position: relative;
}

/*small device style*/

@media (max-width: 767px) {

    .timeline-nodes:nth-child(odd) h3,
    .timeline-nodes:nth-child(odd) p {
        text-align: left;
    }

    .timeline-nodes:nth-child(even) {
        flex-direction: row;
    }

    .timeline::before {
        content: "";
        display: block;
        position: absolute;
        top: 0;
        /* left: 4%; */
        width: 0;
        border-left: 1px solid rgb(173, 173, 173) !important;
        height: 100%;
        z-index: -1;
        transform: translateX(-50%);
    }

    .timeline h3 {
        font-size: 1.7rem;
    }

    .timeline p {
        font-size: 14px;
    }

    .timeline-image {
        position: relative;
        left: 0%;
        /*transform: translateX(-50%;);*/
    }

    .timeline-nodes:nth-child(odd) .timeline-content::after {
        content: "";
        position: absolute;
        top: 5%;
        left: auto;
        right: 100%;
        width: 0;
        border-left: 0;
        border-right: 10px solid var(--blue);
        border-top: 10px solid transparent;
        border-bottom: 10px solid transparent;
    }

    .timeline-nodes:nth-child(even) .timeline-content::after {
        content: "";
        position: absolute;
        top: 5%;
        right: 100%;
        width: 0;
        border-right: 10px solid var(--blue);
        border-top: 10px solid transparent;
        border-bottom: 10px solid transparent;
    }

    .timeline-nodes:nth-child(even) .timeline-date {
        text-align: left;
    }

    .timeline-image::before {
        width: 65px;
        height: 65px;
    }
}

/*extra small device style */
@media (max-width: 575px) {
    .timeline::before {
        content: "";
        display: block;
        position: absolute;
        top: 0;
        /* left: 3%; */
    }

    .timeline-image {
        position: relative;
        /* left: -5%; */
    }

    .timeline-image::before {
        width: 60px;
        height: 60px;
    }

    .capsule-div-mr {
        margin: 0 !important;
        padding: 0 !important;
    }

    .capsule-mp {
        margin: 0rem 0rem 0rem 0rem;
        padding: 1rem 1rem 1rem 1rem !important;
    }

    .laptop-img {
        height: 55vw !important;
    }
}

.padding-top {
    padding-top: 130px;
}

.bg-color-primary {
    background-image: linear-gradient(346deg, #2571bd, #177adec4);
}

.bg-blue {
    background-color: #2572be;
}

.accordion-header:active {
    background-color: #d10202;
}

.section-pd-100 {
    padding-top: 100px;
    padding-bottom: 100px;
}

/* .bg-img{
    background-image: url('assets/primailer/images/homebg3.png');
    background-repeat: no-repeat;
    background-size: cover;
} */

/*------------------------------------------------- EMAIL SUBSCRIPTION FORM------------------------------------------------ */

.email-div {
    background: #ffffff;
    border-radius: 20px;
    width: 35vw;
}

.email-center {
    align-items: center;
}

.bg-light-blue {
    background: #e0efff3b;
}

.img-h-500 {
    height: 500px;
}

.input-form-style {
    width: 100%;
    padding: 12px;
    border: 1px solid #ccc;
    border-radius: 8px;
    border-style: none none solid none;
    background: #f0f0f000;
    box-sizing: border-box;
    margin-top: 6px;
    margin-bottom: 16px;
    resize: vertical;
    outline: none !important;
}

.contact-info {
    font-size: 18px;
}

@media only screen and (max-width: 1399px) {
    .contact-info {
        font-size: 18px;
    }
}

@media only screen and (max-width: 1199px) {
    .contact-info {
        font-size: 18px;
    }
}

@media only screen and (max-width: 991px) {
    .contact-info {
        font-size: 18px;
    }
}

.container1 {
    border-radius: 20px;
    background-color: #f0f0f080;
    padding: 60px;
    border: 1px solid #000000;
    width: 645px;
}

@media screen and (max-width: 768px) {
    .container1 {
        padding: 36px;
        width: 100%;
    }
}

@media only screen and (max-width: 425px) {
    .container1 {
        padding: 15px;
        width: 100%;
    }
}

.bg-image {
    background-image: url();
}

.p-grey {
    color: #dfdfdf;
}

#bubble-container {
    list-style-type: none;
    padding: 0;
    margin: 0;
}

.bubble {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-color: #ff00ff;
    /* Set the desired bubble color */
    position: absolute;
    animation: moveBubble 8s ease-out;
}

/*  */

.flip-scale-2-hor-top {
    animation: flip-scale-2-hor-top 0.5s linear infinite both;
}

/* ----------------------------------------------
 * Generated by Animista on 2023-6-14 17:53:0
 * Licensed under FreeBSD License.
 * See http://animista.net/license for more info.
 * w: http://animista.net, t: @cssanimista
 * ---------------------------------------------- */

@keyframes flip-scale-2-hor-top {
    0% {
        transform: translateY(0) rotateX(0) scale(1);
        transform-origin: 50% 0;
    }

    50% {
        transform: translateY(-50%) rotateX(-90deg) scale(2);
        transform-origin: 50% 50%;
    }

    100% {
        transform: translateY(-100%) rotateX(-180deg) scale(1);
        transform-origin: 50% 100%;
    }
}

.scale-up-center {
    animation: scale-up-center 0.9s cubic-bezier(0.39, 0.575, 0.565, 1) both;
}

/* ----------------------------------------------
 * Generated by Animista on 2023-6-14 17:55:53
 * Licensed under FreeBSD License.
 * See http://animista.net/license for more info.
 * w: http://animista.net, t: @cssanimista
 * ---------------------------------------------- */

@keyframes scale-up-center {
    0% {
        transform: scale(0.5);
    }

    100% {
        transform: scale(1);
    }
}

/* blog */

.font-blog {
    margin-bottom: 0.5rem;
    font-weight: 500;
    line-height: 1.2;
}

.fly-img {
    background-repeat: no-repeat;
    background-size: 200px;
    background-position: 88% 50%;
}

@media only screen and (max-width: 1024px) {
    .px-small-pad {
        padding-left: 0.5rem !important;
        padding-right: 0.5rem !important;
    }
}

@media only screen and (max-width: 991px) {
    .font-blog {
        font-size: 22px;
    }

    .margin-left-img {
        margin-left: 1px !important;
        /* margin-right: -14px !important; */
    }
}

@media only screen and (max-width: 425px) {
    .flex-col {
        flex-direction: column;
    }

    .margin-left-img {
        margin-left: 1px !important;
    }
}

.width {
    width: 526px;
}

.header-items {
    align-items: center;
    gap: 15px;
}

@media only screen and (max-width: 1399px) {
    .width {
        width: 440px;
    }

    .margin-left-img {
        margin-left: -7px !important;
    }
}

@media only screen and (max-width: 1199px) {
    .margin-left-img {
        margin-left: -50px !important;
    }
}

@media screen and (max-width: 320px) {
    .fly-img {
        background-position: 145% 89% !important;
    }
}

.margin-left-img {
    margin-left: 40px;
}

@media only screen and (max-width: 768px) {
    .container-reaching .fluid-reaching {
        margin-bottom: -1.875rem !important;
    }
}

@media only screen and (max-width: 575px) {
    .container-reaching .fluid-reaching {
        margin-bottom: 0rem !important;
    }

    .image-wyg {
        margin-top: -1.875rem !important;
    }
}
