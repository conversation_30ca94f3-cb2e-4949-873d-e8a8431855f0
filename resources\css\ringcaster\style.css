p {
    color: #171717;
}

.text-dark-color {
    color: #000000;
    margin-bottom: 10px;
    font-size: 20px;
}

.page-category {
    font-size: 25px;
    color: #FCD250;
    font-weight: 600;
}

.list-fontsize {
    font-size: 18px;
}

.faq-answer {
    font-size: 1rem;
}

.icon-size {
    font-size: 25px;
}

@media only screen and (max-width:991px) {
    .page-category {
        font-size: 18px;
    }
}

@media only screen and (max-width:425px) {
    .page-category {
        font-size: 14px;
    }

    .list-fontsize {
        font-size: 15px;
    }

    .faq-answer {
        font-size: 15px;
    }

    .icon-size {
        font-size: 18px;
    }
}

.h2-custom {
    font-size: 50px !important;
}

.h3-custom {
    font-size: 40px !important;
}

.h4-custom {
    font-size: 20px !important;
}

.h6-custom {
    font-size: 18px !important;
}

@media only screen and (max-width:1199px) {

    .h2-custom {
        font-size: 36px !important;
    }

    .h3-custom {
        font-size: 30px !important;
    }

    .h4-custom {
        font-size: 18px !important;
    }

    .h6-custom {
        font-size: 16px !important;
    }
}

@media only screen and (max-width:991px) {

    .h2-custom {
        font-size: 30px !important;
    }

    .h3-custom {
        font-size: 25px !important;
    }

    .h4-custom {
        font-size: 18px !important;
    }

    .h6-custom {
        font-size: 16px !important;
    }
}

@media only screen and (max-width:425px) {

    .h2-custom {
        font-size: 23px !important;
    }

    .h3-custom {
        font-size: 20px !important;
    }

    .h4-custom {
        font-size: 18px !important;
    }

    .h6-custom {
        font-size: 16px !important;
    }
}

.btn-font {}

@media only screen and (max-width:425px) {
    .btn-font {
        font-size: 13px;
    }
}

.talk-to-human-btn-img {
    height: 35px;
}

@media only screen and (max-width:425px) {
    .talk-to-human-btn-img {
        height: 25px;
    }
}


.header-div>.header-ul {
    gap: 1.5rem !important;
}

.header-nav>.header-nav-text {
    font-size: 1.5rem !important;
}

.schedule-nav>.schedule-text {
    font-size: 1rem !important;
}

/*------------------------------------------------- navbar css Starts --------------------------------------------------------- */



.sticky-navbar {
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 9999;
    background-color: #ffffff;
    transition: background-color 0.3s ease-in-out;
}

.navbar-brand {
    display: flex;
    align-items: center;
}

.navbar-brand img {
    max-height: 100%;
    margin-right: 10px;
}

#navbarSupportedContent {
    transition: .3s ease;
}

.dark-c-gray {
    color: #212529;
}


#navbar {
    width: 100%;
    position: relative;
    display: flex;
    align-items: center;
    position: sticky;
    top: 0;
    background-color: #D4ECFD;
    z-index: 1;
    justify-content: space-between;
}

#navbar.navbar-scrolled {
    background-color: #000000;
}

#logo.logo-scrolled img {
    height: 50px;
}

@media screen and (max-width: 991px) {
    #logo.logo-scrolled img {
        height: 38px;
    }
}

#logo img {
    height: 50px;
}

@media screen and (max-width: 991px) {
    #logo img {
        height: 38px;
    }
}

#logo1 img {
    height: 50px;
}

.yellow-btn {
    box-shadow: 0px 4px 5px 0px #FFE59561;
    font-weight: 500;
    color: #000000 !important;
    background-color: #FCD250 !important;
    width: max-content;
}

.yellow-btn:hover {
    color: #000000 !important;
    background-color: #FCD250 !important;
}

.yellow-btn:focus-visible {
    color: #000000 !important;

    background-color: #FCD250 !important;
}

.yellow-btn:active {
    color: #000000 !important;
    background-color: #FCD250 !important;
}

 .links {
    text-decoration: none !important;
    font-weight: 500;
    color: white;
    font-size: 16px;
}

.links:hover {
    color: #2d2db0;
    text-decoration: none !important;
}

.hover-underline-animation {
    display: inline-block;
    position: relative;
    text-decoration: none;
}

.hover-underline-animation:after {
    content: "";
    position: absolute;
    width: 100%;
    transform: scaleX(0);
    height: 2px;
    bottom: 0;
    left: 0;
    background-color: #FCD250;
    transform-origin: bottom right;
    transition: transform 0.25s ease-out;
    text-decoration: none;
}

.hover-underline-animation:hover:after {
    transform: scaleX(1);
    transform-origin: bottom left;
}

.hover-underline-animation:active {
    color: #0087ca;
}

.dropdown-content {
    overflow: auto;
    max-height: 600px;
    display: none;
    /* left: 0; */
    position: absolute;
    background-color: #fffdf7;
    right: 0;
    min-width: max-content;
    box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.2);
    z-index: 1;
    padding: 15px 10px;
    border-radius: 4px;
}

.dropdown-content-services {
    display: none;
    left: 10px;
    position: absolute;
    background-color: #fff9e4;
    right: 0;
    min-width: 300px;
    box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.2);
    z-index: 1;
    padding: 15px 10px;
    border-radius: 4px;
}


@media only screen and (max-width: 991px) {
    .dropdown-content {
        left: -160px;
        top: 50px;
        /* min-width: 300px; */
    }

    .dropdown-content-services {
        left: -100px;
        top: 50px;
        min-width: 300px;
    }

    #navbarSupportedContent {
        margin-top: 1rem;
        margin-bottom: 1rem;
    }

    #navbarSupportedContent ul li a {
        padding: 12px 0px 5px 10px;
    }



    .nav-item-btn {
        margin-top: -1rem !important;
        margin-bottom: -2rem !important;
    }
}

@media only screen and (max-width: 425px) {
    .dropdown-content {
        left: -30%;
        top: 50px;
        /* min-width: 300px; */
    }

    .dropdown-content-services {
        left: -70%;
        top: 50px;
        min-width: 300px;
    }
}

.dropdown-content a {
    color: #525260;
    font-weight: 500;
    padding: 5px 10px;
    text-decoration: none;
    display: block;
}

.dropdown-content a:hover {
    color: #2D2DB0;
}

.dropdown:hover .dropdown-content {
    display: block;
}

.nav-items-responsive {
    align-items: center;
}


/*------------------------------------------------- navbar css Ends --------------------------------------------------------- */

/*------------------------------------------------- FOOTER CSS STARTS --------------------------------------------------------- */
.h1-custom,
.h2-custom,
.h3-custom,
.h4-custom,
.h5-custom,
.h6-custom {
    margin-top: 0;
    margin-bottom: 0.5rem;
    /* font-weight: 500; */
    /* line-height: 1.2; */
    /* color:black ; */
}

.para-color-rap {
    color: #869FB2;
    font-size: 17px;
    font-weight: 400;
}

.para-PPT {
    color: #869FB2;
}

.para-PPT:hover {
    text-decoration: none !important;
}

.para-color-list {
    color: #869FB2;
    font-size: 16px;
    font-weight: 400;
    line-height: 40px;
    margin-bottom: 6px;
    transition: .25s;
}

.para-color-list:hover {
    color: #e8e4e4;
}

ul {
    list-style-type: none;
    margin: 0;
    padding: 0
}

ul>li {
    padding-bottom: 10px !important;
}

/* ul>li:hover {
    color: #286fb4;
    cursor: pointer
} */

hr {
    border-width: 1px
}

/* .card {
    padding: 2% 7%
} */

.social>i {
    padding: 1%;
    font-size: 15px
}

.social>i:hover {
    color: #286fb4;
    cursor: pointer
}

.policy>div {
    padding: 4px
}

.heading {
    color: black
}

.divider {
    border-top: 1px solid #869FB2;
}

.logo {
    height: 50px;
}

.icon-height {
    height: 30px;
}

.flex {
    display: flex;
    padding-top: 15px;
}

.padding-left {
    padding: 0px 0px 0px 11px;
}

.height {
    height: 30px;

}

.font-size20 {
    font-size: 20px;
}

.rap-footer-flex {
    display: flex;
    justify-content: space-between;
}

@media only screen and (max-width: 991px) {
    .rap-footer-flex {
        flex-direction: column;
    }
}

.nav-link-head.active {
    color: #2d2db0;
}

@media only screen and (max-width: 991px) {
    .footer-flex {
        display: flex;
        flex-direction: column;
    }

    .footer-JC {
        justify-content: flex-start !important;
    }
}


/*------------------------------------------------- FOOTER CSS ENDS --------------------------------------------------------- */


/* contact form  */
.email-div {
    background: #ffffff;
    border-radius: 20px;
    width: 35vw;
}

.email-center {
    align-items: center;
}

.bg-light-blue {
    background: #e0efff3b;
}

/* contact text-flelds  */


.input-form-style {
    width: 100%;
    padding: 12px;
    border: 1px solid #ccc;
    border-radius: 8px;
    border-style: none none solid none;
    background: #f0f0f000;
    box-sizing: border-box;
    margin-top: 6px;
    margin-bottom: 16px;
    resize: vertical;
    outline: none !important;
}

.container1 {
    border-radius: 20px;
    background-color: #D9EEFD !important;
    padding: 60px;
    border: 1px solid #000000;
    width: 645px;
}

@media screen and (max-width: 768px) {
    .container1 {
        padding: 36px;
        width: 450px;
    }
}

.fly-img {
    background-repeat: no-repeat;
    background-size: 200px;
    background-position: 88% 50%;
}

@media only screen and (max-width: 768px) {
    .fly-img {
        background-repeat: no-repeat;
        background-size: 200px;
        background-position: 109% 50%;
    }
}

input::placeholder {
    color: #938d8d;
    opacity: 1;
}

input:focus {
    color: #000000;
}

textarea {
    border: none;
    border-bottom: 1px solid #938d8d;
    outline: none;
    transition: border-bottom 0.3s;
}

textarea::placeholder {
    color: #938d8d;
}

select:focus {
    outline: none;
}

.play-btn {
    height: 50px;
    width: 50px;
    line-height: 50px;
    text-align: center;
    border-radius: 100%;
    color: #000000;
    /* Set the color of the play button */
    z-index: 999;
    position: relative;
    display: inline-block;
    overflow: hidden;
}

.play-btn i {
    font-size: 24px;
    background: black;
    border-radius: 50px;
    width: 50px;
    padding: 12px;
    /* margin-top: 26px; */
    height: 50px;
}

.waves-block {
    position: absolute;
    width: 150px;
    height: 150px;
    background: rgba(0, 0, 0, 0.3);
    opacity: 0;
    border-radius: 100%;
    /* right: 437px; */
    right: 526px;
    /* bottom: -50px; */
    z-index: -1;
    animation: waves 3s ease-in-out infinite;
}

@media only screen and (max-width: 1399px) {
    .waves-block {
        right: 496px;
    }
}

@media only screen and (max-width: 1299px) {
    .waves-block {
        right: 495px;
    }
}

@media only screen and (max-width: 1199px) {
    .waves-block {
        right: 405px;
    }
}


@media only screen and (max-width: 1024px) {
    .waves-block {
        right: 405px;
    }
}


@media only screen and (max-width: 991px) {
    .waves-block {
        right: 346px;
        top: -13px;
    }
}

.wave-1 {
    animation-delay: 0s;
    background-color: #000000;
}

.wave-2 {
    animation-delay: 1s;
}

.wave-3 {
    animation-delay: 2s;
}

@keyframes waves {
    0% {
        transform: scale(0.2);
        opacity: 0;
    }

    50% {
        opacity: 0.9;
    }

    100% {
        transform: scale(0.9);
        opacity: 0;
    }
}

/* BULK SMS MARKETING  */

.bg-img-size {
    background-size: 50%;
}

@media only screen and (max-width:1199px) {
    .bg-img-size {
        background-size: 50%;
    }
}
