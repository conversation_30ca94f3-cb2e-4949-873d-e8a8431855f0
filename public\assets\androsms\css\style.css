@media screen and (-webkit-min-device-pixel-ratio: 0) {
    input[type="range"] {
        -webkit-appearance: none;
        background-color: white;
        /* background: #f2d563 !important; */

    }

    input[type="range"]::-webkit-slider-runnable-track {
        -webkit-appearance: none;
        background: #f2d563 !important;

    }

    input[type="range"]::-webkit-slider-thumb {
        -webkit-appearance: none;
        cursor: ew-resize;
        background: #fff !important;
    }
}



body {
    padding-top: 5%;
}

@media only screen and (max-width: 2560px) {
    body {
        padding-top: 5%;
    }
}

@media only screen and (max-width: 1299px) {
    body {
        padding-top: 6%;
    }
}

@media only screen and (max-width: 1199px) {
    body {
        padding-top: 8%;
    }
}

@media only screen and (max-width: 992px) {
    body {
        padding-top: 10%;
    }
}

@media only screen and (max-width: 991px) {
    body {
        padding-top: 10%;
    }
}

@media only screen and (max-width: 870px) {
    body {
        padding-top: 11%;
    }
}

@media only screen and (max-width: 768px) {
    body {
        padding-top: 12%;
    }
}

@media only screen and (max-width: 767px) {
    body {
        padding-top: 13%;
    }
}

@media only screen and (max-width: 690px) {
    body {
        padding-top: 14%;
    }
}

@media only screen and (max-width: 625px) {
    body {
        padding-top: 15%;
    }
}

@media only screen and (max-width: 580px) {
    body {
        padding-top: 16%;
    }
}

@media only screen and (max-width: 540px) {
    body {
        padding-top: 17%;
    }
}

@media only screen and (max-width: 500px) {
    body {
        padding-top: 20%;
    }
}

@media only screen and (max-width: 425px) {
    body {
        padding-top: 23%;
    }
}

@media only screen and (max-width: 375px) {
    body {
        padding-top: 25%;
    }
}

@media only screen and (max-width: 320px) {
    body {
        padding-top: 29%;
    }
}

.header-div>.header-ul {
    gap: 1.5rem !important;
}

.header-nav>.header-nav-text {
    font-size: 1.3rem !important;
}

.schedule-nav>.schedule-text {
    font-size: 1rem !important;
}

.text-purple {
    color: #561490;
}

.text-pink {
    color: #e13362;
}

.androsms-h1 {
    font-size: 53px;
    line-height: 62px;
    letter-spacing: 0em;
}

@media only screen and (max-width: 991px) {
    .androsms-h1 {
        font-size: 35px;
        line-height: 40px;
    }
}

.androsms-h2 {
    font-size: 40px;
    line-height: 41px;
    letter-spacing: 0em;
}

@media only screen and (max-width: 991px) {
    .androsms-h2 {
        font-size: 30px;
        line-height: 40px;
        letter-spacing: 0em;
    }
}

.text-dark-color {
    color: #000000;
    margin-bottom: 10px;
    font-size: 18px;
}

/*------------------------------------------------- NAVBAR css Starts --------------------------------------------------------- */
@media only screen and (max-width: 991px) {
    .horzontal-line {
        border: 1px solid #e13362;
    }
}

#logo {
    height: 50px;
}

.pink-btn {
    background: #e13362;
    box-shadow: 0 6px 18px #e1508b57;
    width: fit-content;
    cursor: pointer;
}

.mobile-nav {
    display: flex;
    justify-content: space-between;
}

.max-width-fit {
    max-width: fit-content;
}

.navbar-toggler {
    padding: var(--bs-navbar-toggler-padding-y) var(--bs-navbar-toggler-padding-x);
    font-size: var(--bs-navbar-toggler-font-size);
    line-height: 1;
    color: rgb(255 255 255);
    background-color: transparent;
    border: var(--bs-border-width) solid rgb(255 255 255 / 15%);
    border-radius: var(--bs-navbar-toggler-border-radius);
    transition: var(--bs-navbar-toggler-transition);
}

/*------------------------------------------------- NAVBAR CSS Ends --------------------------------------------------------- */

/*------------------------------------------------- HOME PAGE CSS ----------------------------------------------------------- */

.bg-color {
    background: #f6f2fa;
}

.bg-light-grey {
    background: #f8f8f8;
}

.pink-btn-shadow {
    box-shadow: 0 6px 18px #e1508b57;
}

.padding-h-sec {
    padding: 80px 50px 80px 50px;
}

.info-box {
    box-shadow: 8px 8px 51px 0px #0000001a;
    background: #ffffffa6;
}

.bg-light-shade {
    background: #fef9e8;
}

.grid-3-col {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    column-gap: 6rem;
}

@media only screen and (max-width: 1399px) {
    .grid-3-col {
        grid-template-columns: repeat(3, 1fr);
        column-gap: 2rem;
    }
}

@media only screen and (max-width: 768px) {
    .grid-3-col {
        grid-template-columns: repeat(1, 1fr);
        column-gap: 1rem;
        row-gap: 2rem;
    }
}

@media only screen and (max-width: 425px) {
    .grid-3-col {
        grid-template-columns: repeat(1, 1fr);
        column-gap: 0rem;
        row-gap: 1rem;
    }
}

.carousel-indicators [data-bs-target] {
    background-color: #e13362 !important;
    width: 10px;
    height: 10px;
    border-radius: 50%;
}

.carousel-indicators {
    position: absolute;
    right: 45px;
    bottom: 0;
    left: 0;
    top: 285px;
    z-index: 2;
    display: flex;
    justify-content: center;
    padding: 0;
    margin-right: 15%;
    margin-bottom: 1rem;
    margin-left: 15%;
}

@media only screen and (max-width: 767px) {
    .carousel-indicators {
        position: absolute;
        right: 30px;
        bottom: 0;
        left: 0;
        top: 210px;
        z-index: 2;
        display: flex;
        justify-content: center;
        padding: 0;
        margin-right: 15%;
        margin-bottom: 1rem;
        margin-left: 15%;
    }
}

@media only screen and (max-width: 560px) {
    .carousel-indicators {
        position: absolute;
        right: 30px;
        bottom: 0;
        left: 0;
        top: 225px;
        z-index: 2;
        display: flex;
        justify-content: center;
        padding: 0;
        margin-right: 15%;
        margin-bottom: 1rem;
        margin-left: 15%;
    }
}

@media only screen and (max-width: 425px) {
    .carousel-indicators {
        position: absolute;
        right: 30px;
        bottom: 0;
        left: 0;
        top: 265px;
        z-index: 2;
        display: flex;
        justify-content: center;
        padding: 0;
        margin-right: 15%;
        margin-bottom: 1rem;
        margin-left: 15%;
    }
}

@media only screen and (max-width: 375px) {
    .carousel-indicators {
        position: absolute;
        right: 30px;
        bottom: 0;
        left: 0;
        top: 311px;
        z-index: 2;
        display: flex;
        justify-content: center;
        padding: 0;
        margin-right: 15%;
        margin-bottom: 1rem;
        margin-left: 15%;
    }
}

.pt-20-p {
    padding-top: 20px;
}

@media only screen and (max-width: 1199px) {
    .pt-20-p {
        padding-top: 0px;
    }
}

.align-item-p {
    align-items: center;
}

@media only screen and (max-width: 1199px) {
    .align-item-p {
        align-items: flex-start;
    }
}

.w-90 {
    width: 90%;
}

.align-end {
    justify-content: end;
}

@media only screen and (max-width: 1199px) {
    .align-end {
        justify-content: center;
    }
}

/*------------------------------------------------- HOME PAGE CSS ----------------------------------------------------------- */

/*------------------------------------------------- FOOTER CSS STARTS --------------------------------------------------------- */

ul {
    list-style-type: none;
    margin: 0;
    padding: 0;
}

hr {
    border-width: 3px;
}

/* .card {
    padding: 2% 7%;
} */

.social>i {
    padding: 1%;
    font-size: 15px;
}

.social>i:hover {
    color: #286fb4;
    cursor: pointer;
}

.policy>div {
    padding: 4px;
}

.heading {
    color: black;
}

.divider {
    border-top: 1px solid #e0e0e2;
}

.logo {
    height: 50px;
}

.icon-height {
    height: 30px;
}

.flex {
    display: flex;
    padding-top: 5px;
}

.padding-left {
    padding: 0px 0px 0px 11px;
}

.height {
    height: 30px;
}

.font-size20 {
    font-size: 20px;
}

/*------------------------------------------------- FOOTER CSS ENDS --------------------------------------------------------- */

/* FAQ'S SECTION  */

.faq-accordion .faq-heading {
    font-weight: 400;
    font-size: 30px;
}

.faq-heading {
    line-height: 30px;
    color: #3c3c3c;
    align-items: start;
}

.faq-div p {
    line-height: 24px;
    padding: 10px 20px 10px 20px;
    margin-top: 2px;
}

.mb-3-para p {
    margin-bottom: 0rem !important;
}

.faq-accordion {
    display: flex;
    flex-direction: column;

}

.faq-accordion button {
    text-decoration: none !important;
}

.faq-btn:first-child:active,
:not(.faq-btn-check)+.faq-btn:active {
    background-color: white !important;
}

.faq-accordion a {
    text-decoration: none !important;
}

.faq-card-header {
    display: flex;
    background-color: #fffdf6;
    color: #717171;
    cursor: pointer;
    align-items: self-start;
    padding: 20px;
}

.faq-para {
    box-shadow: 3px 5px 14px 0px rgba(0, 0, 0, 0.1);
}

.btn[aria-expanded="true"] {
    background-color: #e13362;
}

.btn[aria-expanded="true"] .faq-heading {
    color: white;
}

.btn[aria-expanded="true"]:hover {
    background-color: #e13362;
}

.btn[aria-expanded="true"] .faq-card-header {
    background-color: transparent;
}

@keyframes fade-in {
    from {
        opacity: 0;
    }

    to {
        opacity: 1;
    }
}

.bg-faq {
    background: #fef9e8;
}

/* FAQ'S SECTION  */

.yellow-btn {
    background: #f2d563;
    box-shadow: 0px 4px 4px 0px #efe0a391;
}
.yellow-btn:hover,.yellow-btn:focus {
    background: #ffd52e;
}

/* FEATURES PAGE CSS  */
.show1 {
    display: block;
}

@media only screen and (max-width: 991px) {
    .show1 {
        display: none !important;
    }
}

.hide1 {
    display: none;
}

@media only screen and (max-width: 991px) {
    .hide1 {
        display: block;
    }
}

.p-t-feature {
    padding: 150px;
}

@media only screen and (max-width: 1299px) {
    .p-t-feature {
        padding: 65px;
    }
}

@media only screen and (max-width: 1199px) {
    .p-t-feature {
        padding: 50px;
    }
}

@media only screen and (max-width: 991px) {
    .p-t-feature {
        padding: 10px 5px 30px 5px;
    }
}

@media only screen and (max-width: 425px) {
    .p-t-feature {
        padding: 20px 0px 20px 0px;
    }
}

.p-t-video {
    padding: 200px 50px 200px 50px;
}

@media only screen and (max-width: 1199px) {
    .p-t-video {
        padding: 130px 50px 150px 50px;
    }
}

@media only screen and (max-width: 991px) {
    .p-t-video {
        padding: 130px 50px 150px 50px;
    }
}

.b-color {
    border: 1px solid #dfdfdf;
}

/* VIDEO CSS  */

/* video css  */

.about-expert {
    text-align: center;
    position: relative;
}

.about-expert .thumbnail {
    position: relative;
    display: inline-block;
    border-radius: 40px;
    /* box-shadow: 0px 17px 26px 0px rgba(0, 0, 0, 0.2); */
}

.about-expert .thumbnail .popup-video {
    color: #e13362;
    text-align: center;
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    -webkit-transform: translateY(-50%);
    -ms-transform: translateY(-50%);
    transform: translateY(-50%);
}

@media only screen and (max-width: 991px) {
    .popup-video {
        left: 355px;
        top: 65% !important;
    }
}

.about-expert .thumbnail .popup-video .play-btn:hover {
    background-color: var(--color-primary);
}

@media only screen and (max-width: 991px) {
    .about-expert .thumbnail .popup-video .play-btn {
        height: 120px;
        width: 50px !important;
        font-size: 28px;
    }
}

/* @media only screen and (max-width: 767px) {
    .about-expert .thumbnail .popup-video .play-btn {
        height: 80px;
        width: 80px;
        font-size: 24px;
    }
} */

@media only screen and (max-width: 1199px) {
    .about-expert .frame-shape {
        display: none;
    }
}

.about-expert .frame-shape .shape {
    position: absolute;
}

.about-expert .frame-shape .shape.shape-1 {
    left: -10px;
    bottom: 75px;
}

.about-expert .frame-shape .shape.shape-2 {
    bottom: 190px;
    right: -30px;
}

.thumbnail paralax-image {
    background-color: #ffffff00;
    height: 650px;
}

.about-expert .thumbnail .popup-video {
    position: relative;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.about-expert .thumbnail .popup-video .play-btn {
    height: 150px;
    width: 550px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    background-color: #02000000;
    border-radius: 50%;
    margin: 0 auto;
    font-size: 32px;
    color: var(--color-white);
    transition: var(--transition);
}

.about-expert .thumbnail .popup-video {
    text-align: center;
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    -webkit-transform: translateY(-50%);
    -ms-transform: translateY(-50%);
    transform: translateY(-50%);
}

@media only screen and (max-width: 991px) {
    .about-expert .thumbnail .popup-video .play-btn {
        height: 120px;
        width: 120px;
        font-size: 28px;
    }
}

@media only screen and (max-width: 767px) {
    .about-expert .thumbnail .popup-video .play-btn {
        height: 80px;
        width: 80px;
        font-size: 24px;
    }
}

.blog-grid .post-thumbnail .popup-video {
    text-align: center;
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    -webkit-transform: translateY(-50%);
    -ms-transform: translateY(-50%);
    transform: translateY(-50%);
}

.blog-grid .post-thumbnail .popup-video .play-btn {
    height: 150px;
    width: 150px;
    display: flex;
    -webkit-box-align: center;
    -webkit-align-items: center;
    -ms-flex-align: center;
    align-items: center;
    justify-content: center;
    background: -webkit-linear-gradient(top,
            rgba(32, 38, 43, 0.8) 0%,
            #20262b 100%);
    background: linear-gradient(180deg, rgba(32, 38, 43, 0.8) 0%, #20262b 100%);
    border-radius: 50%;
    margin: 0 auto;
    font-size: 32px;
    color: var(--color-white);
    transition: var(--transition);
}

.blog-grid .post-thumbnail .popup-video .play-btn:hover {
    background-color: var(--color-primary);
}

@media only screen and (max-width: 1199px) {
    .blog-grid .post-thumbnail .popup-video .play-btn {
        height: 100px;
        width: 100px;
        font-size: 26px;
    }
}

@media only screen and (max-width: 575px) {
    .blog-grid .post-thumbnail .popup-video .play-btn {
        height: 80px;
        width: 80px;
        font-size: 20px;
    }
}

@media only screen and (max-width: 991px) {
    .waves-block {
        top: -14% !important;
        left: 42% !important;
    }
}

@media only screen and (max-width: 950px) {
    .waves-block {
        top: -14% !important;
        left: 41.7% !important;
    }
}

@media only screen and (max-width: 900px) {
    .waves-block {
        top: -14% !important;
        left: 41% !important;
    }
}

@media only screen and (max-width: 840px) {
    .waves-block {
        top: -14% !important;
        left: 40.5% !important;
    }
}

@media only screen and (max-width: 790px) {
    .waves-block {
        top: -14% !important;
        left: 40% !important;
    }
}

@media only screen and (max-width: 767px) {
    .waves-block {
        top: -45% !important;
        left: 39.5% !important;
    }
}

@media only screen and (max-width: 710px) {
    .waves-block {
        top: -45% !important;
        left: 39% !important;
    }
}

@media only screen and (max-width: 650px) {
    .waves-block {
        top: -45% !important;
        left: 38% !important;
    }
}

@media only screen and (max-width: 600px) {
    .waves-block {
        top: -45% !important;
        left: 37% !important;
    }
}

@media only screen and (max-width: 575px) {
    .waves-block {
        top: -45% !important;
        left: 36% !important;
    }
}

@media only screen and (max-width: 550px) {
    .waves-block {
        top: -45% !important;
        left: 35% !important;
    }
}

@media only screen and (max-width: 525px) {
    .waves-block {
        top: -45% !important;
        left: 34% !important;
    }
}

@media only screen and (max-width: 500px) {
    .waves-block {
        top: -45% !important;
        left: 33% !important;
    }
}

@media only screen and (max-width: 475px) {
    .waves-block {
        top: -45% !important;
        left: 32% !important;
    }
}

@media only screen and (max-width: 450px) {
    .waves-block {
        top: -45% !important;
        left: 31% !important;
    }
}

@media only screen and (max-width: 425px) {
    .waves-block {
        top: -45% !important;
        left: 30% !important;
    }
}

@media only screen and (max-width: 400px) {
    .waves-block {
        top: -45% !important;
        left: 29% !important;
    }
}

@media only screen and (max-width: 375px) {
    .waves-block {
        top: -45% !important;
        left: 27% !important;
    }
}

@media only screen and (max-width: 350px) {
    .waves-block {
        top: -45% !important;
        left: 25% !important;
    }
}

@media only screen and (max-width: 325px) {
    .waves-block {
        top: -45% !important;
        left: 23% !important;
    }
}

.wave-1 {
    animation-delay: 0s;
    background-color: #000000;
}

.wave-2 {
    animation-delay: 1s;
}

.wave-3 {
    animation-delay: 2s;
}

@keyframes waves {
    0% {
        transform: scale(0.2);
        opacity: 0;
    }

    50% {
        opacity: 0.9;
    }

    100% {
        transform: scale(0.9);
        opacity: 0;
    }
}

.play-btn {
    height: 50px;
    width: 50px;
    line-height: 50px;
    text-align: center;
    border-radius: 100%;
    color: #000000;
    /* Set the color of the play button */
    z-index: 999;
    position: relative;
    display: inline-block;
    overflow: hidden;
}

.play-btn i {
    font-size: 24px;
    background: black;
    border-radius: 50px;
    width: 50px;
    padding: 12px;
    /* margin-top: 26px; */
    height: 50px;
}

.waves-block {
    position: absolute;
    width: 150px;
    height: 150px;
    background: rgba(0, 0, 0, 0.3);
    opacity: 0;
    border-radius: 100%;
    right: 322px;
    z-index: -1;
    animation: waves 2.5s ease-in-out infinite;
}

@media only screen and (max-width: 1399px) {
    .waves-block {
        right: 233px;
    }
}

@media only screen and (max-width: 1299px) {
    .waves-block {
        right: 233px;
    }
}

@media only screen and (max-width: 1199px) {
    .waves-block {
        right: 143px;
    }
}

@media only screen and (max-width: 1024px) {
    .waves-block {
        right: 143px;
    }
}

@media only screen and (max-width: 991px) {
    .waves-block {
        right: 346px;
        top: -13px;
    }
}

/* VIDEO CSS  */

/* Price Section Start */
.price-para {
    font-size: 13px;
}

.price-listing {
    list-style-type: none;
    padding-left: 0;
}

.price-grid {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 1rem;
}

.price-EM-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 1rem;
}

.price-WMS-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
}

.price-items-1 {
    padding-top: 27px;
    margin-top: 20px !important;
    padding-bottom: 27px;
    padding-left: 23px;
    padding-right: 23px;
    border-radius: 16px;
    background: #f8fafb;
}

.price-items-1-btn {
    border-radius: 5px;
    border: 1px solid #ebebeb;
    background: #ebebeb;
}

.price-items-1-btn:hover {
    border-radius: 5px;
    border: 1px solid #d3d3d3;
    background: #d3d3d3;
}

.price-items-2 {
    padding-top: 27px;
    margin-top: 20px !important;
    padding-bottom: 27px;
    padding-left: 23px;
    padding-right: 23px;
    border-radius: 16px;
    background: #f6f2fa;
}

.price-items-2-btn {
    border-radius: 5px;
    color: #fff;
    background: #e13362;
    border: 1px solid #e13362;
}

.price-items-2-btn:hover {
    border-radius: 5px;
    color: #fff;
    background: #d32856;
    border: 1px solid #d32856;
}

.price-items-3 {
    padding-top: 27px;
    margin-top: 20px !important;
    padding-bottom: 27px;
    padding-left: 23px;
    padding-right: 23px;
    border-radius: 16px;
    background: #fef9e8;
}

.price-items-3-btn {
    border-radius: 5px;
    color: #000000;
    background: #f2d563;
    border: 1px solid #f2d563;
}

.price-items-3-btn:hover {
    border-radius: 5px;
    color: #000000;
    background: #f0d04f;
    border: 1px solid #f0d04f;
}

.price-list {
    font-size: 14px;
}

.most-popular-blue {
    border-radius: 10px 10px 10px 0px;
    background: #e13362;
    color: white;
    border: 1px solid #e13362;
    position: absolute;
    z-index: -1;
    margin-top: -60px;
    margin-left: -23px;
}

.most-popular-purple {
    border-radius: 10px 10px 10px 0px;
    background: #2d2db0;
    color: white;
    border: 1px solid #2d2db0;
    position: absolute;
    z-index: -1;
    margin-top: -60px;
    margin-left: -23px;
}

.most-popular-grey {
    border-radius: 10px 10px 10px 0px;
    background: #675f72;
    color: white;
    border: 1px solid #675f72;
    position: absolute;
    z-index: -1;
    margin-top: -60px;
    margin-left: -23px;
}

@media only screen and (max-width: 1199px) {
    .price-EM-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .price-EM-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .price-EM-div {
        display: none;
    }
}

@media only screen and (max-width: 991px) {
    .price-grid {
        grid-template-columns: repeat(3, 1fr);
    }

    .price-WMS-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media only screen and (max-width: 768px) {
    .price-grid {
        grid-template-columns: repeat(2, 1fr);
        row-gap: 2.5rem;
    }

    .price-WMS-grid {
        grid-template-columns: repeat(2, 1fr);
        row-gap: 2.5rem;
    }
}

@media only screen and (max-width: 566px) {
    .price-grid {
        grid-template-columns: repeat(1, 1fr);
        row-gap: 2.5rem;
    }

    .price-WMS-grid {
        grid-template-columns: repeat(1, 1fr);
        row-gap: 2.5rem;
    }

    .price-EM-grid {
        grid-template-columns: repeat(1, 1fr);
        row-gap: 2.5rem;
    }
}

.form-check-input:checked {
    /* background-color: #f2d563; */
    /* toggl switch */
    border-color: #0d6efd00;
}

.form-check-input:focus {
    border-color: #86b6fe00;
    outline: 0;
    box-shadow: 0 0 0 0.25rem rgba(13, 109, 253, 0);
}

.form-range {
    width: 100%;
    height: 0rem;
    padding: 0;
    background-color: transparent;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
}

.form-switch .form-check-input {
    --bs-form-switch-bg: url(data:image/svg + xml,
 %3csvgxmlns="http://www.w3.org/2000/svg" viewBox="-4 -4 8 8" %3e%3ccircler="3" fill="rgba%280, 0, 0, 0.25%29" /%3e%3c/svg%3e);
    width: 60px;
    height: 25px;
    margin-left: -2.5em;
    background-image: var(--bs-form-switch-bg);
    background-position: left center;
    border-radius: 2em;
    transition: background-position 0.15s ease-in-out;
}

.d-form-8 {
    display: grid;
    grid-template-columns: repeat(8, auto);
}

@media only screen and (max-width: 600px) {
    .d-form-8 {
        display: grid;
        grid-template-columns: repeat(4, auto);
    }
}

.d-form-10 {
    display: grid;
    grid-template-columns: repeat(10, auto);
}

@media only screen and (max-width: 600px) {
    .d-form-10 {
        display: grid;
        grid-template-columns: repeat(4, auto);
    }
}

option:hover {
    background-color: #000000 !important;
}

/* Price Section End */

/* contact form  */

.justify-res {
    justify-content: end;
}

@media only screen and (max-width: 600px) {

    .justify-res {
        justify-content: start;
    }

}

.email-div {
    background: #ffffff;
    border-radius: 20px;
    width: 35vw;
}

.email-center {
    align-items: center;
}

.bg-light-blue {
    background: #e0efff3b;
}

.img-h-500 {
    height: 500px;
}

/* contact text-flelds  */


.input-form-style {
    width: 100%;
    padding: 12px;
    border: 1px solid #ccc;
    border-radius: 8px;
    border-style: none none solid none;
    background: #f0f0f000;
    box-sizing: border-box;
    margin-top: 6px;
    margin-bottom: 16px;
    resize: vertical;
    outline: none !important;
}

.container1 {
    border-radius: 20px;
    background-color: #f6f2fa;
    padding: 60px;
    border: 1px solid #000000;
    width: 645px;
}

@media screen and (max-width: 768px) {
    .container1 {
        padding: 36px;
        width: 450px;
    }
}

@media only screen and (max-width: 320px) {
    .container1 {
        padding: 15px;
        width: 290px !important;
    }
}

@media only screen and (max-width: 425px) {
    .container1 {
        padding: 15px;
        width: 330px;
    }
}

.fly-img {
    background-repeat: no-repeat;
    background-size: 200px;
    background-position: 88% 50%;
}

@media only screen and (max-width: 1399px) {
    .fly-img {
        background-repeat: no-repeat;
        background-size: 200px;
        background-position: 95% 50%;
    }
}

@media only screen and (max-width: 1199px) {
    .fly-img {
        background-repeat: no-repeat;
        background-size: 200px;
        background-position: 105% 50%;
    }
}

@media only screen and (max-width: 991px) {
    .fly-img {
        background-repeat: no-repeat;
        background-size: 0px;
        background-position: 109% 50%;
    }
}

input::placeholder {
    color: #938d8d;
    opacity: 1;
}

input:focus {
    color: #000000 !important;
}

:focus {
    outline: 0px solid transparent !important;
}

textarea {
    border: none;
    border-bottom: 1px solid #938d8d;
    outline: none;
    transition: border-bottom 0.3s;
}

textarea::placeholder {
    color: #938d8d;
}

select:focus {
    outline: none;
}

.contact-info {
    font-size: 18px;
}

@media only screen and (max-width: 1399px) {
    .contact-info {
        font-size: 18px;
    }
}

@media only screen and (max-width: 1199px) {
    .contact-info {
        font-size: 18px;
    }
}

@media only screen and (max-width: 991px) {
    .contact-info {
        font-size: 18px;
    }
}

.padding-contact {
    padding: 4rem;
}

@media only screen and (max-width: 1399px) {
    .padding-contact {
        padding: 2rem;
    }
}

@media only screen and (max-width: 1199px) {
    .padding-contact {
        padding: 1rem 0rem 1rem 1rem;
    }
}

@media only screen and (max-width: 991px) {
    .padding-contact {
        padding: 3rem;
    }
}

@media only screen and (max-width: 880px) {
    .padding-contact {
        padding: 1rem 0.5rem 1rem 2rem;
    }
}

.gap-contact {
    gap: 2rem;
}

@media only screen and (max-width: 1199px) {
    .gap-contact {
        gap: 1rem;
    }
}

@media only screen and (max-width: 741px) {
    .gap-contact {
        gap: 0rem;
    }
}

/* contact text-flelds  */
/* HELP Page CSS */


@media only screen and (max-width: 360px) {
    .Help-top-btn {
        flex-direction: column;
        width: max-content;
        align-items: center;
        margin: 0 auto;
    }
}

.pricing-card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border: none;
    border-radius: 15px;
}

.pricing-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.feature-list li {
    margin-bottom: 0.8rem;
    color: #6c757d;
}

.popular-badge {
    position: absolute;
    top: -12px;
    right: 20px;
    border-radius: 20px;
}

.gradient-custom {
    background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
}
