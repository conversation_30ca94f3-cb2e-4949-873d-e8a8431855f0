<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\{
    <PERSON><PERSON><PERSON><PERSON>roll<PERSON>,
    Order<PERSON>ontroller,
    <PERSON>tm<PERSON><PERSON>roller,
    <PERSON>fer<PERSON><PERSON>roller,
    Paypal<PERSON><PERSON>roller,
    <PERSON>e<PERSON><PERSON>roller,
    Add<PERSON>und<PERSON><PERSON>roller,
    Address<PERSON><PERSON>roller,
    Contact<PERSON><PERSON>roller,
    Invoice<PERSON><PERSON>roller,
    Li<PERSON><PERSON><PERSON>roller,
    PaymentController,
    PricingController,
    ProductController,
    SitemapController,
    CC<PERSON><PERSON>ue<PERSON>ontroller,
    razor<PERSON>ayController,
    instaMojoController,
    P<PERSON><PERSON><PERSON><PERSON>roller,
    CareerFormController,
    RingcasterController,
    AlreadyPaidController,
    userProfileController,
    UserResourceController,
    KnowladgeBaseController,
    SupportTicketController,
    Bill<PERSON><PERSON><PERSON>ress<PERSON><PERSON>roller,
    GraphicProjectController,
    InvoicePaymentController,
    LeadQuotationRequestController,
    ReviewController,
    brandController,
};
use App\Http\Controllers\Graphic\{AnnotationController, DesignController,  ProjectController as GProjectController};
use Illuminate\Support\Facades\Redirect;
/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

Route::middleware('checkHost')->group(function () {
    $host = request()->getHttpHost();
    if ($host == 'localhost:8001' || $host == 'wabhai.com' || $host == 'wabhai.orkia.in') {
        //wabhai.com
        Route::prefix('/')->name('wabhai.')->group(function () {
            Route::view('', 'wabhai.home')->name('home');
            Route::view('integration', 'wabhai.integration')->name('integration');
            Route::view('pricing', 'wabhai.pricing')->name('price');
            // Route::view('help', 'wabhai.help')->name('help');
            // Route::view('blog', 'wabhai.blog')->name('blog');
            Route::view('features', 'wabhai.features')->name('features');
            Route::view('contact', 'wabhai.contact')->name('contact');
        });
        Route::view('privacy-policy', 'wabhai.privacypolicy')->name('privacypolicy');
        Route::view('terms-condition', 'wabhai.terms_condition')->name('terms_condition');
    } elseif ($host == 'localhost:8002' || $host == 'primailer.com' || $host == 'primailer.orkia.in') {
        //https://primailer.com/
        Route::prefix('/')->name('primailer.')->group(function () {
            Route::view('', 'primailer.home')->name('home');
            Route::view('pricing', 'primailer.pricing')->name('price');
            // Route::view('blog', 'primailer.blog')->name('blog');
            Route::view('features', 'primailer.features')->name('features');
            Route::view('email-data-extractor', 'primailer.email-data-extractor')->name('email-data-extractor');
            Route::view('primailer-validator', 'primailer.primailer-validator')->name('primailer-validator');
            Route::view('contact', 'primailer.contact')->name('contact');
        });
        Route::view('privacy-policy', 'primailer.PrivacyPolicy')->name('privacypolicy');
        Route::view('terms-condition', 'primailer.terms_conditon')->name('terms_condition');
    } elseif ($host == 'localhost:8003' || $host == 'stickyfirst.com' || $host == 'stickyfirst.orkia.in') {
        //https://stickyfirst.com/
        Route::prefix('/')->name('stickyfirst.')->group(function () {
            Route::view('', 'stickyfirst.home')->name('home');
            // Route::view('blog', 'stickyfirst.blog')->name('blog');
            Route::view('organic/blog-content-writing', 'stickyfirst.blog-content-writing')->name('organic.blog-content-writing');
            Route::view('about-us', 'stickyfirst.about-digital-marketing')->name('about-digital-marketing');
            Route::view('contact', 'stickyfirst.contact')->name('contact');
            Route::view('get-a-quote', 'stickyfirst.get-a-quote')->name('get-a-quote');
            Route::view('development/mobile-app-development', 'stickyfirst.mobile-app-development')->name('development.mobile-app-development');
            Route::view('paid-ads/display-ads', 'stickyfirst.display-ads')->name('paid-ads.display-ads');
            Route::view('development', 'stickyfirst.development')->name('development');
            Route::view('organic', 'stickyfirst.organic')->name('organic');
            Route::view('paid-ads', 'stickyfirst.paid-ads')->name('paid-ads');
            Route::view('paid-ads/search-ads', 'stickyfirst.search-ads')->name('paid-ads.search-ads');
            Route::view('paid-ads/shopping-ads', 'stickyfirst.shopping-ads')->name('paid-ads.shopping-ads');
            Route::view('paid-ads/social-media-ads', 'stickyfirst.social-media-ads')->name('paid-ads.social-media-ads');
            Route::view('organic/search-engine', 'stickyfirst.search-engine')->name('organic.search-engine');
            Route::view('organic/social-media-management', 'stickyfirst.social-media-management')->name('organic.social-media-management');
            Route::view('development/web-development', 'stickyfirst.web-development')->name('development.web-development');
            Route::view('paid-ads/youtube-ads', 'stickyfirst.youtube-ads')->name('paid-ads.youtube-ads');
            Route::view('paid-ads/gmail-ads', 'stickyfirst.gmail-ads')->name('paid-ads.gmail-ads');
        });
        Route::view('privacy-policy', 'stickyfirst.privacypolicy')->name('privacypolicy');
        Route::view('terms-condition', 'stickyfirst.terms_condition')->name('terms_condition');
    } elseif ($host == 'localhost:8004' || $host == 'ringcaster.com' || $host == 'ringcaster.orkia.in') {
        Route::prefix('/')->name('ringcaster.')->group(function () {
            Route::view('', 'ringcaster.home')->name('home');
            Route::view('about-us', 'ringcaster.about-us')->name('about-us');
            Route::view('how-it-works', 'ringcaster.how-it-works')->name('how-it-works');
            Route::view('pricing', 'ringcaster.pricing')->name('price');
            Route::view('why-choose-us', 'ringcaster.why-choose-us')->name('why-choose-us');
            Route::view('knowledge-base', 'ringcaster.knowledge-base')->name('knowledge-base');
            // Route::view('blog', 'ringcaster.blog')->name('blog');
            Route::view('features', 'ringcaster.features')->name('features');
            Route::view('contact', 'ringcaster.contact')->name('contact');
        });
        Route::view('privacy-policy', 'ringcaster.privacy-policy')->name('privacypolicy');
        Route::view('terms-condition', 'ringcaster.terms-condition')->name('terms_condition');
    } elseif ($host == 'localhost:8005' || $host == 'pixayogi.com' || $host == 'pixayogi.orkia.in') {
        Route::prefix('/')->name('pixayogi.')->group(function () {
            Route::view('', 'pixayogi.home')->name('home');
            Route::view('work-samples', 'pixayogi.work-samples')->name('work-samples');
            // Route::view('help', 'pixayogi.help')->name('help');
            // Route::view('blog', 'pixayogi.blog')->name('blog');
            Route::view('features', 'pixayogi.features')->name('features');
            Route::view('graphic-designer', 'pixayogi.graphic-designer')->name('graphic-designer');
            Route::view('presentation-design', 'pixayogi.presentation-design')->name('presentation-design');
            Route::view('motion-graphics', 'pixayogi.motion-graphics')->name('motion-graphics');
            Route::view('custom-illustration', 'pixayogi.custom-illustration')->name('custom-illustration');
            Route::view('contact', 'pixayogi.contact')->name('contact');
            Route::view('why-pixayogi', 'pixayogi.why-pixayogi')->name('why-pixayogi');
            Route::view('how-it-works', 'pixayogi.how-it-works')->name('how-it-works');
            Route::view('plans', 'pixayogi.plans')->name('plans');
        });
        // Route::prefix('/')->name('pixayogi.')->group(function () {
        //     Route::view('graphic-designer', 'pixayogi.graphic-designer')->name('graphic-designer');
        // });



        Route::view('privacy-policy', 'pixayogi.privacypolicy')->name('privacypolicy');
        Route::view('terms-condition', 'pixayogi.termscondition')->name('terms_condition');
    } elseif ($host == 'localhost:8006' || $host == 'rokdi.com' || $host == 'rokdi.orkia.in') {
        //https://rokdi.com/

        Route::prefix('/')->name('rokdi.')->group(function () {
            Route::view('', 'rokdi.home')->name('home');
            Route::view('pricing', 'rokdi.pricing')->name('pricing');
            // Route::view('help', 'rokdi.help')->name('help');
            // Route::view('blog', 'rokdi.blog')->name('blog');
            Route::view('features', 'rokdi.features')->name('features');
            Route::view('contact', 'rokdi.contact')->name('contact');
        });
        Route::view('privacy-policy', 'rokdi.privacypolicy')->name('privacypolicy');
        Route::view('terms-condition', 'rokdi.termscondition')->name('terms_condition');
    } elseif ($host == 'localhost:8007' || $host == 'androsms.com' || $host == 'androsms.orkia.in') {
        //https://androsms.com/
        Route::prefix('/')->name('androsms.')->group(function () {
            Route::view('', 'androsms.home')->name('home');
            Route::view('pricing', 'androsms.pricing')->name('price');
            // Route::view('help', 'androsms.help')->name('help');
            // Route::view('blog', 'androsms.blog')->name('blog');
            Route::view('features', 'androsms.features')->name('features');
            Route::view('contact', 'androsms.contact')->name('contact');
        });
        Route::view('privacy-policy', 'androsms.privacypolicy')->name('privacypolicy');
        Route::view('terms-condition', 'androsms.termscondition')->name('terms_condition');
    } elseif ($host == 'localhost:8008' || $host == 'clatos.com' || $host == 'clatos.orkia.in') {
        //https://clatos.com/
        Route::prefix('/')->name('clatos.')->group(function () {
            Route::view('', 'clatos.home')->name('home');
            Route::view('about-us', 'clatos.about-us')->name('about-us');
            Route::view('pricing', 'clatos.pricing')->name('pricing');
            // Route::view('help', 'clatos.help')->name('help');
            // Route::view('blog', 'clatos.blog')->name('blog');
            Route::view('integration', 'clatos.integration')->name('integration');
            Route::view('features', 'clatos.features')->name('features');
            Route::view('why-clatos', 'clatos.why-clatos')->name('why-clatos');
            Route::view('IVR', 'clatos.IVR')->name('IVR');
            Route::view('contact', 'clatos.contact')->name('contact');
        });
        Route::view('privacy-policy', 'clatos.privacypolicy')->name('privacypolicy');
        Route::view('terms-condition', 'clatos.terms_condition')->name('terms_condition');
    } elseif ($host == 'localhost:8009' || $host == 'rapbooster.com' || $host == 'rapbooster.orkia.in') {
        Route::view('terms-condition', 'rapbooster.terms_conditon')->name('terms_condition');
        Route::view('privacy-policy', 'rapbooster.privacypolicy')->name('privacypolicy');

        Route::prefix('/')->name('rapbooster.')->group(function () {
            Route::view('', 'rapbooster.home')->name('home');
            // Route::view('about-us', 'rapbooster.about-us')->name('about-us');
            // Route::view('blog', 'rapbooster.blog')->name('blog');
            Route::view('contact', 'rapbooster.contact')->name('contact');
            // Route::view('help', 'rapbooster.help')->name('help');
            // Route::view('pricing', 'rapbooster.pricing')->name('pricing');
            Route::view('advance', 'rapbooster.advance')->name('advance');
            Route::view('api', 'rapbooster.api')->name('api');
            Route::view('basic', 'rapbooster.basic')->name('basic');
            Route::view('sms-marketing', 'rapbooster.bulk-sms-marketing')->name('bulk-sms-marketing');
            Route::view('rcs-marketing', 'rapbooster.rcs-sms-marketing')->name('rcs-sms-marketing');
            Route::view('cloud', 'rapbooster.cloud')->name('cloud');
            Route::view('whatsapp-crm', 'rapbooster.CRM-software')->name('CRM-software');
            // Route::view('crm-software', 'rapbooster.CRM-software')->name('CRM-software');
            Route::view('email-marketing', 'rapbooster.email-marketing')->name('email-marketing');
            Route::view('lead-extractor', 'rapbooster.flipy-data-extractor')->name('flipy-data-extractor');
            Route::view('whatsapp-marketing', 'rapbooster.whatsapp-marketing-software')->name('whatsapp-marketing-software');
            Route::view('voice-broadcast', 'rapbooster.voice-broadcast')->name('voice-broadcast');
            Route::view('whatsapp-api', 'rapbooster.whatsapp-api')->name('whatsapp-api');
            Route::view('billing-software', 'rapbooster.billing-software')->name('billing-software');
            Route::view('ivr-cloud-telephone', 'rapbooster.ivr-cloud-telephone')->name('ivr-cloud-telephone');
            Route::view('graphic-designing', 'rapbooster.graphic-designing')->name('graphic-designing');
            Route::view('website-designing', 'rapbooster.website-designing')->name('website-designing');
            Route::view('e-commerce-development', 'rapbooster.e-commerce-development')->name('e-commerce-development');
            Route::view('mobile-app-development', 'rapbooster.mobile-app-development')->name('mobile-app-development');
            Route::view('search-engine-optimization', 'rapbooster.search-engine-optimization')->name('search-engine-optimization');
            Route::view('ppc-service', 'rapbooster.ppc-service')->name('ppc-service');
            Route::view('content-marketing', 'rapbooster.content-marketing')->name('content-marketing');
            Route::view('social-media-marketing', 'rapbooster.social-media-marketing')->name('social-media-marketing');
            Route::view('explainer-video-making', 'rapbooster.explainer-video-making')->name('explainer-video-making');
        });
    } elseif ($host == 'localhost:8010' || $host == 'dunesfactory.com' || $host == 'dunesfactory.orkia.in') {
        Route::prefix('/')->name('dunesfactory.')->group(function () {
            Route::view('', 'dunesfactory.home')->name('home');
            Route::view('about-us', 'dunesfactory.about-us')->name('about-us');
            // Route::view('blog', 'dunesfactory.blog')->name('blog');
            Route::view('contact', 'dunesfactory.contact')->name('contact');
            // Route::view('help', 'dunesfactory.help')->name('help');
            Route::view('profile', 'dunesfactory.profile')->name('profile');
            Route::view('work-with-us', 'dunesfactory.work-with-us')->name('work-with-us');
        });
        Route::view('privacy-policy', 'dunesfactory.privacypolicy')->name('privacypolicy');
        Route::view('terms-condition', 'dunesfactory.terms_condition')->name('terms_condition');
    } elseif ($host == 'localhost:8019' || $host == 'texaplus.com' || $host == 'texaplus.orkia.in') {
        //https://texaplus.com/
        Route::prefix('/')->name('texaplus.')->group(function () {
            Route::view('', 'texaplus.home')->name('home');
            Route::view('pricing', 'texaplus.pricing')->name('price');
            // Route::view('help', 'texaplus.help')->name('help');
            // Route::view('blog', 'texaplus.blog')->name('blog');
            Route::view('features', 'texaplus.features')->name('features');
            Route::view('contact', 'texaplus.contact')->name('contact');
        });
        Route::view('privacy-policy', 'texaplus.privacypolicy')->name('privacypolicy');
        Route::view('terms-condition', 'texaplus.termscondition')->name('terms_condition');
    } elseif ($host == 'localhost:8011' || $host == 'andro.orkia.in') {

        Route::prefix('/')->name('andro.')->group(function () {

            Route::view('', 'andro.home')->name('home');
            Route::view('contact', 'andro.contact')->name('contact');
            Route::view('features', 'andro.features')->name('features');
            Route::view('how-it-works', 'andro.howitworks')->name('howitworks');
            Route::view('pricing', 'andro.pricing')->name('pricing');
            Route::view('privacypolicy', 'andro.privacypolicy')->name('privacyPolicy');
            Route::view('termsconditions', 'andro.termscondition')->name('termsAndConditions');
            Route::view('disclaimer', 'andro.disclaimer')->name('disclaimer');
            Route::view('testimonials', 'andro.testimonials')->name('testimonials');


            // Route::prefix('sms')->name('sms.')->group(function () {
            //     Route::view('/', 'andro.sms.home')->name('home');
            //     Route::view('/features', 'andro.sms.features')->name('features');
            //     Route::view('/how-it-works', 'andro.sms.howitworks')->name('howitworks');
            //     Route::view('/pricing', 'andro.sms.pricing')->name('pricing');
            // });
            // Route::prefix('rcs')->name('rcs.')->group(function () {
            //     Route::view('/', 'andro.rcs.home')->name('home');
            //     Route::view('/features', 'andro.rcs.features')->name('features');
            //     Route::view('/how-it-works', 'andro.rcs.howitworks')->name('howitworks');
            //     Route::view('/pricing', 'andro.rcs.pricing')->name('pricing');
            // });

            // Route::view('/transactional', 'andro.transactional.home')->name('transactional-home');
            // Route::view('/transactional/features', 'andro.transactional.features')->name('transactional-features');
            // Route::view('/transactional/howitworks', 'andro.transactional.howitworks')->name('transactional-howitworks');
            // Route::view('/promotional', 'andro.promotional.home')->name('promotional-home');
            // Route::view('/promotional/features', 'andro.promotional.features')->name('promotional-features');
            // Route::view('/promotional/howitworks', 'andro.promotional.howitworks')->name('promotional-howitworks');

            // Route::view('about-us', 'dunesfactory.about-us')->name('about-us');
            // Route::view('blog', 'dunesfactory.blog')->name('blog');
            // Route::view('contact', 'dunesfactory.contact')->name('contact');
            // Route::view('help', 'dunesfactory.help')->name('help');
            // Route::view('profile', 'dunesfactory.profile')->name('profile');
            // Route::view('work-with-us', 'dunesfactory.work-with-us')->name('work-with-us');
        });
        // Route::view('privacy-policy', 'dunesfactory.privacypolicy')->name('privacypolicy');
        // Route::view('terms-condition', 'dunesfactory.terms_condition')->name('terms_condition');
        Route::view('privacy-policy', 'andro.privacypolicy')->name('privacypolicy');
        Route::view('terms-condition', 'andro.termscondition')->name('terms_condition');
    } else {
        // return Redirect::to('https://dunesfactory.com/');
    }


    Route::middleware('auth')->group(function () {

        Route::get('shop/{category}/{categoryId}/product/{productVariant}', [ProductController::class, 'product'])->name('product.view');
        Route::post('cart/add/multiple', [CartController::class, 'cartAddMultiple'])->name('cartAddMultiple');

        Route::get('shop/{categoryName?}/{category}', [PricingController::class, 'pagePricing'])->name('categoryPricing');
        Route::controller(userProfileController::class)->name('user.')->group(function () {
            Route::get('orders', 'userOrders')->name('orders');
            Route::get('licences', 'userLicences')->name('licences');
            Route::get('invoices', 'userInvoices')->name('invoices');
            Route::get('transaction', 'userTransactions')->name('transactions');
            Route::get('profile/view', 'userProfile')->name('profile');
            Route::get('address', 'userAddress')->name('address');
            Route::get('support-ticket', 'userSupportTickets')->name('s.tickets');
            Route::post('profile/edit', 'edit')->name('edit');
            Route::post('profile/update', 'update')->name('update');
        });
        Route::controller(ReferController::class)->prefix('user/refer/')->name('user.refer.')->group(function () {
            Route::get('/', 'index')->name('index');
            Route::get('register', 'register')->name('register');
            Route::post('register', 'regSubmit')->name('register');
            Route::get('add', 'create')->name('create');
            Route::post('submit', 'referSubmit')->name('submit');
        });

        // Route::get('/refer', function () {
        //     return view('userpages.refer&Earn.index');
        // })->name('userpages.refer&Earn.index');

        // Route::get('/view', function () {
        //     return view('userpages.refer&Earn.view');
        // })->name('userpages.refer&Earn.view');

        // Route::get('/add', function () {
        //     return view('userpages.refer&Earn.add');
        // })->name('userpages.refer&Earn.add');

        // Route::get('/refer/affiliate', function () {
        //     return view('userpages.refer&Earn.affiliate');
        // })->name('userpages.refer&Earn.affiliate');

        Route::controller(AddFundController::class)->prefix('addBalance/')->name('addBalance.')->group(function () {
            Route::get('/', 'addFund')->name('payment');
            Route::get('chequePaid', 'chequePaid')->name('chequePaid');
            Route::get('neftPaid', 'neftPaid')->name('neftPaid');
            Route::get('qr', 'generateQr')->name('generateQr');
        });

        Route::controller(SupportTicketController::class)->group(function () {
            Route::get('/support-ticket/create', "CreateSupportTicket")->name('supportCreate');
            Route::post('/support-ticket/store', "store")->name('supportStore');
            Route::post('/support-ticket/status/{id}', "changeStatus")->name('supportStatus');
            Route::get('/support/view/{id}', 'view')->name('supportChat');
            Route::get('/show/ajax/ticket', 'ajaxShowTicket')->name('ajax.Show.Ticket');
            Route::post('support/ticket/store', 'store')->name('support.store');
            Route::post('support/chat/message', 'sendMessage')->name('support.chat.message');
        });

        Route::controller(PaymentController::class)->group(function () {
            // Route::get('invoice/details', 'invoiceDetails')->name('inv.details');
            Route::get('invoice/address', 'invoiceAddress')->name('inv.address');
            Route::get('invoice/payment', 'invoicePayment')->name('inv.payment');
        });

        Route::resource('billingAddress', BillingAddressController::class);
        Route::post('billing-address/update', [BillingAddressController::class, 'updateModel'])->name('billingAddress.modal.update');
        Route::post('/set-default', [BillingAddressController::class, "setasDefault"])->name("billingAddress.setasDefault");
        Route::resource('userProfile', UserResourceController::class)->except(['show', 'destroy', 'index']);
        Route::resource('quotation', LeadQuotationRequestController::class)->only(['create', 'store']);
        Route::post('store/quotation/modal', [LeadQuotationRequestController::class, 'modalStore'])->name('quotation.store.modal');

        Route::resource('graphic/brand', brandController::class)->names('brand');
        Route::delete('/brand/attachment/delete/{attach}', [brandController::class, 'destroyAttachment'])->name('delete.brand.attachment');

        // Route::controller(GraphicProjectController::class)->prefix('graphic/')->group(function () {
        //     Route::get('/projects', 'index')->name('graphic.projects');
        // });


        Route::prefix('graphic/')->name('graphic.')->group(function () {
            $baseViewPath = "userpages.projects.graphic.";
            // Route::get('/projects', function () use ($baseViewPath) {
            //     return view($baseViewPath . 'index');
            // })->name('projects');
            Route::get('/generate_request/{plan}', function () use ($baseViewPath) {
                return view($baseViewPath . 'generateRequest');
            })->name('generateRequest');
            Route::get('/website_request', function () use ($baseViewPath) {
                return view($baseViewPath . 'websiteRequest');
            })->name('websiteRequest');
            Route::get('/software_request', function () use ($baseViewPath) {
                return view($baseViewPath . 'softwareRequest');
            })->name('softwareRequest');
            Route::get('/seo_request', function () use ($baseViewPath) {
                return view($baseViewPath . 'seoRequest');
            })->name('seoRequest');
            Route::get('/ppc_request', function () use ($baseViewPath) {
                return view($baseViewPath . 'ppcRequest');
            })->name('ppcRequest');
            Route::get('/socialMedia_request', function () use ($baseViewPath) {
                return view($baseViewPath . 'socialMediaRequest');
            })->name('socialMediaRequest');
            Route::get('/chat', function () use ($baseViewPath) {
                return view($baseViewPath . 'chat');
            })->name('chat');
            Route::get('/brandCreate', function () use ($baseViewPath) {
                return view($baseViewPath . 'brands.create');
            })->name('brand.create');
            // Route::get('/brand', function () use ($baseViewPath) {
            //     return view($baseViewPath . 'brands.index');
            // })->name('brand');
            Route::get('/designGallery', function () use ($baseViewPath) {
                return view($baseViewPath . 'design_gallery.index');
            })->name('designGallery');
            Route::get('/brandDetails', function () use ($baseViewPath) {
                return view($baseViewPath . 'brands.details');
            })->name('brand.details');
            Route::get('/editBrand', function () use ($baseViewPath) {
                return view($baseViewPath . 'brands.edit');
            })->name('brand.edit');
            Route::get('/addressAdd', function () {
                return view('userpages.billingAddress.Add');
            })->name('billingAddress.billingAddressAdd');
        });
        // Route::post('/users', 'Admin\UserController@store');
        // Route::get('/posts', 'Admin\PostController@index');
        // ... more routes with the "/admin" prefix


        // Route::get('/plan', function () {
        //     return view('userpages.licence.plan');
        // })->name('userpages.licence.plan');

        // Route::resource('graphicProject', GraphicProjectController::class);

        // Route::controller(GraphicProjectController::class)->prefix('graphic/')->group(function () {
        //     Route::get('projects/{id?}', 'index')->name('graphic.projects');
        //     Route::get('project/create/{id?}', 'create')->name('graphic.project.create');
        //     Route::get('project/attachments/{project}', 'projectAttachments')->name('grap.prj.attachments');
        //     Route::post('chat/project', 'projcetChat')->name('graphic.project.chat'); // AJAX sent project chat
        //     Route::get('project/image/{id}', 'image_view')->name('graphic.proj.img'); //
        //     Route::patch('project/complete', 'markcomplete')->name('g.prj.comp');
        // });
        Route::post('/sendcomment', [GraphicProjectController::class, 'sendcomment']); // AJAX sent img comments

        Route::controller(LicenceController::class)->group(function () {
            Route::post('license/update/inline', 'inlineUpdate')->name('licence.update.inline');
            Route::get('hardware/reset', 'hardwareReset')->name('hardware.reset');
            Route::get('license/details', 'getLicence')->name('licence.details');
        });

        Route::controller(CartController::class)->group(function () {
            Route::get('cart', 'index')->name('u.cart');
            Route::post('update/cart', 'update');
            Route::delete('remove/{cart}', 'remove')->name('cart.remove');
            Route::get('cart/add/{product_id}/{quantity?}', 'addToCart')->name('cart.add');
            Route::post('addToCart', 'addToCartForm')->name('addToCartForm');
            // Route::post('coupon/add/to/cart', 'addCouponCart')->name('cart.coupon.add');
            Route::post('remove/coupon', 'removeCoupon')->name('remove.coupon');
            Route::post('update/period', 'updatePeriod')->name('update.period');

            Route::get('checkout', 'checkout')->name('checkout');
        });


        // graphic Routes start
        Route::get('graphic/order/{order}', [DesignController::class, 'orderItemsSelect'])->name('graphics.orders.edit');
        Route::get('graphic/{order}/order', [DesignController::class, 'orderItemsView'])->name('graphics.orders.view');
        Route::post('graphic/order/items/store', [DesignController::class, 'orderItemsStore'])->name('graphics.orders.items.store');

        Route::get('graphic/orders', [GProjectController::class, 'index'])->name('graphics.orders');
        // annotation  start
        Route::controller(AnnotationController::class)->prefix('annotations')->name('annotations.')->group(function () {
            Route::post('/', 'store')->name('store');
            Route::get('/{image_id}', 'getByImage')->name('getByImage');
            Route::delete('/comment/{id}', 'removeComment');
            Route::delete('/{id}', 'destroy')->name('destroy');
            Route::delete('/clear/{id}', 'destroyAll')->name('destroy');
            Route::put('/{id}', 'update')->name('update');
            Route::get('demo/order/view/{id}', 'demoOrderView');
        });
        // annotation  end


        // graphic Routes end

        Route::controller(InvoiceController::class)->group(function () {
            Route::post('/invoice/address/update', 'addressUpdate')->name('inv.address.update');
        });


        Route::controller(PaymentController::class)->prefix('payment/')->group(function () {
            Route::get('neft/details', 'neftPaid')->name('neft.paid');

            Route::get('/upi', 'payUpi')->name('payment.upi');
            Route::get('cheque/details', 'chequePaid')->name('cheque.paid');
            Route::get('upi/net/thankyou', 'thankNetPage')->name('upi.net.thankyou');

            Route::post('pay/upi/request', 'upiRequestSend')->name('upi.request.send');
        });

        // repeat order
        Route::get('repeat/order/{order}', [OrderController::class, 'repeat'])->name('repeat.order');
        /// help
        Route::get('/help/{cate}/{parentCategory?}', [KnowladgeBaseController::class, 'ArticalCate'])->name('help.cate');
        Route::get('/help/article/{article}', [KnowladgeBaseController::class, 'ArticalView'])->name('help.article');
        Route::get('/help', [PrimailerController::class, 'PageHelp'])->name('help');
    }); //End Auth

    Route::get('cheque/neft/thankyou', [PaymentController::class, 'thankOfflinePage'])->name('cheque.neft.thankyou');

    Route::get('generateSitemap', [SitemapController::class, 'make']);
    Route::get('sitemap.xml', [SitemapController::class, 'show']);
    // Route::get('robots.txt', [RobotsController::class, 'show']);
    // Route::get('generateRobot', [RobotsController::class, 'generate']);
    Route::get('/payment/qr', [PaymentController::class, 'payQr'])->name('payment.qr');
    Route::controller(AddressController::class)->group(function () {
        Route::post('address/update/', 'updateAddress')->name('address.update');
        Route::get('/address/inline/edit/{id}', 'inlineEdit');
        Route::post('/address/inline/update', 'inlineUpdate')->name('addr.inline.update');
        Route::post('address/store', 'store')->name('addr.store');
    });
    Route::post('submit/contact', [ContactController::class, 'submit'])->name('contactform.submit');
    // payment processing page
    Route::view('payment/captured', 'userpages.invoice.processingPage')->name('payment.process.page');
    Route::any('payment/processing', [PaymentController::class, 'paymentCaptured'])->name('payment.captured');
    //payments mtd
    Route::get('stripe/charge/{invoice?}', [StripeController::class, 'charge'])->name('call.stripe');
    //paypal
    Route::controller(PaypalController::class)->group(function () {
        Route::get('paypal/charge/{invoice?}', 'charge')->name('call.paypal');
        Route::get('paypal/success', 'success');
        Route::get('paypal/error', 'errorHandle');
    });
    //razorPay
    Route::get('razorpay/charge/{invoice?}', [razorPayController::class, 'charge'])->name('call.razorpay');
    //instaMojo
    Route::get('instaMojo/charge/{invoice?}', [instaMojoController::class, 'charge'])->name('call.instaMojo');
    // CcAvanue
    Route::get('CcAvanue/charge/{invoice?}', [CCAvanueController::class, 'charge'])->name('call.CCA');
    Route::post('ccavResponseHandler', [CCAvanueController::class, 'ccavResponseHandler']);

    Route::post('review/store/{lead}', [ReviewController::class, 'store'])->name('review.store');


    Route::post('verify/upi', [PaytmController::class, 'verify'])->name('verify.upi');
    Route::post('/paytm/response', [PaytmController::class, 'response'])->name('paytm.response');
    Route::post('/paytm/gateway', [PaytmController::class, 'showPopUp'])->name("paytmPopUp");


    // Route::post('demo/request/{product?}', [DemoRequestController::class, 'storeDemo'])->name('store.demorequest');

    Route::post('career/store', [CareerFormController::class, 'store'])->name('career.store');
    Route::post('tryMe/submit', [RingcasterController::class, 'tryMeForm'])->name('tryMe.submit');




    Route::view("partner-with-us", 'global.partner.partnerWithUs')->name('partner');
    Route::view("partner-form", 'global.partner.affiliate')->name('partner-form');


    // AFFILIATED & RESELLER ROUTE START

    // Route::view('affiliate', 'global.affiliate')->name('affiliate');
    // Route::view('reseller', 'global.reseller')->name('reseller');

    // AFFILIATED & RESELLER ROUTE END

    // //////////////////////////////////////////////////////////////

    ////globalcart

    Route::post('check/already/paid', [AlreadyPaidController::class, 'cheque'])->name('check.already.paid');
    Route::post('neft/already/paid', [AlreadyPaidController::class, 'neft'])->name('neft.already.paid');
    // New PI Routes
    Route::get('invoice/paid', [InvoiceController::class, 'alreadyPaid'])->name('alreadyPaid');
    Route::prefix('invoice/{invoice}')->controller(InvoicePaymentController::class)->name('pi.')->group(function () {
        Route::get('/', 'details')->name('details');
        Route::get('address', 'addr')->name('address');
        Route::post('address', 'addrUpdate')->name('address');
        Route::get('qr', 'generateQr')->name('qr');
        Route::get('payment', 'payment')->name('payment');
        Route::get('cheque/paid', 'chequePaid')->name('cheque_paid');
        Route::get('neft/paid', 'neftPaid')->name('neft_paid');
    });

    // new auth5

    Route::prefix('/')->controller(App\Http\Controllers\Auth\LoginController::class)->group(function () {
        Route::middleware('guest')->group(function () {
            Route::get('login', 'loginForm')->name("login");
            Route::post("/sendOtp", "sendOtp")->name("sendOtp");
            Route::get('registration', 'registrationForm')->name('registration');
            Route::post('/registration', "registrationSubmit")->name('userRegister');
            Route::get('login/otp', 'loginOtp')->name('loginOtp');
            Route::post('/otp-verify', "otpVerify")->name('otpVerify');
            Route::get('login/password', 'loginPassword')->name('loginPassword');
            Route::post('login/password', "verifyPassword")->name('passwordVerify');
            Route::get('login/password/generate', 'passwordGenerateView')->name('passwordGenerateView');
            Route::post('/login/password/generate', 'generatePasswordSave')->name('generatePasswordSave');
            Route::post('login/otp', 'noPasswordSendOtp')->name('noPasswordSendOtp');
        });
        Route::get('logout', 'logout')->name('logout')->middleware('auth');
    });
});
