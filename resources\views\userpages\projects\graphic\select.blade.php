    @extends('userpages.cart.main')

    @section('title', 'Graphics')
    @section('child-PAGE-CSS')
        <link rel="stylesheet" href="{{ asset('assets/global/css/cart.css') }}">

        <style>
            /* === RESPONSIVE LAYOUT === */
            @media (max-width: 991.98px) {
                .category-sidebar {
                    display: none !important;
                }

                .mobile-category-toggle {
                    display: block !important;
                }
            }

            @media (min-width: 992px) {
                .mobile-category-toggle {
                    display: none !important;
                }
            }

            /* === CATEGORY STYLES === */
            .category-item,
            .subcategory-item {
                cursor: pointer;
                transition: all 0.3s ease;
                color: black;
            }

            .category-item:hover,
            .subcategory-item:hover {
                background-color: #e9ecef;
            }

            .category-item.active {
                background-color: #6e6e6eaf;
                color: white;
            }

            .category-item.active>a {
                color: white;
            }

            .category-item>a {
                color: black;
            }

            .subcategory-item.active {
                background-color: #44C9F5;
                color: white;
            }

            .subcategory-item {
                padding-left: 2rem;
                font-size: 0.9rem;
            }

            /* === IMAGE CARD STYLES === */
            .image-card {
                transition: transform 0.2s ease, box-shadow 0.2s ease;
                border: 1.5px solid #ededed;
            }

            .image-card:hover {
                transform: translateY(-2px);
                box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            }

            .image-card.selected {
                border-color: #007bff71;
                box-shadow: 3px 3px 7px rgba(0, 123, 255, 0.3);
            }

            .image-placeholder {
                max-width: 300px;
                max-height: auto;
                display: flex;
                align-items: center;
                justify-content: center;
                color: #666;
                font-size: 3rem;
                overflow: hidden;
                cursor: pointer;
                position: relative;
                margin: auto
            }

            .image-card:hover .demo-image {
                transform: scale(1.05);
            }

            .image-overlay {
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(0, 0, 0, 0.5);
                color: white;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 1.2rem;
                opacity: 0;
                transition: opacity 0.3s ease;
            }

            .image-placeholder:hover .image-overlay {
                opacity: 1;
            }

            /* === COLLAPSE STYLES === */
            .collapse-icon {
                transition: transform 0.3s ease;
                transform: rotate(0deg);
            }

            .collapse-toggle:not(.collapsed) .collapse-icon,
            .mobile-category-toggle .fa-chevron-down.rotate-180 {
                transform: rotate(180deg);
            }

            .collapse-toggle {
                transition: all 0.3s ease;
                cursor: pointer;
            }

            .collapse-toggle:hover {
                opacity: 0.7;
            }

            .card-body-checkbox {
                display: flex;
                align-items: center;
                margin-bottom: 8px;
                justify-content: space-between
            }

            .card-body-checkbox label {
                margin: 0;
                cursor: pointer;
                font-weight: 500;
                flex: 1;
                user-select: none;
            }

            /* === MODAL STYLES === */
            .image-modal .modal-dialog {
                max-width: 90vw;
                max-height: 90vh;
            }

            .modal-image {
                max-width: 100%;
                max-height: 70vh;
                object-fit: contain;
            }

            /* === MOBILE STYLES === */
            @media (max-width: 991.98px) {
                .mobile-category-toggle button {
                    border-radius: 8px;
                    font-weight: 500;
                }
            }

            /* === GLOBAL STYLES === */
            body {
                margin: 0;
                padding: 0;
                min-height: 100vh;
                display: flex;
                flex-direction: column;
                background-color: #F8F7FC !important;
            }

            .rounded {
                border-radius: 7px !important;
            }

            .rounded-pill {
                border-radius: 50rem !important;
            }
        </style>
    @endsection
    @section('userpagesection')

        <div class="container-fluid container-lg">
            <div class="row">
                <!-- Categories Sidebar -->


                <div class="col-lg-3 col-12 category-sidebar overflow-auto d-none d-lg-block h-100">
                    <div class=" bg-white rounded h-100">
                        <x-category-list :categories="$categories" :activeCategory="$activeCategory" :orderId="$orderId" />
                    </div>
                </div>

                <!-- Graphics Grid -->
                <div class="col-lg-9 col-12 p-0">
                    <!-- Header Bar -->
                    <div class="bg-white p-2 mb-3  rounded">
                        <div class="d-flex align-items-center justify-content-between">

                            <div class="">
                                <div class="d-flex align-items-center">
                                    <div class="me-3">
                                        <a class="btn rounded-circle shadow" href="{{ url()->previous() }}">
                                            <i class="bi bi-arrow-left"></i>
                                        </a>
                                    </div>
                                    <div class="mobile-category-toggle d-lg-none me-2">
                                        <button class="btn btn-primary btn-sm" type="button" data-bs-toggle="collapse"
                                            data-bs-target="#mobile-categories" aria-expanded="false">

                                            <svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960"
                                                width="24px" fill="#e3e3e3">
                                                <path
                                                    d="M120-840h320v320H120v-320Zm80 80v160-160Zm320-80h320v320H520v-320Zm80 80v160-160ZM120-440h320v320H120v-320Zm80 80v160-160Zm440-80h80v120h120v80H720v120h-80v-120H520v-80h120v-120Zm-40-320v160h160v-160H600Zm-400 0v160h160v-160H200Zm0 400v160h160v-160H200Z" />
                                            </svg>

                                        </button>
                                    </div>
                                    <div class="">

                                        <h4 class="mb-0 d-none d-lg-block">Graphics Gallery</h4>
                                        <h5 class="mb-0 d-lg-none">Gallery</h5>
                                        <div class="text-secondary align-self-end">Pending Designs:
                                            {{ $order->noOfDesignsPending }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class=" d-flex justify-content-end align-items-center">
                                <div class="">
                                    <div class="error-box text-danger small d-none"></div>
                                    <div class="d-flex">
                                        <span class="me-3 d-none d-sm-inline text-muted fs-5 fw-bolder">
                                            <span id="selected-count">0</span> selected
                                        </span>
                                        <button class="btn btn-primary btn-sm rounded-pill" id="updateOrderBtn">
                                            Continue <i class="bi bi-chevron-right"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="collapse" id="mobile-categories">
                            <x-category-list :categories="$categories" :activeCategory="$activeCategory" :orderId="$orderId" :isMobile="true" />
                        </div>

                    </div>
                    <!-- Image Grid -->
                    <div class="card border-0 ">
                        <div class="card-header bg-white">
                            <div class="d-flex  justify-content-between">
                                <div class="d-flex  text-nowrap gap-3">
                                    <div class="fs-5 fw-semibold">Select Brand <sup class="text-danger">*</sup>:</div>
                                    <select class="form-select form-select-sm" aria-label="Default select example"
                                        name="brand_id" id="brand_select">
                                        <option value="" selected>Brand</option>
                                        @foreach ($brands as $brand)
                                            <option value="{{ $brand->id }}">{{ $brand->name }}</option>
                                        @endforeach
                                    </select>

                                </div>
                                <div class=""></div>
                            </div>
                        </div>
                        <div class=" card-body">
                            <div class="row g-3 row-cols-lg-3 row-cols-md-2 row-cols-1" id="image-grid">
                                @foreach ($designs as $image)
                                    @php
                                        $previewFile = s3_fileShow($image->image, 'graphic_design', $image->name);
                                        $parsedUrl = parse_url($previewFile);
                                        $path = isset($parsedUrl['path']) ? $parsedUrl['path'] : '';

                                        $extension = pathinfo($path, PATHINFO_EXTENSION);

                                        if (str_contains($extension, '&')) {
                                            $extension = explode('&', $extension)[0];
                                        }

                                    @endphp
                                    <div class="col">
                                        <div class="card image-card h-100 " data-image-id="{{ $image->id }}">
                                            <div class="image-placeholder mt-3" data-image-index="{{ $image->id }}">
                                                <img src="{{ $previewFile }}" alt="{{ $image->image }}"
                                                    data-name="{{ $image->name }}"
                                                    data-category="{{ $image->category->name }}"
                                                    id="image-{{ $image->id }}"
                                                    class="demo-image image-preview img-fluid rounded-1"
                                                    title="{{ $image->image }}" loading="lazy">
                                                <div class="image-overlay">
                                                    <i class="fas fa-eye"></i>
                                                </div>
                                            </div>
                                            <div class="card-body p-3 align-content-end lh-1">
                                                <div class="card-body-checkbox mb-0">
                                                    <label for="check-{{ $image->id }}"
                                                        class="card-title mb-0">{{ $image->name }}</label>
                                                    <input type="checkbox"
                                                        class="form-check-input custom-checkbox form-select-lg design-checkbox border-secondary"
                                                        id="check-{{ $image->id }}" disabled>
                                                </div>
                                                <small class="text-muted">{{ $extension }} •
                                                    {{ $image->category->name }}</small>
                                            </div>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                            @if ($designs->lastPage() > 1)
                                <div class="mt-3 p-1 bg-white" style="border-radius: 7px;">
                                    @include('components.showPagination', ['data' => $designs])
                                </div>
                            @endif

                        </div>

                    </div>
                </div>
            </div>
        </div>

        <!-- Image View Modal -->
        <div class="modal fade image-modal" id="imageModal" tabindex="-1" aria-hidden="true">
            <div class="modal-dialog modal-lg modal-dialog-centered">
                <div class="modal-content" style="height: 80vh;">
                    <div class="modal-header">
                        <h5 class="modal-title" id="modalImageTitle">Image Title</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div
                        class="modal-body  d-flex justify-content-center align-items-center text-center position-relative p-0">

                        <img src="" alt="" class="modal-image img-fluid  rounded-1" id="modalImage">

                    </div>
                    <div class="modal-footer">
                        <div class="d-flex justify-content-between align-items-center w-100">
                            <small class="text-muted" id="modalImageInfo"> landscapes</small>
                            <div>
                                <button type="button" class="btn btn-secondary btn-sm"
                                    data-bs-dismiss="modal">Close</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    @endsection
    @section('PAGE-script')

        <script>
            $(document).ready(function() {
                let selectedImages = new Set(JSON.parse(sessionStorage.getItem('selectedImages')) || []);

                // Handle brand selection changes
                $('#brand_select').on('change', function() {
                    const $checkboxes = $('.design-checkbox');
                    const hasValidBrand = $(this).val() && $(this).val() !== '';

                    $checkboxes.prop('disabled', !hasValidBrand);
                    if (!hasValidBrand) {
                        resetData();
                    }
                });
                resetData();

                function resetData() {
                    const $checkboxes = $('.design-checkbox');
                    $checkboxes.prop('checked', false);
                    $('.image-card').removeClass("selected");
                    sessionStorage.removeItem('selectedImages');
                    $('#selected-count').text('0');
                    // selectedImages=[];
                    // updateSelectedCount()
                }

                const designsPending = Number("{{ $order->noOfDesignsPending }}");
                // Initialize selected images from session storage

                // Utility functions
                const updateSelectedCount = () => $('#selected-count').text(selectedImages.size);

                const saveSelectedImages = () => {
                    sessionStorage.setItem('selectedImages', JSON.stringify(Array.from(selectedImages)));
                };

                // Set initial checked state for selected images
                function initializeSelectedImages() {
                    selectedImages.forEach(imageId => {
                        const $card = $(`[data-image-id="${imageId}"]`);
                        const $checkbox = $(`#check-${imageId}`);
                        $card.addClass('selected');
                        $checkbox.prop('checked', true);
                    });
                    updateSelectedCount();
                }

                // Show image in modal
                function showImageModal(imageIndex) {
                    const $image = $(`#image-${imageIndex}`);
                    const imageData = $image.data();


                    $('#modalImageTitle').text(imageData.name);
                    $('#modalImage').attr('src', $image.attr('src')).attr('alt', imageData.name);
                    $('#modalImageInfo').text(imageData.category);
                    $('#imageModal').modal('show');
                }


                // Toggle image selection
                function toggleSelection(imageId) {
                    const $card = $(`[data-image-id="${imageId}"]`);
                    const $checkbox = $(`#check-${imageId}`);

                    const isChecked = $checkbox.is(':checked');
                    const remainingSlots = designsPending - selectedImages.size;

                    if (isChecked && remainingSlots > 0) {
                        selectedImages.add(imageId);
                        $card.addClass('selected');

                        if (remainingSlots === 1) {
                            $('.design-checkbox:not(:checked)').prop('disabled', true);
                        }
                    } else if (isChecked) {
                        alert('Maximum allowed design reached.');
                        $checkbox.prop('checked', false);
                    } else {
                        $('.design-checkbox').prop('disabled', false);
                        selectedImages.delete(imageId);
                        $card.removeClass('selected');
                    }
                    saveSelectedImages();
                    updateSelectedCount();
                }

                // Event handlers
                $(document)
                    .on('change', '.custom-checkbox', function() {
                        const imageId = parseInt($(this).closest('.image-card').data('image-id'));
                        toggleSelection(imageId);
                    })
                    .on('click', '.image-placeholder', function(e) {
                        e.stopPropagation();
                        const imageIndex = parseInt($(this).data('image-index'));
                        showImageModal(imageIndex);
                    })
                    .on('show.bs.collapse', function(e) {
                        if (e.target.id === 'mobile-categories') {
                            $('.mobile-category-toggle .fa-chevron-down').addClass('rotate-180');
                        } else {
                            $(`[data-bs-target="#${e.target.id}"]`).removeClass('collapsed');
                        }
                    })
                    .on('hide.bs.collapse', function(e) {
                        if (e.target.id === 'mobile-categories') {
                            $('.mobile-category-toggle .fa-chevron-down').removeClass('rotate-180');
                        } else {
                            $(`[data-bs-target="#${e.target.id}"]`).addClass('collapsed');
                        }
                    });

                // Window resize handler
                $(window).on('resize', function() {
                    if ($(window).width() >= 992) {
                        $('#mobile-categories').removeClass('show');
                        $('.mobile-category-toggle .fa-chevron-down').removeClass('rotate-180');
                    }
                });

                // Initialize
                initializeSelectedImages();

                // Form submission handler
                $('#updateOrderBtn').on('click', async function(e) {

                    if (selectedImages.size === 0) {
                        $('.error-box').text('Please select at least one image.').removeClass('d-none');
                        return;
                    }

                    let brandID = $('#brand_select').val();

                    console.log(brandID);
                    if (!brandID) {
                        $('.error-box').text('Please select a brand.').removeClass('d-none');
                        return;
                    }

                    // console.log(selectedImages);
                    const $btn = $(this);
                    const originalHtml = $btn.html();

                    $btn.html('<div class="spinner-border spinner-border-sm" role="status"></div>');
                    $('.error-box').addClass('d-none');
                    if (selectedImages.size <= designsPending) {
                        try {
                            const response = await fetch("{{ route('graphics.orders.items.store') }}", {
                                method: 'POST',
                                headers: {
                                    'Content-Type': 'application/json',
                                    'X-CSRF-TOKEN': document.querySelector(
                                            'meta[name="csrf-token"]')
                                        .getAttribute('content')
                                },
                                body: JSON.stringify({
                                    brand_id: brandID,
                                    graphicOrderId: "{{ $orderId }}",
                                    selectedImages: Array.from(selectedImages)
                                })
                            });

                            const result = await response.json();

                            if (result.status) {
                                sessionStorage.removeItem('selectedImages');
                                selectedImages.clear();
                                initializeSelectedImages();
                                window.location.href =
                                    "{{ route('graphics.orders.view', ['order' => $orderId]) }}";
                            } else {
                                $('.error-box').text(result.message || 'An error occurred').removeClass(
                                    'd-none');
                            }
                        } catch (error) {
                            $('.error-box').text('Network error. Please try again.').removeClass('d-none');
                            console.error('Fetch error:', error);
                        } finally {
                            // $btn.html(originalHtml);
                        }
                    } else {
                        alert('invalid Request');
                    }
                });

            })
        </script>
    @endsection
