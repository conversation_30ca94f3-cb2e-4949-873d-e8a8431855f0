@extends('auth5.main')
@section('form')
   <div class="">
        <div class="mb-2">
            <h3 class="fs-3 text-dark">Enter the details below</h3>
        </div>
        <p class="mb-3" style="color:#424242;">
            We will send you a confirmation code.
        </p>
    </div>
    <form action="{{ route('userRegister') }}" method="post" onsubmit="formSubmit()">
        @csrf
        <div class="input-group" style="border-bottom: 1px solid #C0C0C0;">
            <span class="input-group-text bg-transparent border-0">
                <i class="fa-solid fa-user"></i>
            </span>
            <div class="form-floating">
                <input type="text" name="name" class="form-control border-0 shadow-none" style="border-radius: 0;"
                    placeholder="Name" value="{{ old('name') }}" autofocus required>
                <label for="EmailMobile">Name<span class="text-danger">*</span></label>
            </div>
        </div>
        @error('name')
            <div class="text-danger">{{ $message }}</div>
        @enderror
        <div class="input-group" style="border-bottom: 1px solid #C0C0C0;">
            <span class="input-group-text bg-transparent border-0">
                <i class="fa-solid fa-house-chimney"></i>
            </span>
            <div class="form-floating">
                <input type="text" name="company" class="form-control border-0 shadow-none" style="border-radius: 0;"
                    placeholder="Business name" value="{{ old('company') }}" required>
                <label for="EmailMobile">Business name<span class="text-danger">*</span></label>
            </div>
        </div>
        @error('company')
            <div class="text-danger">{{ $message }}</div>
        @enderror
        {{-- <input type="hidden" name="lead" value="{{ session()->get('lead') }}"> --}}
        @if (session()->has('userData.login_email'))
            {{-- <input type="hidden" name="old_email" value="{{ session()->get('email') }}"> --}}
            <input type="hidden" name="code" value="{{ old('code') }}" id="code" >
            <div class="my-3">
                <div class="form-floating " style="border-bottom: 1px solid #C0C0C0;">
                    <input type="text" name="mobile" class="form-control border-0 shadow-none" style="border-radius: 0;" required
                        id="number" placeholder="Mobile Number*" value="{{ old('mobile') }}">
                </div>
                @error('mobile')
                    <div class="text-danger">{{ $message }}</div>
                @enderror
            </div>
        @elseif(session()->has('userData.login_mobile'))
            <div class="mb-3">
                <div class="input-group" style="border-bottom: 1px solid #C0C0C0;">
                    <span class="input-group-text bg-transparent border-0">
                        <i class="fa-solid fa-envelope"></i>
                    </span>
                    <div class="form-floating">
                        <input type="email" name="email" class="form-control border-0 shadow-none" id="email" required
                            style="border-radius: 0;" placeholder="Email" value="{{ old('email') }}">
                        <label for="Email">Email<span class="text-danger">*</span></label>
                    </div>
                </div>
                @error('email')
                    <div class="text-danger">{{ $message }}</div>
                @enderror
            </div>
        @endif
        <h4 class="mb-2">Interested in<span class="text-danger">*</span></h4>
        @error('interestedIn')
            <div class="text-danger">{{ $message }}</div>
        @enderror
        <div class="row px-2 mb-2" style="font-size: 12px;">
            @foreach ($lead_for as $key => $name)
                <div class="p-1 pt-0 col-lg-6 col-md-6 ">

                    <div class="form-check pe-0 d-flex align-items-baseline gap-2">
                        <input class="form-check-input shadow-none checkBoxes" name="interestedIn[]" type="checkbox"
                            value="{{ $key }}" id="lead_for{{ $key }}" @checked(in_array($key, old('interestedIn') ?? []))
                            style="border: 1px solid #b4b6b7">
                        <label class="form-check-label fs-6" for="lead_for{{ $key }}">
                            {{ $name }}
                        </label>
                    </div>
                </div>
            @endforeach
        </div>

        <div class="continue-btn mb-5" id="continue_btn">
            <button type="submit" data-sitekey="{{ env('RECAPTCHA_SITE_KEY') }}" data-callback='onSubmit'
                class="btn btn-main btn-lg ps-5 pe-5 rounded-pill fs-5 px-2 d-flex align-items-baseline justify-content-center gap-3"
                style="width: -webkit-fill-available;">
                Next
                <i class="fa-solid fa-chevron-right"></i>
            </button>
        </div>
    </form>
@endsection
@section('form-css')
    <script>
        function onSubmit(token) {
            document.getElementById("token").value = token;
            var countryCodenumber = $('.iti__selected-dial-code').text();
            $('#cCode').val(countryCodenumber);
            document.getElementById("submit_form").submit();
        }
    </script>
    <link rel="stylesheet" href="{{ asset('assets/auth/country_code/build/css/intlTelInput.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/auth/country_code/build/css/demo.css') }}">
    <script src="{{ asset('assets/auth/country_code/build/js/intlTelInput.js') }}"></script>
@endsection
@section('form-script')
    <script>
        var input = document.querySelector("#number");
        window.intlTelInput(input, {
            dropdownContainer: document.body,
            localizedCountries: {
                'IN': 'india'
            },
            placeholderNumberType: "MOBILE",
            preferredCountries: ['in'],
            separateDialCode: true,
        });
    </script>
    <script>
        function formSubmit() {
            let inputField = $('#number').val();
            let code = $('.iti__selected-dial-code').text().slice(1, 10);
            $('#code').val(code);
            inputField = code + inputField;
            $('#number').val(inputField);
        };
    </script>

@endsection
