@extends('components.pixayogi.main')
@section('pixayogi-css')
    <style>
        .category-sidebar {
            background-color: #f8f9fa;
            border-right: 1px solid #dee2e6;
            min-height: 100vh;
        }

        /* Mobile responsive styles */
        @media (max-width: 768px) {
            .category-sidebar {
                min-height: auto;
                border-right: none;
                border-bottom: 1px solid #dee2e6;
            }

            .mobile-category-toggle {
                display: block !important;
            }

            .category-content {
                display: none;
            }

            .category-content.show {
                display: block;
            }
        }

        @media (min-width: 769px) {
            .mobile-category-toggle {
                display: none !important;
            }

            .category-content {
                display: block !important;
            }
        }

        .category-item {
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .category-item:hover {
            background-color: #e9ecef;
        }

        .category-item.active {
            background-color: #007bff;
            color: white;
        }

        .subcategory-item {
            padding-left: 2rem;
            font-size: 0.9rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .subcategory-item:hover {
            background-color: #e9ecef;
        }

        .subcategory-item.active {
            background-color: #0056b3;
            color: white;
        }

        .image-card {
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }

        .image-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .image-card.selected {
            border: 3px solid #007bff;
            box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
        }

        .image-placeholder {
            max-width: 200px;
            max-height: 300px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            font-size: 3rem;
            overflow: hidden;
            cursor: pointer;
            position: relative;
            margin: auto
        }


        .image-card:hover .demo-image {
            transform: scale(1.05);
        }

        .image-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .image-placeholder:hover .image-overlay {
            opacity: 1;
        }

        .stats-bar {
            background-color: #fff;
            border-bottom: 1px solid #dee2e6;
            padding: 1rem 0;
        }

        .collapse-icon {
            transition: transform 0.3s ease;
        }

        .collapsed .collapse-icon {
            transform: rotate(-90deg);
        }

        .rotate-180 {
            transform: rotate(180deg);
        }

        .card-body-checkbox {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
            justify-content: space-between
        }

        .card-body-checkbox label {
            margin: 0;
            cursor: pointer;
            font-weight: 500;
            flex: 1;
            user-select: none;
        }

        /* Modal styles */
        .image-modal .modal-dialog {
            max-width: 90vw;
            max-height: 90vh;
        }

        .modal-image {
            max-width: 100%;
            max-height: 70vh;
            object-fit: contain;
        }

        .modal-navigation {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            background: rgb(0 0 0 / 31%);
            color: white;
            border: none;
            cursor: pointer;
            border-radius: 50%;
            transition: background-color 0.3s ease;
            z-index: 1055;
        }

        .modal-navigation:hover {
            background: rgba(0, 0, 0, 0.8);
            color: white;
        }

        .modal-nav-prev {
            left: 20px;
        }

        .modal-nav-next {
            right: 20px;
        }

        .modal-nav-disabled {
            opacity: 0.3;
            cursor: not-allowed;
        }

        .modal-nav-disabled:hover {
            background: rgba(0, 0, 0, 0.5);
        }
    </style>
@endsection

@section('webpage')
    <div class="container-fluid mt-4">
        <div class="row">
            <!-- Categories Sidebar (3/12) -->
            <div class="col-md-3 col-12 category-sidebar p-0">
                <!-- Mobile Toggle Button -->
                <div class="mobile-category-toggle d-md-none p-3 border-bottom">
                    <button class="btn btn-outline-primary w-100" type="button" data-bs-toggle="collapse"
                        data-bs-target="#mobile-categories" aria-expanded="false">
                        <i class="fas fa-folder me-2"></i>Categories
                        <i class="fas fa-chevron-down float-end mt-1"></i>
                    </button>
                </div>

                <!-- Categories Content -->
                <div class="category-content collapse d-md-block" id="mobile-categories">
                    <div class="p-3">
                        <a href="{{ route('shop.graphics.home') }}" class="text-decoration-none text-dark">
                            <h5 class="mb-3 d-none d-md-block"><i class="fas fa-folder me-2"></i>Categories</h5>
                        </a>
                        @foreach ($categories as $category)
                            @if ($category->subcategories->count())
                                <div class="category-section mb-2">
                                    <div class="category-item p-2 rounded {{ !$loop->first ? 'collapsed' : '' }}"
                                        data-bs-toggle="collapse" data-bs-target="#{{ $category->id }}-subcategories">
                                        <i class="fas fa-chevron-down collapse-icon me-2"></i>

                                        {{ $category->name }}
                                        <span
                                            class="badge bg-secondary float-end">{{ $category->subcategories_count }}</span>
                                    </div>
                                    <div class="collapse {{ $loop->first ? 'show' : '' }}"
                                        id="{{ $category->id }}-subcategories">
                                        @foreach ($category->subcategories as $sub)
                                            <a href="{{ route('shop.graphics.home', ['category' => $sub->id]) }}"
                                                class="text-decoration-none text-dark">

                                                <div class="subcategory-item p-2 rounded"
                                                    data-category="{{ $sub->name }}">
                                                    • {{ $sub->name }}
                                                    <span
                                                        class="badge bg-light text-dark float-end">{{ $sub->designs_count }}</span>
                                                </div>
                                            </a>
                                        @endforeach
                                    </div>
                                </div>
                            @else
                                <a href="{{ route('shop.graphics.home', ['category' => $category->id]) }}"
                                    class="text-decoration-none text-dark">

                                    <div class="subcategory-item p-2 rounded" data-category="{{ $category->name }}">
                                        • {{ $category->name }}
                                        <span
                                            class="badge bg-light text-dark float-end">{{ $category->designs_count }}</span>
                                    </div>
                                </a>
                            @endif
                        @endforeach
                    </div>
                </div>
            </div>

            <!-- Graphics Grid (9/12) -->
            <div class="col-md-9 col-12 p-0">
                <!-- Stats Bar -->
                <div class="stats-bar">
                    <div class="container-fluid px-3">
                        <div class="row align-items-center">
                            <div class="col-6">
                                <h4 class="mb-0 d-none d-md-block">Graphics Gallery</h4>
                                <h5 class="mb-0 d-md-none">Gallery</h5>
                            </div>
                            <div class="col-6 d-flex justify-content-end align-items-center">
                                <span class="me-3 d-none d-sm-inline"><span id="selected-count">0</span> selected</span>
                                <button class="btn btn-primary btn-sm rounded-5" id="updateOrderBtn">Continue
                                    <i class="bi bi-chevron-right"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Image Grid -->
                <div class="container-fluid p-3">
                    <div class="row g-3" id="image-grid">
                        @foreach ($designs as $image)
                            @php
                                $path = parse_url($image->image, PHP_URL_PATH);
                                $extension = pathinfo($path, PATHINFO_EXTENSION);
                            @endphp
                            <div class="col-xl-3 col-lg-4 col-md-6 col-sm-6 col-12">
                                <div class="card image-card h-100" data-image-id="{{ $image->id }}">
                                    <div class="image-placeholder mt-3" data-image-index="{{ $image->id }}">
                                        <img src="{{ $image->image }}" alt="{{ $image->image }}"
                                            class="demo-image image-preview img-fluid" title="{{ $image->image }}"
                                            loading="lazy">
                                        <div class="image-overlay">
                                            <i class="fas fa-eye"></i>
                                        </div>
                                    </div>
                                    <div class="card-body p-3 align-content-end ">
                                        <div class="card-body-checkbox ">
                                            <label for="check-{{ $image->id }}"
                                                class="card-title mb-0">{{ $image->name }}</label>
                                            <input type="checkbox" class="form-check-input custom-checkbox form-select-lg"
                                                id="check-{{ $image->id }}">
                                        </div>
                                        <small class="text-muted">{{ $extension }} •
                                            {{ $image->category->name }}</small>
                                    </div>
                                </div>
                            </div>
                        @endforeach

                    </div>

                </div>
            </div>
        </div>
    </div>

    <!-- Image View Modal -->
    <div class="modal fade image-modal" id="imageModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-lg modal-dialog-centered">
            <div class="modal-content" style="height: 80vh;">
                <div class="modal-header">
                    <h5 class="modal-title" id="modalImageTitle">Image Title</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body  d-flex justify-content-center align-items-center text-center position-relative p-0">
                    {{-- <button class="btn modal-navigation modal-nav-prev" id="prevImageBtn">
                        <i class="fas fa-chevron-left"></i>
                    </button> --}}
                    <img src="" alt="" class="modal-image img-fluid" id="modalImage">
                    {{-- <button class="btn modal-navigation modal-nav-next" id="nextImageBtn">
                        <i class="fas fa-chevron-right"></i>
                    </button> --}}
                </div>
                <div class="modal-footer">
                    <div class="d-flex justify-content-between align-items-center w-100">
                        <small class="text-muted" id="modalImageInfo"> landscapes</small>
                        <div>
                            <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">Close</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('pixayogi-js')
    <script>
        $(document).ready(function() {
            // Sample image data with demo images
            const sampleImages = @json($designs->toArray());
            let selectedImages = new Set(JSON.parse(sessionStorage.getItem('selectedImages')) || []);
            let currentImages = sampleImages;
            let currentModalIndex = 0;

            // Set checked state for selected images
            function setSelectedImagesChecked() {
                $('#image-grid .image-card').each(function() {
                    const imageId = parseInt($(this).data('image-id'));
                    if (selectedImages.has(imageId)) {
                        $(this).addClass('selected');
                        $(`#check-${imageId}`).prop('checked', true);
                    }
                });
                updateSelectedCount();
            }

            function addToSelected(e) {
                const id = parseInt(e.target.dataset.imageId);
                console.log('id', id);
            }


            // Show image in modal
            function showImageModal(imageIndex) {

                const image = currentImages.find(img => img.id == imageIndex);

                currentModalIndex = imageIndex;

                $('#modalImageTitle').text(image.name);
                $('#modalImage').attr('src', image.image).attr('alt', image.name);
                $('#modalImageInfo').text(`${image.category.name}`);
                $('#modalImagePosition').text(`${imageIndex + 1} of ${currentImages.length}`);
                // $('#modelCheck').attr('data-image-id', image.id);
                $('#imageModal').modal('show');
            }


            // Toggle image selection with jQuery
            function toggleSelection(imageId) {
                console.log('imageId', imageId);

                const $card = $(`[data-image-id="${imageId}"]`);
                const $checkbox = $(`#check-${imageId}`);
                if ($checkbox.is(':checked')) {
                    selectedImages.add(imageId);
                    $card.addClass('selected');
                    sessionStorage.setItem('selectedImages', JSON.stringify(Array.from(selectedImages)));
                } else {
                    selectedImages.delete(imageId);
                    $card.removeClass('selected');
                    sessionStorage.setItem('selectedImages', JSON.stringify(Array.from(selectedImages)));
                }
                updateSelectedCount();
            }

            // Update selected count
            function updateSelectedCount() {
                $('#selected-count').text(selectedImages.size);
                $('#mobile-selected-count').text(selectedImages.size);
            }

            // Update stats
            function updateStats(showing) {
                $('#showing-count').text(showing);
                updateSelectedCount();
            }

            // Checkbox change handler
            $(document).on('change', '.custom-checkbox', function() {
                const imageId = parseInt($(this).closest('.image-card').data('image-id'));
                toggleSelection(imageId);
            });

            // Image click handler for modal view
            $(document).on('click', '.image-placeholder', function(e) {
                e.stopPropagation();
                const imageIndex = parseInt($(this).data('image-index'));
                showImageModal(imageIndex);
            });

            // Mobile category toggle animation
            $('#mobile-categories').on('show.bs.collapse', function() {
                $('.mobile-category-toggle .fa-chevron-down').addClass('rotate-180');
            }).on('hide.bs.collapse', function() {
                $('.mobile-category-toggle .fa-chevron-down').removeClass('rotate-180');
            });

            // Window resize handler
            $(window).on('resize', function() {
                if ($(window).width() >= 768) {
                    $('#mobile-categories').removeClass('show');
                    $('.mobile-category-toggle .fa-chevron-down').removeClass('rotate-180');
                }
            });


            setSelectedImagesChecked();

            $('#updateOrderBtn').on('click', function() {
                console.log(selectedImages);
            });
        })
    </script>
@endsection
