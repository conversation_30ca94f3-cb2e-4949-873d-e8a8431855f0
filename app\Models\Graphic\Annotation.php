<?php

namespace App\Models\Graphic;

use App\Models\Lead;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Annotation extends Model
{
    use HasFactory;
    protected $table = "graphic_design_order_item_comment";
    public $timestamps = false;

    protected $fillable = [
        'graphic_design_order_item_id',
        'comment',
        'annotationJson',
        'user_id',
        'annotationId',
        'lead_id',
      
    ];

    protected $dates = ['datetime'];

    protected $casts = [
        'annotationJson' => 'array',
        'datetime' => 'datetime:Y-m-d\TH:i:s.v\Z',
    ];

    // protected $appends = ['annotation_id', 'item_id'];
    public function designItem()
    {
        return $this->belongsTo(DesignOrderItem::class, 'graphic_design_order_item_id', 'id');
    }

    public function byUser()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    public function byLead()
    {
        return $this->belongsTo(Lead::class, 'lead_id', 'id');
    }

 
}
