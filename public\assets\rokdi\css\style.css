
.mySwiper .swiper-pagination-bullet {
    background-color: #000000 !important;
    /* Change this to your desired color */
}

body {
    padding-top: 5%;
}

@media only screen and (max-width: 320px) {
    body {
        padding-top: 15%;
    }
}

@media only screen and (max-width: 768px) {
    body {
        padding-top: 10%;
    }
}

@media only screen and (max-width: 991px) {
    body {
        padding-top: 6%;
    }
}

@media only screen and (max-width: 992px) {
    body {
        padding-top: 10%;
    }
}

@media only screen and (max-width: 1199px) {
    body {
        padding-top: 8%;
    }
}

@media only screen and (max-width: 1299px) {
    body {
        padding-top: 5%;
    }
}

@media only screen and (max-width: 2560px) {
    body {
        padding-top: 4%;
    }
}

.bg-color {
    background: #910b2b;
}

.bg-color-light {
    background: #fce4ea4d;
}

.bg-cream {
    background: #faf7ee;
}

.p-color-grey {
    color: #595959;
    font-weight: 100;
}

h1 {
    font-size: 60px;
    font-weight: 600;
    line-height: 68px;
    letter-spacing: 0em;
}

@media only screen and (max-width: 1299px) {

    h1 {
        font-size: 58px;
        font-weight: 600;
        line-height: 75px;
        letter-spacing: 0em;
    }
}

@media only screen and (max-width: 991px) {
    h1 {
        font-size: 50px;
        font-weight: 600;
        line-height: 60px;
        letter-spacing: 0em;
    }
}

@media only screen and (max-width: 425px) {
    h1 {
        font-size: 44px;
        font-weight: 600;
        line-height: 45px;
        letter-spacing: 0em;
    }
}

h2 {
    font-size: 55px;
    font-weight: 600;
    line-height: 63px;
    letter-spacing: 0em;
}

@media only screen and (max-width: 1299px) {

    h2 {
        font-size: 50px;
        font-weight: 600;
        line-height: 55px;
        letter-spacing: 0em;
    }
}

@media only screen and (max-width: 991px) {
    h2 {
        font-size: 45px;
        font-weight: 600;
        line-height: 55px;
        letter-spacing: 0em;
    }
}

h3 {
    font-size: 50px;
    font-weight: 600;
    line-height: 55px;
    letter-spacing: 0em;
}

@media only screen and (max-width: 1299px) {

    h3 {
        font-size: 45px;
        font-weight: 600;
        line-height: 55px;
        letter-spacing: 0em;
    }
}

@media only screen and (max-width: 991px) {
    h3 {
        font-size: 40px;
        font-weight: 600;
        line-height: 55px;
        letter-spacing: 0em;
    }
}



.header-div>.header-ul {
    gap: 1.5rem !important;
}

.header-nav>.header-nav-text {
    font-size: 1.3rem !important;
}

.schedule-nav>.schedule-text {
    font-size: 1rem !important;
}

/*------------------------------------------------- NAVBAR css Starts --------------------------------------------------------- */
a.underline-hover-effect {
    text-decoration: none;
    color: inherit;
}

.underline-hover-effect {
    display: inline-block;
    padding-bottom: 0.25rem;
    position: relative;
}

.underline-hover-effect::before {
    content: "";
    position: absolute;
    left: 0;
    bottom: 0;
    width: 0;
    height: 2px;
    background-color: #ffffff;
    transition: width 0.25s ease-out;
}

.underline-hover-effect:hover::before {
    width: 100%;
}

@media only screen and (max-width: 991px) {
    .horzontal-line {
        border: 1px solid #910b2b;
    }
}

.yellow-btn {
    background: #ffbe0b;
    box-shadow: 0px 4px 5px 0px #ffe59561;
    cursor: pointer;
    transition: .3s;
}

.yellow-btn:hover {
    background: #fdc93c;
}

.maroon-btn {
    background: #910b2b;
    box-shadow: 0px 4px 4px 0px #ce102126;
    width: fit-content;
    transition: .3s;
}

.maroon-btn:hover {
    background: #d31642;
}

.white-btn {
    border: 1px solid #ffffff;
    background-color: transparent;
}

.mobile-nav {
    display: flex;
    justify-content: space-between;
}

.max-width-fit {
    max-width: fit-content;
}

.navbar-toggler {
    padding: var(--bs-navbar-toggler-padding-y) var(--bs-navbar-toggler-padding-x);
    font-size: var(--bs-navbar-toggler-font-size);
    line-height: 1;
    color: rgb(255 255 255);
    background-color: transparent;
    border: var(--bs-border-width) solid rgb(255 255 255 / 15%);
    border-radius: var(--bs-navbar-toggler-border-radius);
    transition: var(--bs-navbar-toggler-transition);
}

.nav-items-responsive {
    align-items: center;
}


@media only screen and (max-width: 991px) {
    .horzontal-line {
        border: 1px solid #ffffff;
    }

    .nav-items-responsive {
        align-items: flex-start !important;
    }
}

/*------------------------------------------------- NAVBAR CSS Ends --------------------------------------------------------- */

/*------------------------------------------------- FOOTER CSS STARTS --------------------------------------------------------- */
/* Footer CSS */

.social {
    display: flex;
    justify-content: center;
    align-items: center;
}

.social .link {
    height: 35px;
    width: 35px;
    background-color: #caf7e3;
    border-radius: 20px;
    text-align: center;
    margin: 2px;
    line-height: 37.5px;
}

.social a i {
    transition: all 0.3s linear;
}

.social a:hover i {
    transform: scale(1.5);
}

.underline {
    display: inline-block;
    position: relative;
}

.underline:after {
    content: '';
    position: absolute;
    width: 100%;
    transform: scaleX(0);
    height: 2px;
    bottom: 0;
    left: 0;
    background-color: #cbc7c3;
    transform-origin: bottom left;
    transition: transform 0.5s ease-out;
}

.underline:hover:after {
    transform: scaleX(1);
}

.logo {
    height: 60px;
}

#logo {
    height: 85px;
}

li {
    font-weight: 400;
    /* line-height: 41px; */
    letter-spacing: 0em;
}

h4 {
    font-size: 23px;
    font-weight: 400;
    line-height: 41px;
    letter-spacing: 0em;
}

h5 {
    font-weight: 400;
    line-height: 41px;
    letter-spacing: 0em;
}

.divider {
    border-top: 1px solid #909090;
}

@media only screen and (max-width: 768px) {
    .footer-flex {
        display: flex;
        flex-direction: column;
    }

    .footer-JC {
        justify-content: flex-start !important;
    }
}

/*------------------------------------------------- FOOTER CSS ENDS --------------------------------------------------------- */

/*------------------------------------------------- HOME PAGE CSS STARTS --------------------------------------------------------- */

.yellow-color {
    color: #ffbe0b;
}

.p-hr {
    border-left: 3px solid #ffbe0b;
}



/* FAQ'S SECTION  */


.faq-accordion .faq-heading {
    font-size: 20px;
}

.faq-heading {
    line-height: 30px;
    color: #3c3c3c;
    align-items: start;
}

.faq-div p {
    color: #675f72;
    font-weight: 100;
    line-height: 24px;
    padding: 10px 20px 10px 20px;
    margin-top: 2px;
}

.mb-3-para p {
    margin-bottom: 0rem !important;
}

.faq-accordion {
    display: flex;
    flex-direction: column;
}

.faq-accordion button {
    text-decoration: none !important;
}

.faq-btn:first-child:active,
:not(.faq-btn-check)+.faq-btn:active {
    background-color: white !important;
}

.faq-accordion a {
    text-decoration: none !important;
}

.faq-card-header {
    display: flex;
    background-color: #ffffff00;
    color: #717171;
    cursor: pointer;
    align-items: self-start;
    padding: 20px;
    box-shadow: 0 0 10px 0 rgba(183, 183, 183, 0.2);
}

.faq-para {
    box-shadow: 3px 5px 14px 0px rgba(0, 0, 0, 0.1);
}

.btn[aria-expanded="true"] {
    background-color: #910b2b;
}

.btn[aria-expanded="true"] .faq-heading {
    color: #ffffff;
}

.btn[aria-expanded="true"] .accordion-item {
    border-left: 1px solid black !important;
}

.btn[aria-expanded="true"]:hover {
    background-color: #910b2b;
}

/* .btn {
    --bs-btn-padding-x: 0rem !important;
} */
.btn[aria-expanded="true"] .faq-card-header {
    background-color: transparent;
}

@keyframes fade-in {
    from {
        opacity: 0;
    }

    to {
        opacity: 1;
    }
}

.bg-faq {
    background: #ff6f14;
}

.fa-facebook {
    color: #cdcdcd;
}

.fa-facebook:hover {
    color: #ff6f14;
}

.fa-instagram {
    color: #cdcdcd;
}

.fa-instagram:hover {
    color: #ff6f14;
}

/* FAQ'S SECTION  */

/* TESTIMONIAL CSS STARTS  */
swiper-container {
    width: 100%;
    height: 100%;
}

swiper-slide {
    text-align: center;
    font-size: 18px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.swiper-slide img {
    display: block;
    /* width: 100%; */
    height: 100%;
    object-fit: cover;
}

.bullet-color {
    --swiper-theme-color: #910b2b !important;
    /* position: absolute;
    text-align: center;
    transition: opacity 0.3s ease 0s;
    transform: translate3d(0px, 0px, 0px);
    z-index: 10; */
}

.testi-style {
    box-shadow: 2px 4px 33px -17px #00000040;
    background: #ffffff;
    border-bottom: 3px solid #910b2b;
    padding: 40px 30px 40px 30px;
    margin: 0px 20px 60px 20px;
}

@media only screen and (max-width: 1299px) {

    .testi-style {
        margin: 0px 0px 60px 0px;
    }

}

@media only screen and (max-width: 1199px) {

    .testi-style {
        padding: 40px 10px 40px 10px;
        margin: 0px 0px 60px 0px;
    }

}

.hide1 {
    display: none;
}

.show1 {
    display: block;
}

@media only screen and (max-width: 991px) {

    .show1 {
        display: none !important;
    }

}

@media only screen and (max-width: 991px) {

    .hide1 {
        display: block;
    }

}

/* TESTIMONIAL CSS ENDS  */

/* Process css starts  */

.grid-3 {
    display: grid;
    grid-template-columns: auto auto auto;
}

@media only screen and (max-width: 991px) {

    .grid-3 {
        display: grid;
        grid-template-columns: auto;
    }

}

@media only screen and (max-width: 991px) {
    .BS-left-side-content {
        text-align: left !important;
        flex-direction: row-reverse;
    }
}