@extends('userpages.invoice.components.main')
@section('webpage')
    @php
        $hostdata = hostData();
    @endphp
    <div class="">
        <div class="row">
            <div class="col-lg-8 col-md-12 col-sm-12">

                <div class="mt-3" style="box-shadow: 0px 4px 7.199999809265137px 0px #00000008; border-radius:7px;">
                    @include('userpages.paymentInvoice.card_cart')
                    @include('userpages.paymentInvoice.card_billing')

                    <div class="d-flex align-items-center gap-3 p-3"
                        style="background-color: #EAF1FF;border-radius:7px 7px 0 0;">
                        <span class="align-items-center justify-content-center badge d-flex rounded-circle text-white"
                            style="height: 30px;width: 30px;background-color:#194DAB;">3</span>
                        <h4 style="color: #194DAB;">Payment</h4>
                    </div>
                    <div class="bg-white">
                        <div class="col-12">
                            @if ($hostdata->enableUpi || $hostdata->enablePaytmQr)
                                <hr class="m-0">
                                <div class="p-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="flexRadioDefault"
                                            id="flexRadioDefault2" checked>
                                        <label class="form-check-label d-flex align-items-center gap-3"
                                            for="flexRadioDefault2">
                                            <h6>UPI / QR Code</h6>
                                            <img class="img-fluid img-upiContent d-block"
                                                src="{{ asset('media/icons/upi-icons.png') }}" alt="UPI">
                                        </label>
                                        <div class="content show">
                                            <div class="d-flex align-items-center gap-3">
                                                @include('components.payment.qr')
                                            </div>

                                        </div>
                                    </div>
                                </div>
                            @endif
                            <hr class="m-0">
                            <div class="p-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="flexRadioDefault"
                                        id="flexRadioDefault4">
                                    <div class="d-flex">
                                        <label class="form-check-label mb-3" for="flexRadioDefault4">
                                            <h6>NetBanking / Credit / Debit Cards / Wallets</h6>
                                        </label>
                                        <div class="spinner-border text-secondary ms-3" role="status"
                                            style="width: 25px; height: 25px;" id="paymentLoader">
                                            <span class="visually-hidden">Loading...</span>
                                        </div>
                                    </div>
                                    <div class=" content d-none">
                                        @include('components.payment.buttons')
                                    </div>
                                </div>
                            </div>
                            @if ($hostdata->enableStaticBankAccount || $hostdata->enableRazorPayVirtualAccount)
                                <hr class="m-0">
                                <div class="p-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="flexRadioDefault"
                                            id="flexRadioDefault1">
                                        <label class="form-check-label d-flex flex-column" for="flexRadioDefault1">
                                            <div class="d-flex align-items-start justify-content-between">
                                                <h6>NEFT/ IMPS/ RTGS</h6>
                                                <a aria-label="link"
                                                    href="{{ route('pi.neft_paid', ['invoice' => $invoice->id]) }}"
                                                    class="btn p-2 ps-3 pe-3 rounded-pill text-white  content "
                                                    style="background-color: #194DAB;">Add Transaction details
                                                </a>
                                            </div>
                                        </label>
                                        <div class="content d-none">
                                            @include('components.payment.bankDetails')
                                        </div>
                                    </div>
                                </div>
                            @endif

                            @if ($hostdata->enableStaticBankAccount || $hostdata->enableRazorPayVirtualAccount)
                                <hr class="m-0">
                                <div class="p-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="flexRadioDefault"
                                            id="flexRadioDefault3">
                                        <label class="form-check-label d-flex flex-column" for="flexRadioDefault3">
                                            <div class="d-flex align-items-start justify-content-between">
                                                <h6>Cheque Deposit</h6>

                                                <a aria-label="link"
                                                    href="{{ route('pi.cheque_paid', ['invoice' => $invoice->id]) }}"
                                                    class="btn p-2 ps-3 pe-3 rounded-pill text-white   content d-none"
                                                    style="background-color: #194DAB;">Add Transaction details
                                                </a>
                                            </div>
                                        </label>
                                        <div class="content d-none">
                                            @include('components.payment.bankDetails')
                                        </div>
                                    </div>
                                </div>
                            @endif


                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-4  col-md-12 col-sm-12">
                @include('userpages.cart.cartCalculations')
            </div>
        </div>
    @endsection
    @section('PAGE-script')
        @include('components.payment.paytmGatewayScript')
        @include('components.payment.paytmQrScript')
        <script>
            $(document).ready(function() {
                $('input[type="radio"][name="flexRadioDefault"]').change(function() {
                    $('.content').addClass('d-none');
                    $(this).closest('.form-check').find('.content').toggleClass('d-none');
                });

            });
        </script>
    @endsection
