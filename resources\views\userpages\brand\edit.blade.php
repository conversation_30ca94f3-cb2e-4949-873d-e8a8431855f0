@extends('userpages.layouts.projBrandMain')
@section('title', 'Brand Edit')

@section('proj-pages')
    <div class="bg-white d-flex align-items-center justify-content-between gap-3 pt-2 pb-2 pe-3 ps-3 mb-3"
        style="border-radius: 7px;">
        <h4 class="heading-font">Brand Edit</h4>
        <a aria-label="link" href="{{ url()->previous() }}" class="btn text-white btn-sm rounded-pill"
            style="background-color: #194DAB;">
            Cancel
        </a>
    </div>
    <form action="{{ route('brand.update', ['brand' => Crypt::encrypt($brand->id)]) }}" class="form" method="post"
        enctype="multipart/form-data">
        @csrf
        @method('patch')
        <div class="card mb-5">
            <div class="card-body">
                <div class="row text-capitalize w-100">
                    <div class="col-lg-6 col-md-6 col-sm-12 pe-lg-7 pe-sm-3">
                        <div class="mb-3 ">
                            <label for="nameinput" class=" fs-5  form-label  required">name </label>
                            <input type="text" class="form-control @error('brandname') is-invalid @enderror" id="nameinput"
                                value="{{ old('brandname') ?? ($brand->name ?? '') }}" name="brandname" placeholder="Name"
                                required>
                            @error('brandname')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        <div class="mb-3 fs-5">
                            <label class="form-label fs-5  ">
                                Brand logo</label>
                            <div class=" form-control fs-7 text-gray-500">
                                <label for="logoinput"
                                    class="btn btn-outline btn-outline-primary btn-sm fw-semibold shadow-sm btn-outline-dashed">
                                    <i class="bi bi-plus-lg"></i>
                                    Select logo</label>
                                <input type="file" class="d-none @error('logo') is-invalid @enderror" id="logoinput" name="logo" accept="image/*"
                                    placeholder="Select logo Here ...">
                                <div class="" id="fileNameDisplay"></div>
                                @error('logo')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="mb-3 ">
                            <label for="websiteinput" class=" fs-5  form-label">website</label>
                            <input type="text" class="form-control @error('website') is-invalid @enderror" id="websiteinput"
                                value="{{ old('website') ?? ($brand->website ?? '') }}" name="website"
                                placeholder="https://www.MyWebsite.com">
                            @error('website')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        <div class="mb-3">
                            <label class="form-label fs-5  ">
                                Attachments
                            </label>
                            <div class="form-control  text-gray-500 ">
                                <label for="attachmentFile"
                                    class=" btn btn-outline btn-outline-primary btn-sm fw-semibold shadow-sm btn-outline-dashed">
                                    <i class="bi bi-plus-lg"></i>
                                    Atachments</label>
                                <input type="file" class="d-none @error('attachment') is-invalid @enderror" id="attachmentFile" name="attachment[]"
                                    placeholder="Attachment" multiple onchange="updateList()">
                                <div class="" id="fileList"></div>
                                @error('attachment')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <span class="  text-secondary " style="font-size: 12px">Note: The maximum file size
                                allowed is 50 MB , and only 'pdf','zip','gif', 'png', 'jpg', and 'jpeg' are
                                accepted.</span>
                        </div>
                    </div>
                    <div class="col-lg-6 col-md-6 col-sm-12 ps-lg-7 ps-sm-3">
                        <div class="mb-3 ">
                            <div class="mb-2">
                                <label for="descriptionText" class=" fs-5 form-label required d-block">Description
                                </label>
                            </div>
                            <textarea id="descriptionText" class="text-lowercase mt-3 @error('description') is-invalid @enderror" rows="8" name="description"
                                placeholder="Write brand description here...">{{ old('description') ?? ($brand->description ?? '') }}</textarea>
                            @error('description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <span class="  text-secondary " style="font-size: 12px">
                                Create checklists and format your paragraphs to make your description easier to
                                read and follow. Better designs result from well-written instructions.
                            </span>
                        </div>
                        <div class="mb-3 ">
                            <label for="colorText" class=" fs-5  form-label">Colors </label>
                            <textarea id="colorText" rows="2" class="form-control @error('colors') is-invalid @enderror" name="colors"
                                placeholder="Feel free to describe your colors here...">{{ old('colors') ?? ($brand->colors ?? '') }}</textarea>
                            @error('colors')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        <div class="mb-3 ">
                            <label for="guidelineText" class=" fs-5  form-label">Guideline</label>
                            <textarea id="guidelineText" rows="2" class="form-control @error('guideline') is-invalid @enderror" name="guideline"
                                placeholder="Write brand guidelines here...">{{ old('guideline') ?? ($brand->guideline ?? '') }}</textarea>
                            @error('guideline')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>
            </div>

            <div class="card-footer bg-white">
                <div class="col text-end">
                    <button  type="submit" class="btn text-white rounded-pill" style="background-color: #194DAB;">Update
                            Brand</button>
                    {{-- <button class="btn btn-primary ">Update</button> --}}
                </div>
                <!--end::Col-->
            </div>
        </div>
    </form>
@endsection
@section('PAGE-script')
    <script src="https://cdn.ckeditor.com/ckeditor5/39.0.1/classic/ckeditor.js"></script>
    <script>
        ClassicEditor
            .create(document.querySelector('#descriptionText'))
            .then(editor => {})
            .catch(error => {
                console.error(error);
            });
    </script>
    <script>
        $(document).ready(function() {
            // When the file input changes
            $('#logoinput').on('change', function() {
                var fileName = $(this).val().split('\\').pop(); // Get the selected filename

                $('#fileNameDisplay').wrapInner(
                    "<ul><li class='pt-2'>" + fileName + "</li></ul>"); // Update the filename display
            });
        });
        updateList = function() {
            var input = document.getElementById('attachmentFile');
            var output = document.getElementById('fileList');

            output.innerHTML = '<ul>';
            for (var i = 0; i < input.files.length; ++i) {
                output.innerHTML += '<li class="pt-2">' + input.files.item(i).name + '</li>';
            }
            output.innerHTML += '</ul>';
        }
    </script>
@endsection
