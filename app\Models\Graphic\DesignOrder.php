<?php

namespace App\Models\Graphic;

use App\Models\Graphic\DesignOrderItem;
use App\Models\Order;
use App\Models\OrderItems;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class DesignOrder extends Model
{
    use HasFactory;
    protected $table = 'graphic_design_order';
    public $timestamps = false;

    public function items()
    {
        return $this->hasMany(DesignOrderItem::class, 'graphic_design_order_id', 'id');
    }

    public function order()
    {
        return $this->belongsTo(Order::class, 'order_id', 'id');
    }

    public function orderItem()
    {
        return $this->belongsTo(OrderItems::class, 'order_item_id', 'id');
    }
}
