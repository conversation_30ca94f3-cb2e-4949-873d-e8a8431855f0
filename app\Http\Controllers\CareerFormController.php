<?php

namespace App\Http\Controllers;

use App\Models\CareerForm;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class CareerFormController extends Controller
{
    public function store(Request $request)
    {
        $validate = Validator::make($request->all(), [

            'fName' => 'required',
            'mobile' => 'required',
            'email' => 'required',
            'position' => 'required',
            'description' => 'required',
            'attachment' => 'required',
        ], [
            'position.required' => 'Select Position You\'re Applying.',
        ], [
            'fName' => 'Full name',
            'mobile' => 'Mobile numbers',
            'email' => 'E-Mail',
            'position' => 'Position',
            'description' => 'Description',
            'attachment' => 'Resume',
        ]);
        if ($validate->fails()) {
            return redirect()->back()->withErrors($validate)->withInput();
        }
        try {

            $newCareer = new CareerForm();
            $newCareer->name = $request->fName;
            $newCareer->mobile = $request->mobile;
            $newCareer->email = $request->email;
            $newCareer->description = $request->description;
            $newCareer->positionApplyFor = $request->position;
            if ($request->hasFile('attachment')) {
                $path = s3_fileUpload($request->file('attachment'), 'website_career_form');
                $newCareer->file=$path;
            }
            $newCareer->save();
        } catch (\Exception $e) {
          
            return redirect()->back()->with('error',ErrMsg())->withInput();
        }

        return redirect()->back()->with("success", "Thank you for submitting your application. We will promptly contact you at our earliest convenience.");
    }
}
