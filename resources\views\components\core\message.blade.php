<div id="toast-container" class="position-fixed end-0 p-3" style="z-index: 1500;top: 100px!important;">
    @php
        $alertTypes = [
            'success' => ['icon' => 'fa-circle-check', 'color' => 'text-success', 'bg' => 'bg-success-subtle', 'title' => 'Success'],
            'warning' => ['icon' => 'fa-triangle-exclamation', 'color' => 'text-warning', 'bg' => 'bg-warning-subtle', 'title' => 'Warning'],
            'error' => ['icon' => 'fa-circle-xmark', 'color' => 'text-danger', 'bg' => 'bg-danger-subtle', 'title' => 'Error'],
            'info' => ['icon' => 'fa-circle-info', 'color' => 'text-info', 'bg' => 'bg-info-subtle', 'title' => 'Info'],
            'added_cart' => ['icon' => 'add-to-cart.png', 'color' => '', 'bg' => 'bg-primary-subtle', 'title' => 'Product Added', 'isImage' => true]
        ];
    @endphp

    @foreach ($alertTypes as $type => $alert)
        @if (Session::has($type))
            <div class="toast show {{ $alert['bg'] }} border-0" role="alert" aria-live="assertive" aria-atomic="true">
                <div class="toast-header border-0 bg-transparent">
                    <div class="d-flex align-items-center me-auto">
                        @if (isset($alert['isImage']) && $alert['isImage'])
                            <img src="{{ asset('media/icons/' . $alert['icon']) }}" alt="" class="me-2" style="width: 20px;">
                        @else
                            <i class="fa-regular {{ $alert['icon'] }} {{ $alert['color'] }} me-2"></i>
                        @endif
                        <strong class="{{ $alert['color'] }} ">{{ $alert['title'] }}</strong>
                    </div>
                    <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
                </div>
                <div class="fs-6 fw-normal toast-body">
                    {{ Session::get($type) }}
                </div>
            </div>
        @endif
    @endforeach
</div>

@if (Session::has('success') ||
        Session::has('info') ||
        Session::has('warning') ||
        Session::has('error') ||
        Session::has('added_cart'))
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(function() {
                const toasts = document.querySelectorAll('.toast');
                toasts.forEach(toast => {
                    const bsToast = new bootstrap.Toast(toast);
                    bsToast.hide();
                });
            }, 10000);
        });
    </script>
@endif
