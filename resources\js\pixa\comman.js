const navbarToggler = document.querySelector(".navbar-toggler");
const navbarContent = document.querySelector(".navbar-collapse");
const dropdowns = document.querySelectorAll(".dropdown");

navbarToggler.addEventListener("click", toggleMenu);

function toggleMenu() {
    navbarToggler.classList.toggle("active");
    navbarContent.classList.toggle("show");

    dropdowns.forEach((dropdown) => {
        dropdown.querySelector("ul.dropdown-menu").classList.remove("show");
    });
}

dropdowns.forEach((dropdown) => {
    dropdown.addEventListener("mouseenter", showDropdown);
    dropdown.addEventListener("mouseleave", hideDropdown);
});

function showDropdown() {
    if (window.innerWidth > 991) {
        this.querySelector("ul.dropdown-menu").classList.add("show");
    }
}

function hideDropdown() {
    if (window.innerWidth > 991) {
        this.querySelector("ul.dropdown-menu").classList.remove("show");
    }
}

navbarToggler.addEventListener("click", function () {
    navbarCollapse.classList.toggle("show");
});

function showMenu() {
    document.getElementById("home").classList.toggle("homedirection");
    document.getElementById("first").classList.toggle("showhead");

    document.getElementById("second").classList.toggle("showhead");
}


var screenWidth = window.screen.width;
var slidesPerView1 = (screenWidth <= 767) ? 2 : 3;
var swiper = new Swiper(".mySwiper", {
    slidesPerView: slidesPerView1,
    spaceBetween: 4,
    centeredSlides: true,
    autoplay: {
        delay: 2500,
        disableOnInteraction: false,
    },
    loop: true,
    pagination: {
        el: ".swiper-pagination",
        clickable: true,
    },
    navigation: {
        nextEl: ".swiper-button-next",
        prevEl: ".swiper-button-prev",
    },
});

// ---------------------------Accordion js---------------------------------------------------
document.addEventListener("DOMContentLoaded", function () {
    var collapseButtons = document.querySelectorAll('[data-bs-toggle="collapse"]');
    var collapseSections = document.querySelectorAll('.div-collapse');

    collapseButtons.forEach(function (button) {
        button.addEventListener("click", function () {
            var target = document.querySelector(button.getAttribute("href"));
            var isTargetOpen = target.classList.contains("show");

            collapseSections.forEach(function (section) {
                if (section !== target && section.classList.contains('show')) {
                    section.style.maxHeight = "0";
                    section.addEventListener("transitionend", function () {
                        section.classList.remove("show");
                    }, { once: true });
                }
            });

            if (!isTargetOpen) {
                target.classList.add("show");
                target.style.maxHeight = target.scrollHeight + "px";
            } else {
                target.style.maxHeight = "0";
                target.addEventListener("transitionend", function () {
                    target.classList.remove("show");
                }, { once: true });
            }
        });
    });
});

// -------------------------------------------------------------------------------------------
function reveal() {
    var reveals = document.querySelectorAll(".reveal");

    for (var i = 0; i < reveals.length; i++) {
        var windowHeight = window.innerHeight;
        var elementTop = reveals[i].getBoundingClientRect().top;
        var elementVisible = 200;

        if (elementTop < windowHeight - elementVisible) {
            reveals[i].classList.add("active");
        } else {
            reveals[i].classList.remove("active");
        }
    }
}
window.addEventListener("scroll", reveal);
// -------------------------------------------------------------------------------------------

//   change toggler icon

$(document).ready(function () {
    $('.navbar-toggler').click(function () {
        //   $(this).toggleClass('active');
        if ($('.navbar-collapse').hasClass('show')) {
            $('.navbar-toggler-icon').removeClass('navbar-toggler-icon').addClass('navbar-cross-icon');
            $("#changeIcon1").css("display", "none");
            $("#changeIcon2").removeClass('d-none').addClass('d-block');
        } else {
            $('.navbar-cross-icon').removeClass('navbar-cross-icon').addClass('navbar-toggler-icon');
            $("#changeIcon1").css("display", "block");
            $("#changeIcon2").removeClass('d-block').addClass('d-none');
        }
    });
});

// space to set for both header to show it properly

var prevScrollPos = window.pageYOffset;
var globalHeader = document.querySelector('.global-header');
var navbarHeader = document.querySelector('.navbar-header');
var showGlobalHeader = true;

window.addEventListener('scroll', function () {
    var currentScrollPos = window.pageYOffset;

    if (currentScrollPos <= 0) {
        showGlobalHeader = true;
    }

    if (showGlobalHeader) {
        globalHeader.style.top = "0";
        navbarHeader.style.top = "2rem";
    } else {
        globalHeader.style.top = "-3rem";
        navbarHeader.style.top = "0";
    }

    if (currentScrollPos == 0) {
        showGlobalHeader = true;
        navbarHeader.style.top = "2rem";
        globalHeader.style.top = "0";
    } else {
        showGlobalHeader = false;
    }

    prevScrollPos = currentScrollPos;
});

