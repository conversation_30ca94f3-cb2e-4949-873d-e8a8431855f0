<?php
    $isSwiper = 'swiper-wrapper';
    $swiperContainer = 'mySwiper swiper';
    if ($category && $category->products->count() < 3) {
        $isSwiper = 'd-flex no-swiper gap-3 overflow-auto';
        $swiperContainer = 'w-auto overflow-auto';
    }
?>

<?php if($category && $category->products->count()): ?>
    <div class="container-lg">
        <div class="<?php echo e($swiperContainer); ?>">
            <div class="pt-lg-3 pt-md-3 pt-4 justify-content-center <?php echo e($isSwiper); ?>" style="min-width: max-content">
                <?php $__currentLoopData = $category->products; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <?php
                        $width = $category->products->count() < 3 ? 'w-lg-50 w-md-75 w-sm-100' : 'swiper-slide';
                        $btnTextColor = '#ffffff';
                        $btnBgColor = $loop->iteration % 2 == 0 ? '#2c8cf4' : '#2D2DB0';
                    ?>

                    <?php if($product && $product->isActive): ?>
                        <div class="h-auto mb-3 <?php echo e($width); ?>">
                            <div class="bg-white d-flex flex-column h-100 justify-content-between p-4 plan-card-box rounded-4 work-sans"
                                style="z-index:3;">
                                <div class="text-center">
                                    <?php if(count($product->variants)): ?>
                                        <a href="<?php echo e(route('product.view', ['categoryId' => $product->category->id, 'category' => strtolower(str_replace(' ', '-', $product->category->name)), 'productVariant' => $product->variants[0]->id])); ?>"
                                            class="text-decoration-none">
                                            <h4 class="fw-semibold fs-3 text-wrap text-secondary-emphasis">
                                                <?php echo e($product->name); ?>

                                            </h4>
                                        </a>
                                    <?php else: ?>
                                        <h4 class="fw-semibold fs-3 text-wrap text-secondary-emphasis">
                                            <?php echo e($product->name); ?>

                                        </h4>
                                    <?php endif; ?>

                                    <?php if(auth()->check() && !auth()->user()->hidePrice && ($product->showPriceAnonymous || $product->showPriceAfterLogin)): ?>
                                        <?php if($product->variants->count()): ?>
                                            <div class="d-flex justify-content-center mb-3">
                                                <select class="variant-select form-select" style="width: max-content;">
                                                    <?php $__currentLoopData = $product->variants; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $var): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                        <option value="<?php echo e($var->id); ?>"
                                                            data-variant="<?php echo e($var->id); ?>"
                                                            data-product="<?php echo e($product->id); ?>"
                                                            data-sellprice="<?php echo e($var->sellingPrice); ?>"
                                                            data-mrp="<?php echo e($var->mrp); ?>"
                                                            data-link="<?php echo e(route('cart.add', ['product_id' => $var->id])); ?>"
                                                            <?php if($loop->first): echo 'selected'; endif; ?>>
                                                            <?php echo e($var->name); ?>

                                                        </option>
                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                </select>
                                            </div>
                                            <div class="d-flex justify-content-center align-items-center gap-2 product">
                                                <h5 class="fw-normal fs-5 mrpPrice">
                                                    <del
                                                        id="<?php echo e('productMrp' . $product->id); ?>">₹<?php echo e($product->variants[0]->mrp); ?></del>
                                                </h5>
                                                <h4 class="fw-semibold fs-4 sellingPrice"
                                                    id="<?php echo e('productSelling' . $product->id); ?>">
                                                    ₹<?php echo e($product->variants[0]->sellingPrice); ?></h4>
                                            </div>
                                        <?php endif; ?>
                                    <?php endif; ?>
                                </div>

                                
                                <div class="d-flex  text-center justify-content-center flex-column gap-4">
                                    <?php if($product->variants->count()): ?>
                                        <?php if(auth()->check() && auth()->user()->hidePrice == 0): ?>
                                            <?php if($product->showPriceAnonymous || $product->showPriceAfterLogin): ?>
                                                <div class="p-2">
                                                    <a href="<?php echo e(route('cart.add', ['product_id' => $product->variants[0]->id])); ?>"
                                                        id="<?php echo e('productLink' . $product->id); ?>"
                                                        class="btn btn-lg fw-medium text-white w-75 text-nowrap"
                                                        style="background-color: <?php echo e($btnBgColor); ?>; color: <?php echo e($btnTextColor); ?>">
                                                        Add to Cart
                                                    </a>
                                                </div>
                                            <?php endif; ?>
                                        <?php endif; ?>
                                    <?php endif; ?>

                                    <?php if(!auth()->check() && $product->getQuoteAnonymous): ?>
                                        <div class="p-2">
                                            <a href="<?php echo e(route('quotation.create', ['product' => $product->id])); ?>"
                                                class="btn btn-lg fw-medium text-white w-75 text-nowrap"
                                                style="background-color: <?php echo e($btnBgColor); ?>; color: <?php echo e($btnTextColor); ?>">Get
                                                Quotation</a>
                                        </div>
                                    <?php endif; ?>

                                    <?php if(auth()->check() && auth()->user()->hidePrice == 0 && $product->getQuoteAfterLogin): ?>
                                        <div class="p-2">
                                            <a href="<?php echo e(route('quotation.create', ['product' => $product->id])); ?>"
                                                class="btn btn-lg fw-medium text-white w-75 text-nowrap"
                                                style="background-color: <?php echo e($btnBgColor); ?>; color: <?php echo e($btnTextColor); ?>">Get
                                                Quotation</a>
                                        </div>
                                    <?php endif; ?>
                                </div>

                                <div class="d-flex flex-column mx-2 gap-2 py-3" style="font-size: 1rem;">
                                    <?php $__currentLoopData = $product->features; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <div class="d-flex gap-2 align-items-baseline">
                                            <div style="width: 10%">
                                                <i class="<?php echo e($item->iconClass ?? 'fa-solid fa-check'); ?>"
                                                    style="color: <?php echo e($item->iconColorHex ?? '#2d45c1'); ?>;"></i>
                                            </div>
                                            <div class="text-start"><?php echo e($item->feature); ?></div>
                                        </div>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </div>

                                <div class="text-center">
                                    <?php if($product->variants->count()): ?>
                                        <a href="<?php echo e(route('product.view', ['categoryId' => $product->category->id, 'category' => strtolower(str_replace(' ', '-', $product->category->name)), 'productVariant' => $product->variants[0]->id])); ?>"
                                            class="text-decoration-none">Read More</a>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
            <div class="mt-5">
                <div class="swiper-pagination"></div>
            </div>
        </div>
    </div>
<?php endif; ?>
<?php /**PATH C:\Users\<USER>\Desktop\live\websites_laravel\resources\views/rapbooster/plans/showPlans.blade.php ENDPATH**/ ?>