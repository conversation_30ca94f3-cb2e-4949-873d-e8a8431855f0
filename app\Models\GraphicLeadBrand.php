<?php

namespace App\Models;

use App\Models\GraphicBrandAttachments;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class GraphicLeadBrand extends Model
{
    use HasFactory;

    protected $table = 'graphic_lead_brand';
    public $timestamps = false;

    public function attachments()
    {
        return $this->hasMany(GraphicBrandAttachments::class, 'graphic_lead_brand_id', 'id');
    }
}
