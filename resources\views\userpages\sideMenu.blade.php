<div class="d-flex flex-column justify-content-between bg-white overflow-auto p-2"
    style="height: 80vh; border-radius: 8px;">
    <div class="menu c-menu flex-column ">
        @php
            $menuItems = [
                [
                    'route' => 'user.profile',
                    'icon' => 'fa-solid fa-user',
                    'title' => 'Profile',
                ],
                [
                    'route' => 'user.address',
                    'icon' => 'fa-solid fa-user-tag',
                    'title' => 'Billing Profile',
                ],
                [
                    'route' => 'user.s.tickets',
                    'icon' => 'fa-solid fa-flag',
                    'title' => 'Support Tickets',
                    'condition' => 'support*',
                ],
                [
                    'route' => 'user.invoices',
                    'icon' => 'fa-solid fa-clipboard-list',
                    'title' => 'Invoices',
                ],
                [
                    'route' => 'user.licences',
                    'icon' => 'fa-solid fa-address-card',
                    'title' => 'Subscriptions',
                ],
                [
                    'route' => 'graphics.orders',
                    'icon' => 'fa fa-briefcase',
                    'title' => 'Graphic Project',
                    // 'useRawIcon' => true,
                    'condition' => 'graphics*',
                ],
                [
                    'route' => 'brand.index',
                    'icon' => 'bi bi-box2-heart-fill',
                    'title' => 'Graphic Brand',
                    // 'useRawIcon' => true,
                    'condition' => '*brand*',
                ],
            ];
            // <i class="bi bi-box2-heart-fill"></i>

        @endphp

        @foreach ($menuItems as $item)
            <a aria-label="link" href="{{ route($item['route']) }}" class="text-decoration-none w-100">
                <div
                    class="pt-1 pb-1 d-flex ps-2 {{ isset($item['condition']) ? (request()->is($item['condition']) ? 'c-bg-light-blue text-white' : 'side-menu-item text-secondary') : (Route::is($item['route']) ? 'c-bg-light-blue text-white' : 'side-menu-item text-secondary') }}">
                    <div class="p-1 d-flex justify-content-between align-items-center pe-4">
                        <div class="d-flex align-items-center fs-5 mid-gap">
                            <span class="" style="width: 30px; height: 30px;">
                                @if (isset($item['useRawIcon']) && $item['useRawIcon'])
                                    {!! $item['icon'] !!}
                                @else
                                    <i class="{{ $item['icon'] }}"></i>
                                @endif
                            </span>
                            <div class="text-left">{{ $item['title'] }}</div>
                        </div>
                    </div>
                </div>
            </a>
        @endforeach


        {{--
        <a aria-label="link" href="{{ route('graphic.brand') }}" class=" text-decoration-none  w-100    ">
                <div class="pt-1 pb-1 d-flex  ps-2 {{ Route::is('graphic.brand') ? 'c-bg-light-blue  text-white' : 'side-menu-item text-secondary' }}">
        <div class="p-1 d-flex justify-content-between align-items-center pe-4">
            <div class="d-flex align-items-center fs-5 mid-gap">
                <span class="" style="    width: 30px;    height: 30px;">
                    <i class="fa-solid fa-crown"></i>
                </span>
                <div class=" text-left">Brands</div>
            </div>

        </div>
</div>
</a>
--}}
        {{--
        <a aria-label="link" href="{{ route('graphic.designGallery') }}" class=" text-decoration-none  w-100    ">
                 <div class="pt-1 pb-1 d-flex  ps-2 {{ Route::is('graphic.designGallery') ? 'c-bg-light-blue  text-white' : 'side-menu-item text-secondary' }}">
    <div class="p-1 d-flex justify-content-between align-items-center pe-4">
        <div class="d-flex align-items-center fs-5 mid-gap">
            <span class="" style="    width: 30px;    height: 30px;">
                <i class="fa-solid fa-images"></i>
            </span>
            <div class=" text-left">Design Gallery</div>
        </div>

    </div>
    </div>
</a>
--}}
        <a aria-label="link" href="{{ route('user.transactions') }}" class=" text-decoration-none  w-100    ">
            <div
                class="pt-1 pb-1 d-flex  ps-2 {{ Route::is('user.transactions') ? 'c-bg-light-blue  text-white' : 'side-menu-item text-secondary' }}">
                <div class="p-1 d-flex justify-content-between align-items-center pe-4">
                    <div class="d-flex align-items-center fs-5 mid-gap">
                        <span class="" style="    width: 30px;    height: 30px;">
                            <i class="fa-brands fa-cc-mastercard"></i>
                        </span>
                        <div class=" text-left">Transactions</div>
                    </div>

                </div>
            </div>
        </a>
        {{--
<a aria-label="link" href="{{ route('user.refer.index') }}" class=" text-decoration-none w-100  ">
<div class="pt-1 pb-1 d-flex  ps-2 {{ Route::is('user.refer*') ? 'c-bg-light-blue  text-white' : 'side-menu-item text-secondary' }}">
    <div class="p-1 d-flex justify-content-between align-items-center pe-4">
        <div class="d-flex align-items-center fs-5 mid-gap">
            <span class="" style="    width: 30px;    height: 30px;">
                <i class="fa-solid fa-coins"></i>
            </span>
            <div class=" text-left">Refer & Earn</div>
        </div>

    </div>
</div>
</a>
--}}
    </div>

    <a aria-label="link" href="{{ route('logout') }}" class=" text-decoration-none  w-100   ">
        <div class="pt-1 pb-1 d-flex  res-sign-out ms-2 align-items-center">
            <div class="d-flex fs-5 res-mid-gap text-secondary">
                <span class="">
                    <i class="fa-solid fa-circle-left fs-sign-out"></i>
                </span>
                <div class="d-flex flex-column">
                    <div class="text-start  res-pad-start">Sign Out</div>
                    {{-- <div class="text-start  res-pad-start">{{ auth()->user()->email ?? '' }}</div> --}}
                </div>
            </div>
        </div>
    </a>
</div>
