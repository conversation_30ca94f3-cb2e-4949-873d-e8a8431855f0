const navbarToggler = document.querySelector(".navbar-toggler");
const navbarContent = document.querySelector(".navbar-collapse");
// const dropdowns = document.querySelectorAll(".dropdown");
// var navbarDropdown = document.getElementById("navbarDropdown");
// var paidads = document.getElementById("paidads");

navbarToggler.addEventListener("click", toggleMenu);

function toggleMenu() {
    navbarToggler.classList.toggle("active");
    navbarContent.classList.toggle("show");

    dropdowns.forEach((dropdown) => {
        dropdown.querySelector("ul.dropdown-menu").classList.remove("show");
    });
}

var paidads = document.getElementById("paidads");
var organads = document.getElementById("organads");
var devads = document.getElementById("devads");

function showPaidAds() {
    if (window.innerWidth < 991) {
        if (paidads.classList.contains("show")) {
            paidads.classList.remove("show");
        } else {
            paidads.classList.add("show");
        }
    }
}

function showOrganAds() {
    if (window.innerWidth < 991) {
        if (organads.classList.contains("show")) {
            organads.classList.remove("show");
        } else {
            organads.classList.add("show");
        }
    }
}

function showDevAds() {
    if (window.innerWidth < 991) {
        if (devads.classList.contains("show")) {
            devads.classList.remove("show");
        } else {
            devads.classList.add("show");
        }
    }
}

window.addEventListener("DOMContentLoaded", (event) => {
    var nav1 = document.getElementById("navItem1");
    var nav2 = document.getElementById("navItem2");
    var nav3 = document.getElementById("navItem3");
    if (window.innerWidth < 991) {
        $("#angle-1").removeClass('d-block').addClass('d-none');
        $("#angle-2").css("display", "block");
        $("#angle-3").removeClass('d-block').addClass('d-none');
        $("#angle-4").css("display", "block");
        $("#angle-5").removeClass('d-block').addClass('d-none');
        $("#angle-6").css("display", "block");
    }
    else {
        nav1.classList.add('dropdown');
        nav2.classList.add('dropdown');
        nav3.classList.add('dropdown');
        $("#angle-1").css("display", "block");
        $("#angle-2").removeClass('d-block').addClass('d-none');
        $("#angle-3").css("display", "block");
        $("#angle-4").removeClass('d-block').addClass('d-none');
        $("#angle-5").css("display", "block");
        $("#angle-6").removeClass('d-block').addClass('d-none');
    }
});


// dropdowns.forEach((dropdown) => {
//     dropdown.addEventListener("click", showDropdown);
//     dropdown.addEventListener("click", hideDropdown);
// });

// function showDropdown() {
//     if (window.innerWidth > 991) {
//         this.querySelector("ul.dropdown-menu").classList.add("show");
//     }
// }

// function hideDropdown() {
//     if (window.innerWidth > 991) {
//         this.querySelector("ul.dropdown-menu").classList.remove("show");
//     }
// }

// navbarToggler.addEventListener("click", function () {
//     navbarCollapse.classList.toggle("show");
// });

// function showMenu() {
//     document.getElementById("home").classList.toggle("homedirection");
//     document.getElementById("first").classList.toggle("showhead");

//     document.getElementById("second").classList.toggle("showhead");
// }

//   change toggler icon

$(document).ready(function () {
    $('.navbar-toggler').click(function () {
        //   $(this).toggleClass('active');
        if ($('.navbar-collapse').hasClass('show')) {
            $('.navbar-toggler-icon').removeClass('navbar-toggler-icon').addClass('navbar-cross-icon');
            $("#changeIcon1").css("display", "none");
            $("#changeIcon2").removeClass('d-none').addClass('d-block');
        } else {
            $('.navbar-cross-icon').removeClass('navbar-cross-icon').addClass('navbar-toggler-icon');
            $("#changeIcon1").css("display", "block");
            $("#changeIcon2").removeClass('d-block').addClass('d-none');
        }
    });
});


// space to set for both header to show it properly

var prevScrollPos = window.pageYOffset;
var globalHeader = document.querySelector('.global-header');
var navbarHeader = document.querySelector('.navbar-header');
var showGlobalHeader = true;

window.addEventListener('scroll', function () {
    var currentScrollPos = window.pageYOffset;

    if (currentScrollPos <= 0) {
        showGlobalHeader = true;
    }

    if (showGlobalHeader) {
        globalHeader.style.top = "0";
        navbarHeader.style.top = "2rem";
    } else {
        globalHeader.style.top = "-3rem";
        navbarHeader.style.top = "0";
    }

    if (currentScrollPos == 0) {
        showGlobalHeader = true;
        navbarHeader.style.top = "2rem";
        globalHeader.style.top = "0";
    } else {
        showGlobalHeader = false;
    }

    prevScrollPos = currentScrollPos;
});