<?php

namespace App\Http\Controllers;

use Exception;

use Illuminate\Http\Request;
use App\Models\GraphicLeadBrand;
use Aws\S3\Exception\S3Exception;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Validation\Rules\File;
use App\Models\GraphicBrandAttachments;
use Illuminate\Support\Facades\Validator;

class  brandController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $data['sort'] = $sort = 'DESC';
        if (isset($_GET['sort'])) {

            $data['sort'] = $sort = $_GET['sort'] == 'DESC' ? 'ASC' : 'DESC';
        }


        if (isset($_GET['search'])) {

            $data['search'] = $src = $_GET['search'];

            $data['brands'] = GraphicLeadBrand::orderBy('ts', $sort)
                ->where('ts', 'like', '%' . $src . '%')
                ->orWhere('guideline', 'like', '%' . $src . '%')
                ->orWhere('colors', 'like', '%' . $src . '%')
                ->orWhere('website', 'like', '%' . $src . '%')
                ->orWhere('name', 'like', '%' . $src . '%')
                ->orWhere('description', 'like', '%' . $src . '%')
                ->get();
            $data['brands'] = $data['brands']->where('lead_id', Auth::user()->id);
        } else {
            $data['brands'] = GraphicLeadBrand::orderBy('ts', $sort)
                ->where('lead_id', Auth::id())
                ->get();
        }

        return view('userpages.brand.index', $data);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('userpages.brand.add');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        // dd($request);

        $validator = Validator::make(
            $request->all(),
            [
                'brandname' => 'required|max:100',
                'description' => 'required',
                'logo' =>  [
                    'nullable',
                    File::types(['png', 'jpg', 'jpeg'])
                        ->max(10 * 1024),
                ],
                'attachment.*' =>  [
                    'nullable',
                    File::types(['png', 'jpg', 'jpeg', 'pdf', 'zip', 'gif'])
                        ->max(50 * 1024),
                ],
            ],
            [
                'description.required' => 'Please Enter Brand Details.',
                'logo.size' => 'Logo : Maximum file size limit exceeded. Please upload a file that is smaller than 50 MB.',
                'attachment.size' => 'Attachments : Maximum file size limit exceeded. Please upload file that is smaller than 200 MB.',

            ],
            [
                'brandname' => 'Brand name',
                'description' => 'Description',
                'logo' => 'Logo',
            ]
        );
        if ($validator->fails()) {
            // dd($validator);
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        $brand = new GraphicLeadBrand;
        $brand->name = $request->brandname;
        $brand->description = $request->description;
        $brand->colors = $request->colors;
        $brand->guideline = $request->guideline;
        $brand->website = $request->website;
        $brand->lead_id = Auth::id() ?? '1';
        if ($request->hasFile('logo')) {
            $file = $request->logo;

            $filename = s3_fileUpload($file, 'graphic_lead_brand');
            if ($filename) {
                $brand->logo = $filename;
                // $data->attachment = $path;
            } else {
                $brand->save();
                return redirect()->back()->with('info', "Brand created Successfully. however, it appears that your logo has not been uploaded.");
            }
        }
        $brand->save();
        $msg = 'Brand created Successfully.';
        $files = $request->file('attachment');
        if ($files != null) {
            foreach ($files as $file) {
                $attach = new GraphicBrandAttachments;
                $path = s3_fileUpload($file, 'graphic_brand_attachment');
                if ($path) {
                    $attach->graphic_lead_brand_id = $brand->id;
                    $attach->file = $path;
                    $attach->by_lead_id = Auth::id();
                    $attach->save();
                } else {
                    $msg = 'Brand created Successfully.however, it appears that your attatchments has not been uploaded.';
                }
                   }
        }
        // dd($brand);
        return redirect()->route('brand.index')->with('success', $msg);
    }


    /**
     * Display the specified resource.
     */
    public function show($brand)
    {

        $brand = GraphicLeadBrand::where('id', $brand)->with('attachments')->first();

        return view('userpages.brand.show', compact('brand'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit($brand)
    {
        $brand = GraphicLeadBrand::find(Crypt::decryptString($brand));
        return view('userpages.brand.edit', compact('brand'));
    }

    /**
     * Update the specified resource in storage.
     */

    public function update(Request  $request, $brand)

    {
        $validator = Validator::make(
            $request->all(),
            [
                'brandname' => 'required|max:100',
                'description' => 'required',
                'logo' =>  [
                    'nullable',
                    File::types(['png', 'jpg', 'jpeg'])
                        ->max(10 * 1024),
                ],
                'attachment.*' =>  [
                    'nullable',
                    File::types(['png', 'jpg', 'jpeg', 'pdf', 'zip', 'gif'])
                        ->max(50 * 1024),
                ],
            ],
            [
                'brandname' => 'Brand name',
                'description' => 'Description',
                'logo' => 'Logo',
            ]
        );
        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }
        try {
            $data = GraphicLeadBrand::where('id', Crypt::decrypt($brand))->first();
            $data->name = $request->brandname;
            $data->description = $request->description;
            $data->website = $request->website;
            $data->guideline = $request->guideline;
            $data->colors = $request->colors;
            $data->lead_id = Auth::id() ?? 1;
            if ($request->hasFile('logo')) {
                $file = $request->file('logo');
                try {
                    $path = s3_fileUpload($file, 'graphic_lead_brand');
                } catch (S3Exception $e) {
                    return redirect()->back()->with('error', $e->getMessage())->withInput();
                }
                if ($data->logo) {
                    $delete = s3_fileDelete($data->logo, 'graphic_lead_brand');
                }
                $data->logo = $path;
            }
            $data->update();
        } catch (Exception $e) {
            return redirect()->back()->with('error', $e->getMessage())->withInput();
        }
        $files = $request->file('attachment');
        if ($files != null) {
            try {
                foreach ($files as $file) {
                    try {
                        $path = s3_fileUpload($file, 'graphic_brand_attachment');
                    } catch (S3Exception $e) {
                        return redirect()->back()->with('error', $e->getMessage())->withInput();
                    }
                    $attach = new GraphicBrandAttachments;
                    $attach->graphic_brand_id = $data->id;
                    $attach->file = $path;
                    $attach->by_lead_id = Auth::id() ?? '1';
                    $attach->save();
                }
            } catch (Exception $e) {
                return redirect()->back()->with('error', $e->getMessage())->withInput();
            }
        }
        return redirect()->route('brand.show', ['brand' => $data->id])->with('success', 'Updated Successfully');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $brand)
    {
        $brand = GraphicLeadBrand::where('id', $brand)->with('attachments')->first();
        try {
            DB::beginTransaction();
            if ($brand->attachments) {
                foreach ($brand->attachments as $items) {
                    s3_fileDelete($items->file, 'graphic_brand_attachment');
                    $items->delete();
                }
            }
            $brand->delete();
            DB::commit();
        } catch (Exception $e) {
            DB::rollBack();
            return redirect()->back()->with('error', 'Some error while deleting ?');
        } catch (S3Exception $e) {
            return redirect()->back()->with('error', 'Some error while deleting ?');
        }
        return redirect()->route('brand.index')->with('success', 'Brand has been deleted successfully');
    }

    public function destroyAttachment($attach)
    {
        try {
            $data = GraphicBrandAttachments::where('id', $attach)->first();
            s3_fileDelete($data->file, 'graphic_brand_attachment');
            $data->delete();
        } catch (Exception $e) {
            return redirect()->back()->with('error', 'Some error while deleting ?');
        } catch (S3Exception $e) {
            return redirect()->back()->with('error', 'Some error while deleting ?');
        }
        return redirect()->back()->with('success', 'Succeccfully Deleted Attachment');
    }
}
