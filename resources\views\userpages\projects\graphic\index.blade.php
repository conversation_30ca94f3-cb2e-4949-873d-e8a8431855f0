@extends('userpages.main')



@section('userpagesection')
    {{-- @foreach ($plan as $items)
        <div class="bg-white p-2 mb-2 d-flex align-items-center justify-content-between generate-btn p-lg-2 p-md-2 p-sm-3 p-4"
            style="border-radius: 7px;">
            <p class="m-0">{{ $items['plan']->title }} :<strong> {{ count($activeprojects[$items['plan']->id]) }}
                    Creatives out of {{ $items['noOfProjects'] }} |<span style="color: #FD2C2C;">
                        Expiry Date : {{ humanDate($items->endDate) }}</span></strong></p>
            <a href="{{ route('graphic.generateRequest', ['plan' => $items->id]) }}">
                <button
                    class="btn-bg-blue rounded-pill text-nowrap  border-0 text-white ps-4 pe-4 pt-1 pb-1 d-flex align-items-center gap-3"
                    style="margin: 0 auto;min-width: 235px;">
                    Generate your request
                    <i class="fa-solid fa-angle-right text-white"></i>
                </button>
            </a>
        </div>
    @endforeach --}}
    @include('components.core.alerts')
    <div class="d-flex align-items-center gap-lg-3 gap-md-3 gap-2 GD-menu mb-3 p-lg-0 p-md-0 p-3">
        <a aria-label="link" href="#tab1" data-w-tab="tab1" style="width: fit-content;">
            <div class="d-flex flex-column align-items-center bg-white text-black ps-lg-5 pe-lg-5 ps-4 pe-4 pt-3 pb-3 GD-tab"
                style="width: fit-content; border-radius: 7px;">
                <h4 style="font-size: 14px;">All</h4>
            </div>
        </a>
        <a aria-label="link" href="#tab2" data-w-tab="tab2" style="width: fit-content;">
            <div class="d-flex flex-column align-items-center bg-white text-black ps-lg-5 pe-lg-5 ps-md-4 pe-md-4 ps-sm-3 pe-sm-3 ps-2 pe-2 pt-1 pb-1 GD-tab"
                style="width: fit-content; border-radius: 7px;">
                <h4 class="fw-bold">{{ $draftprojects->count() }}</h4>
                <h4 class="GD-tab-heading">Draft Projects</h4>
            </div>
        </a>
        <a aria-label="link" href="#tab3" data-w-tab="tab3" style="width: fit-content;">
            <div class="d-flex flex-column align-items-center bg-white text-black ps-lg-5 pe-lg-5 ps-md-4 pe-md-4 ps-sm-3 pe-sm-3 ps-2 pe-2 pt-1 pb-1 GD-tab"
                style="width: fit-content; border-radius: 7px;">
                <h4 class="fw-bold">{{ $pending->count() }}</h4>
                <h4 class="GD-tab-heading">Pending Projects</h4>
            </div>
        </a>
        <a aria-label="link" href="#tab4" data-w-tab="tab4" style="width: fit-content;">
            <div class="d-flex flex-column align-items-center bg-white text-black ps-lg-5 pe-lg-5 ps-md-4 pe-md-4 ps-sm-3 pe-sm-3 ps-2 pe-2 pt-1 pb-1 GD-tab"
                style="width: fit-content; border-radius: 7px;">
                <h4 class="fw-bold">{{ $revision->count() }}</h4>
                <h4 class="GD-tab-heading">Review Needed</h4>
            </div>
        </a>
        <a aria-label="link" href="#tab5" data-w-tab="tab5" style="width: fit-content;">
            <div class="d-flex flex-column align-items-center bg-white text-black ps-lg-5 pe-lg-5 ps-md-4 pe-md-4 ps-sm-3 pe-sm-3 ps-2 pe-2 pt-1 pb-1 GD-tab"
                style="width: fit-content; border-radius: 7px;">
                <h4 class="fw-bold">{{ $completeprojects->count() }}</h4>
                <h4 class="GD-tab-heading">Completed Projects</h4>
            </div>
        </a>
    </div>
    <div class="bg-white" style="border-radius: 7px;">
        <div class="GD-section-tab-content p-lg-2 p-md-2 p-4" id="tab1">
            <div class="table-responsive" style="max-width: 100%; overflow-x: auto;">
                <table class="table table-striped text-center bg-white">
                    <thead>
                        <tr>
                            <th scope="col">S.No.</th>
                            <th scope="col" class="text-start">List of tasks</th>
                            <th scope="col">Status</th>
                            <th scope="col">Created on</th>
                            <th scope="col">Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        @php
                            $i = $projects->perPage() * ($projects->currentPage() - 1);
                        @endphp
                        @foreach ($projects as $prj)
                            <tr>
                                <th scope="row">{{ ++$i }}</th>
                                <td style="min-width: 170px;">
                                    <div class="d-flex flex-column align-items-baseline gray-dark-text">
                                        {{ $prj->title }}
                                        <p class="m-0" style="font-size: small;color: #9C9C9C;">
                                            {{ str()->limit($prj->description, 30) }}
                                        </p>
                                    </div>
                                </td>
                                <td>
                                    {{--
                                        0 - Draft
                                        1 - Unassigned
                                        2 - Assigned
                                        3 - QC Pending
                                        4 - Sent to Customer
                                        5 - Need Revision
                                        6 - Completed
                                    --}}
                                    @if ($prj->status == 0)
                                        <div
                                            class="rounded-pill text-nowrap  border-0 ps-4 pe-4 pt-1 pb-1 d-flex align-items-center justify-content-center gap-2 status-bg-red">
                                            <i class="fa-solid fa-file-lines"></i>
                                            Draft
                                        </div>
                                    @elseif($prj->status == 1)
                                        <div
                                            class="rounded-pill text-nowrap  border-0 ps-4 pe-4 pt-1 pb-1 d-flex align-items-center justify-content-center gap-2 status-bg-purple">
                                            <i class="fa-solid fa-hourglass-end"></i>
                                            Pending projects
                                        </div>
                                    @elseif($prj->status == 5)
                                        <div
                                            class="rounded-pill text-nowrap  border-0 ps-4 pe-4 pt-1 pb-1 d-flex align-items-center justify-content-center gap-2 status-bg-blue">
                                            <i class="fa-solid fa-eye"></i>
                                            Review needed
                                        </div>
                                    @elseif($prj->status == 6)
                                        <div
                                            class="rounded-pill text-nowrap  border-0 ps-4 pe-4 pt-1 pb-1 d-flex align-items-center justify-content-center gap-2 status-bg-green">
                                            <i class="fa-solid fa-trophy"></i>
                                            Completed projects
                                        </div>
                                    @endif
                                </td>
                                <td class="gray-dark-text" style="min-width: 150px;">
                                    {{ humanDate($prj->ts) }}

                                </td>
                                <td>
                                    <a href="{{ route('graphic.chat') }}">
                                        <button
                                            class="rounded-pill text-nowrap  text-white ps-4 pe-4 pt-1 pb-1 d-flex align-items-center gap-3"
                                            style="margin: 0 auto;border:2px solid #194DAB;background-color: #194DAB;">
                                            <i class="fa-brands fa-rocketchat text-white"></i>
                                            Insights
                                        </button>
                                    </a>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
                @if ($projects->count() == 0)
                    <div class="p-0">
                        <div class=" text-center">
                            <div class=" text-center">
                                <img src="{{ asset('media/images/emptyRecord.png') }}" alt="emptyRecord" class="img-fluid">
                            </div>
                            <h3 style="color: #194DAB">
                                No Project Found In Draft.
                            </h3>
                        </div>
                    </div>
                @endif
            </div>
        </div>
        <div class="GD-section-tab-content p-lg-2 p-md-2 p-4" id="tab2">
            <div class="table-responsive" style="max-width: 100%; overflow-x: auto;">
                <table class="table table-striped text-center bg-white">
                    <thead>
                        <tr>
                            <th scope="col">S.No.</th>
                            <th scope="col" class="text-start">List of tasks</th>
                            <th scope="col">Status</th>
                            <th scope="col">Created on</th>
                            <th scope="col">Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        @php
                            $i = $draftprojects->perPage() * ($draftprojects->currentPage() - 1);
                        @endphp
                        @foreach ($draftprojects as $prj)
                            <tr>
                                <th scope="row">{{ ++$i }}</th>
                                <td style="min-width: 170px;">
                                    <div class="d-flex flex-column align-items-baseline gray-dark-text">
                                        {{ $prj->title }}
                                        <p class="m-0" style="font-size: small;color: #9C9C9C;">
                                            {{ str()->limit($prj->description, 30) }}
                                        </p>
                                    </div>
                                </td>
                                <td>
                                    {{--
                                        0 - Draft
                                        1 - Unassigned
                                        2 - Assigned
                                        3 - QC Pending
                                        4 - Sent to Customer
                                        5 - Need Revision
                                        6 - Completed
                                    --}}
                                    @if ($prj->status == 0)
                                        <div
                                            class="rounded-pill text-nowrap  border-0 ps-4 pe-4 pt-1 pb-1 d-flex align-items-center justify-content-center gap-2 status-bg-red">
                                            <i class="fa-solid fa-file-lines"></i>
                                            Draft
                                        </div>
                                    @elseif($prj->status == 1)
                                        <div
                                            class="rounded-pill text-nowrap  border-0 ps-4 pe-4 pt-1 pb-1 d-flex align-items-center justify-content-center gap-2 status-bg-purple">
                                            <i class="fa-solid fa-hourglass-end"></i>
                                            Pending projects
                                        </div>
                                    @elseif($prj->status == 5)
                                        <div
                                            class="rounded-pill text-nowrap  border-0 ps-4 pe-4 pt-1 pb-1 d-flex align-items-center justify-content-center gap-2 status-bg-blue">
                                            <i class="fa-solid fa-eye"></i>
                                            Review needed
                                        </div>
                                    @elseif($prj->status == 6)
                                        <div
                                            class="rounded-pill text-nowrap  border-0 ps-4 pe-4 pt-1 pb-1 d-flex align-items-center justify-content-center gap-2 status-bg-green">
                                            <i class="fa-solid fa-trophy"></i>
                                            Completed projects
                                        </div>
                                    @endif
                                </td>
                                <td class="gray-dark-text" style="min-width: 150px;">
                                    {{ humanDate($prj->ts) }}

                                </td>
                                <td>
                                    <a href="{{ route('graphic.chat') }}">
                                        <button
                                            class="rounded-pill text-nowrap  text-white ps-4 pe-4 pt-1 pb-1 d-flex align-items-center gap-3"
                                            style="margin: 0 auto;border:2px solid #194DAB;background-color: #194DAB;">
                                            <i class="fa-brands fa-rocketchat text-white"></i>
                                            Insights
                                        </button>
                                    </a>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
                @if ($draftprojects->count() == 0)
                    <div class="p-0">
                        <div class=" text-center">
                            <div class=" text-center">
                                <img src="{{ asset('media/images/emptyRecord.png') }}" alt="emptyRecord"
                                    class="img-fluid">
                            </div>
                            <h3 style="color: #194DAB">
                                No Project Found In Draft.
                            </h3>
                        </div>
                    </div>
                @endif
            </div>
        </div>
        <div class="GD-section-tab-content p-lg-2 p-md-2 p-4" id="tab3">
            <div class="table-responsive" style="max-width: 100%; overflow-x: auto;">
                <table class="table table-striped text-center bg-white">
                    <thead>
                        <tr>
                            <th scope="col">S.No.</th>
                            <th scope="col" class="text-start">List of tasks</th>
                            <th scope="col">Status</th>
                            <th scope="col">Created on</th>
                            <th scope="col">Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        @php
                            $i = $pending->perPage() * ($pending->currentPage() - 1);
                        @endphp
                        @foreach ($pending as $prj)
                            <tr>
                                <th scope="row">{{ ++$i }}</th>
                                <td style="min-width: 170px;">
                                    <div class="d-flex flex-column align-items-baseline gray-dark-text">
                                        {{ $prj->title }}
                                        <p class="m-0" style="font-size: small;color: #9C9C9C;">
                                            {{ str()->limit($prj->description, 30) }}
                                        </p>
                                    </div>
                                </td>
                                <td>
                                    {{--
                                        0 - Draft
                                        1 - Unassigned
                                        2 - Assigned
                                        3 - QC Pending
                                        4 - Sent to Customer
                                        5 - Need Revision
                                        6 - Completed
                                    --}}
                                    @if ($prj->status == 0)
                                        <div
                                            class="rounded-pill text-nowrap  border-0 ps-4 pe-4 pt-1 pb-1 d-flex align-items-center justify-content-center gap-2 status-bg-red">
                                            <i class="fa-solid fa-file-lines"></i>
                                            Draft
                                        </div>
                                    @elseif($prj->status == 1)
                                        <div
                                            class="rounded-pill text-nowrap  border-0 ps-4 pe-4 pt-1 pb-1 d-flex align-items-center justify-content-center gap-2 status-bg-purple">
                                            <i class="fa-solid fa-hourglass-end"></i>
                                            Pending projects
                                        </div>
                                    @elseif($prj->status == 5)
                                        <div
                                            class="rounded-pill text-nowrap  border-0 ps-4 pe-4 pt-1 pb-1 d-flex align-items-center justify-content-center gap-2 status-bg-blue">
                                            <i class="fa-solid fa-eye"></i>
                                            Review needed
                                        </div>
                                    @elseif($prj->status == 6)
                                        <div
                                            class="rounded-pill text-nowrap  border-0 ps-4 pe-4 pt-1 pb-1 d-flex align-items-center justify-content-center gap-2 status-bg-green">
                                            <i class="fa-solid fa-trophy"></i>
                                            Completed projects
                                        </div>
                                    @endif
                                </td>
                                <td class="gray-dark-text" style="min-width: 150px;">
                                    {{ humanDate($prj->ts) }}

                                </td>
                                <td>
                                    <a href="{{ route('graphic.chat') }}">
                                        <button
                                            class="rounded-pill text-nowrap  text-white ps-4 pe-4 pt-1 pb-1 d-flex align-items-center gap-3"
                                            style="margin: 0 auto;border:2px solid #194DAB;background-color: #194DAB;">
                                            <i class="fa-brands fa-rocketchat text-white"></i>
                                            Insights
                                        </button>
                                    </a>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
                @if ($pending->total() == 0)
                    <div class="p-0">
                        <div class=" text-center">
                            <div class=" text-center">
                                <img src="{{ asset('media/images/emptyRecord.png') }}" alt="emptyRecord"
                                    class="img-fluid">
                            </div>
                            <h3 style="color: #194DAB">
                                No Pending Project Found.
                            </h3>
                        </div>
                    </div>
                @endif
            </div>
        </div>
        <div class="GD-section-tab-content p-lg-2 p-md-2 p-4" id="tab4">
            <div class="table-responsive" style="max-width: 100%; overflow-x: auto;">
                <table class="table table-striped text-center bg-white">
                    <thead>
                        <tr>
                            <th scope="col">S.No.</th>
                            <th scope="col" class="text-start">List of tasks</th>
                            <th scope="col">Status</th>
                            <th scope="col">Created on</th>
                            <th scope="col">Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        @php
                            $i = $revision->perPage() * ($revision->currentPage() - 1);
                        @endphp
                        @foreach ($revision as $prj)
                            <tr>
                                <th scope="row">{{ ++$i }}</th>
                                <td style="min-width: 170px;">
                                    <div class="d-flex flex-column align-items-baseline gray-dark-text">
                                        {{ $prj->title }}
                                        <p class="m-0" style="font-size: small;color: #9C9C9C;">
                                            {{ str()->limit($prj->description, 30) }}
                                        </p>
                                    </div>
                                </td>
                                <td>
                                    {{--
                                        0 - Draft
                                        1 - Unassigned
                                        2 - Assigned
                                        3 - QC Pending
                                        4 - Sent to Customer
                                        5 - Need Revision
                                        6 - Completed
                                    --}}
                                    @if ($prj->status == 0)
                                        <div
                                            class="rounded-pill text-nowrap  border-0 ps-4 pe-4 pt-1 pb-1 d-flex align-items-center justify-content-center gap-2 status-bg-red">
                                            <i class="fa-solid fa-file-lines"></i>
                                            Draft
                                        </div>
                                    @elseif($prj->status == 1)
                                        <div
                                            class="rounded-pill text-nowrap  border-0 ps-4 pe-4 pt-1 pb-1 d-flex align-items-center justify-content-center gap-2 status-bg-purple">
                                            <i class="fa-solid fa-hourglass-end"></i>
                                            Pending projects
                                        </div>
                                    @elseif($prj->status == 5)
                                        <div
                                            class="rounded-pill text-nowrap  border-0 ps-4 pe-4 pt-1 pb-1 d-flex align-items-center justify-content-center gap-2 status-bg-blue">
                                            <i class="fa-solid fa-eye"></i>
                                            Review needed
                                        </div>
                                    @elseif($prj->status == 6)
                                        <div
                                            class="rounded-pill text-nowrap  border-0 ps-4 pe-4 pt-1 pb-1 d-flex align-items-center justify-content-center gap-2 status-bg-green">
                                            <i class="fa-solid fa-trophy"></i>
                                            Completed projects
                                        </div>
                                    @endif
                                </td>
                                <td class="gray-dark-text" style="min-width: 150px;">
                                    {{ humanDate($prj->ts) }}

                                </td>
                                <td>
                                    <a href="{{ route('graphic.chat') }}">
                                        <button
                                            class="rounded-pill text-nowrap  text-white ps-4 pe-4 pt-1 pb-1 d-flex align-items-center gap-3"
                                            style="margin: 0 auto;border:2px solid #194DAB;background-color: #194DAB;">
                                            <i class="fa-brands fa-rocketchat text-white"></i>
                                            Insights
                                        </button>
                                    </a>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
                @if ($revision->count() == 0)
                    <div class="p-0">
                        <div class=" text-center">
                            <div class=" text-center">
                                <img src="{{ asset('media/images/emptyRecord.png') }}" alt="emptyRecord"
                                    class="img-fluid">
                            </div>
                            <h3 style="color: #194DAB">
                                No Project For Review Found.
                            </h3>
                        </div>
                    </div>
                @endif
            </div>
        </div>
        <div class="GD-section-tab-content p-lg-2 p-md-2 p-4" id="tab5">
            <div class="table-responsive" style="max-width: 100%; overflow-x: auto;">
                <table class="table table-striped text-center bg-white">
                    <thead>
                        <tr>
                            <th scope="col">S.No.</th>
                            <th scope="col" class="text-start">List of tasks</th>
                            <th scope="col">Status</th>
                            <th scope="col">Created on</th>
                            <th scope="col">Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        @php
                            $i = $completeprojects->perPage() * ($completeprojects->currentPage() - 1);
                        @endphp
                        @foreach ($completeprojects as $prj)
                            <tr>
                                <th scope="row">{{ ++$i }}</th>
                                <td style="min-width: 170px;">
                                    <div class="d-flex flex-column align-items-baseline gray-dark-text">
                                        {{ $prj->title }}
                                        <p class="m-0" style="font-size: small;color: #9C9C9C;">
                                            {{ str()->limit($prj->description, 30) }}
                                        </p>
                                    </div>
                                </td>
                                <td>
                                    {{--
                                        0 - Draft
                                        1 - Unassigned
                                        2 - Assigned
                                        3 - QC Pending
                                        4 - Sent to Customer
                                        5 - Need Revision
                                        6 - Completed
                                    --}}
                                    @if ($prj->status == 0)
                                        <div
                                            class="rounded-pill text-nowrap  border-0 ps-4 pe-4 pt-1 pb-1 d-flex align-items-center justify-content-center gap-2 status-bg-red">
                                            <i class="fa-solid fa-file-lines"></i>
                                            Draft
                                        </div>
                                    @elseif($prj->status == 1)
                                        <div
                                            class="rounded-pill text-nowrap  border-0 ps-4 pe-4 pt-1 pb-1 d-flex align-items-center justify-content-center gap-2 status-bg-purple">
                                            <i class="fa-solid fa-hourglass-end"></i>
                                            Pending projects
                                        </div>
                                    @elseif($prj->status == 5)
                                        <div
                                            class="rounded-pill text-nowrap  border-0 ps-4 pe-4 pt-1 pb-1 d-flex align-items-center justify-content-center gap-2 status-bg-blue">
                                            <i class="fa-solid fa-eye"></i>
                                            Review needed
                                        </div>
                                    @elseif($prj->status == 6)
                                        <div
                                            class="rounded-pill text-nowrap  border-0 ps-4 pe-4 pt-1 pb-1 d-flex align-items-center justify-content-center gap-2 status-bg-green">
                                            <i class="fa-solid fa-trophy"></i>
                                            Completed projects
                                        </div>
                                    @endif
                                </td>
                                <td class="gray-dark-text" style="min-width: 150px;">
                                    {{ humanDate($prj->ts) }}

                                </td>
                                <td>
                                    <a href="{{ route('graphic.chat') }}">
                                        <button
                                            class="rounded-pill text-nowrap  text-white ps-4 pe-4 pt-1 pb-1 d-flex align-items-center gap-3"
                                            style="margin: 0 auto;border:2px solid #194DAB;background-color: #194DAB;">
                                            <i class="fa-brands fa-rocketchat text-white"></i>
                                            Insights
                                        </button>
                                    </a>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
                @if ($completeprojects->count() == 0)
                    <div class="p-0">
                        <div class=" text-center">
                            <div class=" text-center">
                                <img src="{{ asset('media/images/emptyRecord.png') }}" alt="emptyRecord"
                                    class="img-fluid">
                            </div>
                            <h3 style="color: #194DAB">
                                No Completed Project Found.
                            </h3>
                        </div>
                    </div>
                @endif
            </div>
        </div>
    </div>
@endsection
@section('PAGE-script')
    <script>
        document.addEventListener("DOMContentLoaded", function() {
            const menuLinks = document.querySelectorAll(".GD-menu a");
            const sectionContents = document.querySelectorAll(".GD-section-tab-content");

            // Hide all tab contents except the first one
            sectionContents.forEach(function(tabContent) {
                tabContent.style.display = "none";
            });
            sectionContents[0].style.display = "block";

            // Add click event handler to the attribute tags
            menuLinks.forEach(function(menuLink) {
                menuLink.addEventListener("click", function(e) {
                    e.preventDefault();

                    // Add active class to the clicked link
                    menuLink.classList.add('active');

                    // Remove active class from all links
                    menuLinks.forEach(function(link) {
                        link.classList.remove("active");
                    });

                    // Add active class to the clicked link
                    menuLink.classList.add("active");

                    // Hide all tab contents
                    sectionContents.forEach(function(tabContent) {
                        tabContent.style.display = "none";
                    });

                    // Show the corresponding tab content based on the clicked link
                    const targetContent = menuLink.getAttribute("data-w-tab");
                    document.getElementById(targetContent).style.display = "block";
                });
            });

            // Add active class to the first tab link
            menuLinks[0].classList.add("active");
        });
    </script>
    <script>
        function toggleMenu(event) {
            // Get the clicked element (ellipsis icon)
            const icon = event.target;

            // Get the menu div
            const menu = icon.nextElementSibling;

            // Toggle the menu's display
            const isMenuVisible = window.getComputedStyle(menu).display !== 'none';
            menu.style.display = isMenuVisible ? 'none' : 'block';
        }

        function downloadItem() {
            // Add your download logic here
            console.log('Downloading item');
        }

        function deleteItem() {
            // Add your delete logic here
            console.log('Deleting item');
        }
    </script>
@endsection
