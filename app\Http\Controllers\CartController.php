<?php

namespace App\Http\Controllers;

use App\Models\Cart;
use App\Models\CartView;
// use App\Models\Coupon;
use App\Models\ProductVariant;
use App\Models\User;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use PDO;
use PDOException;

class CartController extends Controller
{
    public function index()
    {
        $lead = auth()->user();
        if ($lead->hidePrice == 1) {
            return redirect('/');
        }
        $data['cartViewData'] = CartView::where(['lead_id' => $lead->id, 'parentCart_id' => null])
            ->with(['addons', 'product', 'variant' => function ($query) {
                $query->has('ranges');
            }, 'variant.ranges'])
            ->get();

        $data['calculation'] = getCartTotals($data['cartViewData']);
        return view('userpages.cart.index', $data);
    }

    public function update(Request $request)
    {
        try {
            if (!Auth::check()) {
                return response()->json(['status' => 'Unauthorized.']);
            }
            DB::enableQueryLog();
            $prod = ProductVariant::where(['id' => $request->pid, 'isActive' => 1])
                ->with('minRange', 'maxRange')
                ->firstOrFail();

            $cart = Cart::where(['id' => $request->cid, 'lead_id' => Auth::user()->id])->firstOrFail();

            $quantityChange = $cart->quantity;
            if ($request->type == '1') {
                $quantityChange = $cart->quantity + $prod->qtyMultiple;
            } else {
                $quantityChange = $cart->quantity - $prod->qtyMultiple;
            }

            if ($quantityChange < $prod->minRange->minQty) {
                $cart->CartProductAddons()->delete();
                $cart->delete();
                return response()->json(['status' => 'Cart Updated.']);
            }
            if ($quantityChange > $prod->maxRange->maxQty) {
                $quantityChange = $prod->maxRange->maxQty;
            }

            $cart->update(['quantity' => $quantityChange]);

            return response()->json(['status' => 'Quantity Updated']);
        } catch (Exception $e) {

            return response()->json(['status' => 'Fail to update.']);
        }
    }
    public function remove($cart)
    {
        try {
            $cart =  Cart::where('lead_id', Auth::id())
                ->where('id', $cart)
                ->first();
            if ($cart) {
                $cart->CartProductAddons()->delete();
                $cart->delete();
            }
        } catch (Exception $e) {
            return redirect()->back()->with('error', 'Due to some system error OR Invalid Request  we can\'t proceed with request.');
        }
        return redirect()->back()->with('success', 'Item removed. ');
    }
    public function addToCart($product_id, $quantity = null)
    {
        try {
            $product = ProductVariant::where(['id' => $product_id, 'isActive' => 1])
                ->with('minRange')
                ->firstOrFail();

            $parent = Cart::firstOrNew([
                'lead_id' => Auth::user()->id,
                'product_variant_id' => $product_id,
                'parentCart_id' => null
            ]);

            $defaultQty = $product->minRange->minQty;
            $parent->quantity = $parent->exists
                ? ($parent->quantity + ($quantity ?? $defaultQty))
                : ($quantity ?? $defaultQty);

            $parent->save();

            return redirect()->route('u.cart')
                ->with('added_cart', 'Cart Updated.');
        } catch (Exception $e) {

            return redirect()->back()->with('error', 'Due to some system error OR Invalid Request  we can\'t proceed with request.');
        }
    }
    public function addToCartForm(Request $request)
    {
        try {
            $product = ProductVariant::where(['id' => $request->productVariant, 'isActive' => 1])->with('minRange')->first();

            if ($product) {
                $parent = Cart::where(['lead_id' => Auth::user()->id, 'product_variant_id' => $product->id, 'parentCart_id' => null])->first();
                if ($parent) {
                    $parent->quantity = $parent->quantity ? $parent->quantity + $product->minRange->minQty : $product->minRange->minQty;
                    $parent->save();
                } else {
                    $parent = new Cart();
                    $parent->lead_id = Auth::user()->id;
                    $parent->product_variant_id = $product->id;
                    $parent->parentCart_id = null;
                    $parent->quantity = $request->quantity ?? $product->minRange->minQty;
                    $parent->save();
                }

                $message = $parent->wasRecentlyCreated ? 'Item added to cart.' : 'Item Updated.';
                return redirect()->route('u.cart')->with('added_cart', $message);
            } else {
                return redirect()->back()->with('error', 'Currently this Product or Service not Available.');
            }
        } catch (Exception $e) {
            return redirect()->back()->with('error', 'Due to some system error OR Invalid Request  we can\'t proceed with request.');
        }
    }
    public function cartAddMultiple(Request $request)
    {
        $validator = Validator::make(
            $request->all(),
            ['products' => 'required|array']
        );
        if ($validator->fails()) {

            return response()->json($validator->errors());
        }
        try {
            $leadId = Auth::user()->id;

            $allItems = collect($request->products)->map(function ($item, $key) use ($leadId) {
                $item = [
                    'lead_id' => $leadId,
                    'product_variant_id' => $item['variant'],
                    "quantity" => $item['qty'],
                    "isParent" => $item['isParent'],
                    'product_id' => $key
                ];
                return $item;
            });

            $parentProduct = $allItems->where('isParent', true)->first();
            $childProducts = $allItems->where('isParent', false);
            $parent = Cart::where([
                'lead_id' => Auth::user()->id,
                'product_variant_id' => $parentProduct['product_variant_id'],
                'parentCart_id' => null
            ])->first();
            if ($parent) {
                $parent->quantity = $parent->quantity ? $parent->quantity + $parentProduct['quantity'] : $parentProduct['quantity'];
                $parent->save();
            } else {
                $parent = new Cart();
                $parent->lead_id = Auth::user()->id;
                $parent->product_variant_id = $parentProduct['product_variant_id'];
                $parent->parentCart_id = null;
                $parent->quantity = $parentProduct['quantity'];
                $parent->save();
            }
            $parent_id = $parent->id;

            $values = [];
            $placeholders = [];

            foreach ($childProducts as $product) {
                $values[] = $leadId;
                $values[] = $product['product_variant_id'];
                $values[] = $product['quantity'];
                $values[] = $parent_id;

                $placeholders[] = '(?, ?, ?, ?)';
            }
            if (!empty($placeholders)) {
                $sql = "
                INSERT INTO " . (new Cart())->getTable() . " (lead_id, product_variant_id, quantity, parentCart_id)
                VALUES " . implode(', ', $placeholders) . "
                ON DUPLICATE KEY UPDATE quantity = quantity + VALUES(quantity)";

                DB::statement($sql, $values);
            }

            // DB::commit();
            return response()->json(['status' => true, 'message' => 'Cart Updated.']);
        } catch (PDOException $e) {

            $errorInfo = $e->errorInfo;
            // DB::rollBack();
            if ($errorInfo[0] == 45000) {
                return response()->json(['status' => false, 'message' => $errorInfo[2], 'dev-error' => $e->getMessage()]);
            }
            return response()->json(['status' => false, 'message' => 'Fail to update Cart.', 'dev-error' => $e->getMessage()]);
        }
    }

    // public function addCouponCart(Request $request)
    // {
    //     try {
    //         $coupon = Coupon::where('code', $request->code)->first();

    //         if ($coupon) {


    //             return redirect()->route('u.cart')->with('success', 'Coupon applied.');
    //         } else {
    //             return redirect()->back()->with('error', 'Invalid Code.');
    //         }
    //     } catch (Exception $e) {
    //         return redirect()->back()->with('error', ErrMsg());
    //     }
    // }
    public function removeCoupon(Request $request)
    {
        try {
            $lead = User::find(auth()->id());
            $lead->couponCode = null;
            $lead->update();
            return redirect()->route('u.cart')->with('success', 'Coupon removed.');
        } catch (Exception $e) {
            return redirect()->back()->with('error', ErrMsg());
        }
    }

    public function updatePeriod(Request $request)
    {
        DB::beginTransaction();
        try {
            $product_id = $request->period;
            $product = ProductVariant::where(['id' => $product_id, 'isActive' => 1])
                ->with('maxRange')
                ->firstOrFail();



            $cart = Cart::find($request->cart_id);
            $oldCart = Cart::where(['lead_id' => Auth::user()->id, 'product_variant_id' => $request->period,])->first();

            if ($oldCart) {
                $qty = $cart->quantity + $oldCart->quantity;
                if ($qty < $product->maxRange->minQty) {
                    $qty = $product->maxRange->minQty;
                }
                if ($qty > $product->maxRange->maxQty) {
                    $qty = $product->maxRange->maxQty;
                }
                $oldCart->quantity = $qty;
                if ($cart->CartProductAddons) {
                    $cart->CartProductAddons()->update(['parentCart_id' => $oldCart->id]);
                }

                $oldCart->update();
                $cart->delete();
            } else {
                $cart->product_variant_id = $product_id;
                $cart->update();
            }
            DB::commit();
            return redirect()->route('u.cart');
        } catch (PDOException $e) {
            DB::rollBack();
            $errorInfo = $e->errorInfo;
            DB::rollBack();
            if ($errorInfo[0] == 45000) {
                return redirect()->back()->with('error', $errorInfo[2]);
            }

            return redirect()->back()->with('error', ErrMsg());
        }
    }



    public function checkout()
    {
        try {
            $select = DB::select('select createInvoiceFromCart(?) as invoice', [Auth::user()->id]);
            $invoice = json_decode(json_encode(json_decode(json_encode($select))[0], true), true)['invoice'];

            return redirect()->route('pi.address', ['invoice' => $invoice]);
        } catch (Exception $e) {
            Log::error('Cart checkout error: ' . $e->getMessage());
            return redirect()->back()->with('error', ErrMsg());
        }
    }
}
