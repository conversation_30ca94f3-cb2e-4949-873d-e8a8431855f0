@php
    $faqs = [
        [
            'question' => 'How quickly can initiate calls for my customers?',
            'answer' =>
                'Voice broadcasting works by uploading a pre-recorded message, selecting a list of recipients, and then broadcasting the message to the specified audience via phone calls. The tool automates the process to reach a large number of contacts quickly.',
        ],
        [
            'question' => 'How quickly can I send a message to my customers ?',
            'answer' =>
                'Hundreds of thousands of messages may be sent out every hour using our sophisticated technologies. We have the capability to deliver your message on time, every time, no matter how rigorous your requirements are.',
        ],
        [
            'question' => 'What format of audio file is allowed for broadcasting ?',
            'answer' => 'For Voice Broadcast you can use only .WAV Format audio files.',
        ],
        [
            'question' => 'How can I schedule a Voice Broadcast campaign to start in future ?',
            'answer' =>
                'When establishing a campaign, go to the Campaign Schedule option and fill in the start field with the time and date you want the campaign to begin. Once the campaign has been established, start it from the campaign`s Action menu, and it will begin dialling on the specified day and time.',
        ],
        [
            'question' => 'What is a Voice Broadcast?',
            'answer' =>
                'A voice broadcast is just a pre-recorded message that is sent to customers via telephone. It`s simple and incredibly effective way to communicate with large numbers of people very quickly.',
        ],
        [
            'question' => 'Can I track the performance of my voice broadcasting campaigns?',
            'answer' =>
                'Yes, Ringcaster provide analytics and reporting features. You can track metrics such as call delivery, response rates, and overall campaign success.',
        ],
        [
            'question' => 'How secure is the data used in voice broadcasting?',
            'answer' =>
                'We prioritize data security by adhering to industry standards and implementing robust encryption measures to safeguard sensitive information.',
        ],
    ];
@endphp


<div class="container-fluid p-lg-5 p-md-5 p-3">
    <div class="container-lg p-lg-5 rounded-5">
        <h3 class="text-center fw-bold text-blue">Frequently Asked Questions</h3>
        <p class="text-center">Most frequent questions and answers</p>
        <div class="container-lg">
            <x-faq-accordion :faqs="$faqs" collapsed-color="#ffffff" active-color="#f68c3e" />
        </div>
    </div>
</div>
