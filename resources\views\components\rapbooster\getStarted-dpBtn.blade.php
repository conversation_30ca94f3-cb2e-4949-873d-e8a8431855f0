@php
    $class =
        'text-dark DP-btn align-items-center border-0 btn d-flex gap-1 gap-lg-2 gap-md-2 btn-font rounded-pill text-decoration-none';
    $icon = '<i class="fa-brands fa-telegram fa-2x" style="color: #FFF;"></i>';
@endphp

    @isset($category['id'])
        <a aria-label="link" class="{{ $class }}"
            href="{{ route('categoryPricing', ['category' => $category['id'], 'categoryName' => strtolower($category['name'])]) }}">
            Get started
            {!! $icon !!}
        </a>
    @elseif(isset($modalTarget))
        <button aria-label="link" data-bs-toggle="modal" data-bs-target="#{{ $modalTarget }}" class="{{ $class }}">
            Get started
            {!! $icon !!}
        </button>
    @else
        <a aria-label="link" class="{{ $class }}" href="{{ route('quotation.create') }}">
            Get Quote
            {!! $icon !!}
        </a>
    @endisset

