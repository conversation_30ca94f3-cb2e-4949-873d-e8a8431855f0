<style>
    /* Accordion button (collapsed/inactive state) */
    .accordion-button {
        padding: 30px !important;
        background-color: var(--accordion-bg, #F6F6F6);
        color: #3c3c3c !important;
        border-radius: 0.76rem !important;
        font-size: 1.25rem !important;
        font-weight: 400 !important;
    }

    /* Accordion button (expanded/active state) */
    .accordion-button:not(.collapsed) {
        background-color: var(--accordion-active-bg, #2C8CF4);
        color: white !important;
        border-radius: 0.75rem !important;
    }

    .accordion-header {
        border-radius: 0.76rem !important;
    }

    /* Remove default arrow icon */
    .accordion-button::after {
        display: none !important;
    }

    /* Remove default focus shadow */
    .accordion-button:focus {
        box-shadow: none;
    }

    /* Accordion item container */
    .accordion-item {
        border: none;
        background-color: transparent;
    }

    /* Space between accordion items */
    .accordion-item+.accordion-item {
        margin-top: 1rem;
    }

    /* Accordion collapse (content area wrapper) */
    .accordion-collapse {
        border: 1px solid #dee2e6;
        border-top: none !important;
        border-radius: var(--bs-border-radius-xl) !important;
        border-top-left-radius: 0 !important;
        border-top-right-radius: 0 !important;
        /* prevent double border with button */

        overflow: hidden;
        background-color: #fff;
    }

    /* Accordion content body */
    .accordion-body {
        background-color: #fff;
        padding: 1.5rem;
        /* border-radius: 0 0 0.75rem 0.75rem; */
    }
</style>
<?php /**PATH C:\Users\<USER>\Desktop\live\websites_laravel\resources\views/components/global/accordionStyles.blade.php ENDPATH**/ ?>