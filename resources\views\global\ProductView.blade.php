@extends('global.main')

@section('globle-PAGE-CSS')
    <style>
        .tooltip.custom-tooltip-danger .tooltip-inner {
            background-color: #f8d7da;
            color: #842029;
            border: 1px solid #f5c2c7;
        }

        .tooltip.custom-tooltip-danger.bs-tooltip-top .tooltip-arrow::before {
            border-top-color: #f8d7da;
        }

        .tooltip.custom-tooltip-danger.bs-tooltip-bottom .tooltip-arrow::before {
            border-bottom-color: #f8d7da;
        }

        .tooltip.custom-tooltip-danger.bs-tooltip-start .tooltip-arrow::before {
            border-left-color: #f8d7da;
        }

        .tooltip.custom-tooltip-danger.bs-tooltip-end .tooltip-arrow::before {
            border-right-color: #f8d7da;
        }
    </style>
@endsection
@section('GlobalPages')
    <div class="container my-2">
        <div class="d-flex text-secondary gap-2 p-2">
            <div class="">
                <a class="text-decoration-none text-secondary-emphasis" href="/">
                    <i class=" bi bi-house-fill"></i>
                </a>
            </div>
            <div class="">/</div>
            <div class="">
                <a class="text-decoration-none text-secondary-emphasis"
                    href="{{ route('categoryPricing', ['category' => $product->category->id, 'categoryName' => strtolower(str_replace(' ', '-', $product->category->name))]) }}">
                    {{ $product->category->name }}
                </a>
            </div>
            <div class="">/</div>
            <div class="">{{ $product->name }}</div>
        </div>
    </div>
    <div class="container my-2">
        <div class="row product-view-box">
            <div class="col-lg-5 col-md-12  text-center">
                @if ($product->images->count())
                    @if ($product->images->count() == 1)
                        <img src="{{ s3_fileShow($product->images->first()->image, 'product_gallery') }}"
                            srcset="{{ s3_fileShow($product->images->first()->image, 'product_gallery_small') }} 400w,
                                                        {{ s3_fileShow($product->images->first()->image, 'product_gallery') }} 800w"
                            sizes="(max-width: 600px) 400px, 800px" loading="lazy" class="img-fluid" />
                    @else
                        <!-- Main slider -->
                        <div id="main-slider" class="splide">
                            <div class="splide__track">
                                <ul class="splide__list">
                                    @foreach ($product->images as $img)
                                        <li class="splide__slide"><img class="img-fluid"
                                                src="{{ s3_fileShow($img->image, 'product_gallery') }}" alt="Image 1" />
                                        </li>
                                    @endforeach
                                </ul>
                            </div>
                        </div>

                        <!-- Thumbnail slider -->
                        <div id="thumbnail-slider" class="splide">
                            <div class="splide__track">
                                <ul class="splide__list">
                                    @foreach ($product->images as $img)
                                        <li class="splide__slide"><img class="img-fluid"
                                                src="{{ s3_fileShow($img->image, 'product_gallery') }}" alt="Thumb 1" />
                                        </li>
                                    @endforeach

                                </ul>
                            </div>
                        </div>
                    @endif
                @else
                    <img src="https://dummyimage.com/500/aaaaaa/fff.jpg&text={{ $product->name }}" alt="..."
                        class="img-fluid">
                @endif
            </div>
            <div class="col-lg-7 col-md-12 d-flex flex-column ">

                <h2 class="m-0 product-name mb-2">
                    {{ $product->name }}
                </h2>
                @if (count($productVariants))
                    <div class="d-flex gap-3 w-100 flex-wrap text-nowrap mb-2">
                        @foreach ($productVariants as $item)
                            <a href="{{ route('product.view', ['categoryId' => $product->category->id, 'category' => strtolower(str_replace(' ', '-', $product->category->name)), 'productVariant' => $item->id]) }}"
                                class="text-decoration-none text-dark">
                                <div
                                    class=" variant  p-1 rounded px-4 fw-semibold text-secondary @if ($productVariant->id == $item->id || $productVariant->name == $item->name) variant-active @else border-secondary @endif">
                                    {{ $item->name }}
                                </div>
                            </a>
                        @endforeach
                    </div>
                @endif

                @if ($hidePrice && $mainProduct['first'])
                    <div class="d-flex gap-3 align-items-baseline fs-3 lh-1">
                        @php
                            $discount = round(
                                (($productVariant->mrp - $mainProduct['first']->sellingPrice) / $productVariant->mrp) *
                                    100,
                            );
                        @endphp
                        <div class=" text-secondary fw-semibold ">
                            <del>
                                ₹<span id="mainMrp">{{ $productVariant->mrp }}</span>
                            </del>
                        </div>
                        <div class="fw-bold">
                            ₹<span class="selling">{{ $mainProduct['first']->sellingPrice }}</span>
                        </div>
                        <div class="text-success fs-5 align-self-end" id="mainDiscount">
                            @if ($discount > 0)
                                {{ $discount }}% off
                            @endif
                        </div>
                    </div>
                @endif
                @if (count($product->features))
                    <div class="d-flex gap-3 mb-2">
                        <div class="w-25">
                            <h4 class="text-secondary " style="font-size:18px">
                                Features
                            </h4>
                        </div>
                        <div class="d-flex  w-75 flex-wrap" style="font-size: 14px;">
                            @foreach ($product->features as $item)
                                <div class="d-flex gap-2 align-items-baseline w-50">
                                    <i class="{{ $item->iconClass ? $item->iconClass : 'fa-solid fa-check' }}"
                                        style="color: {{ $item->iconColorHex ?? '#2d45c1' }};"></i>
                                    <div class="text-start">
                                        {!! $item->feature !!}
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                @endif
                @if ($hidePrice)
                    <div class="d-flex gap-3 mb-2">
                        <div class="w-25">
                            <h4 class="text-secondary " style="font-size:18px">
                                Quantity
                            </h4>
                        </div>
                        <div class="row  flex-wrap align-items-center w-75">
                            <div class="col-6 col-md-6 col-lg-4">

                                <div class="quantity-container ">
                                    <div class="d-flex align-items-center gap-1 ">
                                        <button class="rounded-circle btn btn-sm border bg-white" type="button"
                                            value="-"
                                            onclick="updateMainQuantity('-','#qtyInput',{{ $productVariant->qtyMultiple }},this)">
                                            <i class="fas fa-minus"></i>
                                        </button>
                                        <input type="number" aria-label="main-quantity" name="quantity" disabled
                                            value="{{ $mainProduct['first']->minQty }}" id="qtyInput"
                                            class="qty form-control form-control-sm  border-secondary-subtle  text-center bg-white"
                                            style="width: 60px;" step="{{ $productVariant->qtyMultiple }}"
                                            min="{{ $mainProduct['first']->minQty }}"
                                            max="{{ $mainProduct['last']->maxQty }}" />
                                        <button class="rounded-circle btn btn-sm border bg-white" type="button"
                                            value="+"
                                            onclick="updateMainQuantity('+','#qtyInput',{{ $productVariant->qtyMultiple }},this)">
                                            <i class="fas fa-plus"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                @endif
                @if (count($bulkDiscounts) > 1 && $hidePrice)
                    <div class="d-flex gap-3">
                        <div class="w-25">
                            <h4 class="text-secondary " style="font-size:18px">
                                Bulk Discount
                            </h4>
                        </div>
                        <div class="w-75">
                            <table class="table table-bordered">
                                <thead>
                                    <tr>
                                        <th scope="col">Quantity Range</th>
                                        <th scope="col">Price</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach ($bulkDiscounts as $range)
                                        <tr>
                                            <td>{{ $range->minQty }}-{{ $range->maxQty }}</td>
                                            <td>₹ {{ $range->sellingPrice }}</td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                @endif
                <div class="">

                    @if (count($addons))
                        <div class="card p-0 rounded-2">
                            <h5 class="p-2 fs-6">Upgrade Your Experience</h5>
                            <div style="max-height:30vh;overflow-y:auto">
                                @foreach ($addons as $pItem)
                                    @if (count($pItem->variants))
                                        @php

                                            $defaultRanges = null;
                                            $defaultFirstRange = null;
                                            $defaultLastRange = null;
                                            $defaultVariant = null;
                                            $defaultVariant = $pItem->variants->where('isDefault', 1)->first();
                                            if ($defaultVariant == null) {
                                                $defaultVariant = $pItem->variants->first();
                                            }

                                            $defaultRanges = $defaultVariant->ranges->sortBy('minQty');
                                            $defaultFirstRange = $defaultRanges->first();
                                            $defaultLastRange = $defaultRanges->last();
                                        @endphp
                                        {{-- {{ $defaultVariant->name }} --}}
                                        <div
                                            class="border-bottom-0 border-end-0 border-start-0 card p-2 rounded-0 row-gap-2">
                                            <div class="row column-gap-3 g-0">
                                                @if ($hidePrice)
                                                    <div class="col-auto  ">
                                                        <label for="product-{{ $pItem->id }}"
                                                            class="form-check-label h-100 p-1">
                                                            <input type="checkbox" name=""
                                                                class="form-check-input variant-checkbox border-secondary"
                                                                id="product-{{ $pItem->id }}">
                                                        </label>
                                                    </div>
                                                @endif
                                                {{-- image div --}}
                                                <div class="col-auto">
                                                    <a class="text-decoration-none" target="_blank"
                                                        href="{{ route('product.view', ['categoryId' => $pItem->category->id, 'category' => strtolower(str_replace(' ', '-', $pItem->category->name)), 'productVariant' => $defaultVariant->id]) }}">
                                                        @if (isset($pItem->primaryImage->image))
                                                            <img src="{{ s3_fileShow($pItem->primaryImage->image, 'product_gallery') }}"
                                                                class="img-fluid rounded-1" style="width: 70px;"
                                                                alt="{{ $pItem->primaryImage->image }}">
                                                        @else
                                                            <img src="https://dummyimage.com/75/969696/ffffff.jpg&text={{ urlencode($pItem->name) }}"
                                                                alt="{{ $pItem->name }}" class="img-fluid rounded-1">
                                                        @endif
                                                    </a>
                                                </div>
                                                {{-- name variant qty div --}}
                                                <div class="col">
                                                    <div class=" pb-0">
                                                        <a class="text-decoration-none" target="_blank"
                                                            href="{{ route('product.view', ['categoryId' => $pItem->category->id, 'category' => strtolower(str_replace(' ', '-', $pItem->category->name)), 'productVariant' => $defaultVariant->id]) }}">
                                                            <div
                                                                class="flex-md-row  gap-md-2 align-items-baseline align-items-end align-items-lg-baseline align-items-xl-baseline d-flex flex-column flex-lg-column flex-xl-row gap-lg-0 gap-xl-2">
                                                                <div class="fw-semibold text-dark  ">
                                                                    {{ $pItem->name }}
                                                                </div>
                                                                <div class="d-flex gap-2 align-items-end">
                                                                    <div class="text-muted fs-6">
                                                                        ({{ $pItem->category->name }})
                                                                    </div>
                                                                    <div class="" data-bs-toggle="tooltip"
                                                                        data-bs-title="Know More">

                                                                        <span style="font-size: 13px">
                                                                            <i
                                                                                class="bi bi-exclamation-circle text-muted"></i>
                                                                        </span>

                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </a>
                                                        <div
                                                            class="align-items-baseline align-items-end align-items-lg-baseline align-items-xl-baseline d-flex flex-column flex-lg-column flex-xl-row gap-lg-0 gap-xl-2 flex-md-row justify-content-md-between">
                                                            <div class="d-flex gap-3 mt-1 d-none qty-variants-div">
                                                                <div class="">
                                                                    <select id="{{ $pItem->id }}-variantSelect-id"
                                                                        class="form-select form-select-sm addon-v-select variant-select"
                                                                        aria-label="Small select">

                                                                        @foreach ($pItem->variants as $variant)
                                                                            @php
                                                                                $ranges = $variant->ranges->sortBy(
                                                                                    'minQty',
                                                                                );
                                                                                $firstRange = $ranges->first();
                                                                                $lastRange = $ranges->last();
                                                                            @endphp
                                                                            @if (count($ranges))
                                                                                <option value="{{ $variant->id }}"
                                                                                    last="{{ $lastRange }}"
                                                                                    first="{{ $firstRange }}"
                                                                                    data-variant="{{ $variant->id }}"
                                                                                    data-selling="{{ $firstRange->sellingPrice }}"
                                                                                    data-mrp="{{ $variant->mrp }}"
                                                                                    data-pid="{{ $pItem->id }}"
                                                                                    data-min="{{ $firstRange->minQty }}"
                                                                                    data-max="{{ $lastRange->maxQty }}"
                                                                                    data-changeby="{{ $variant->qtyMultiple }}"
                                                                                    @selected($defaultVariant ? $defaultVariant->id == $variant->id : false)>
                                                                                    {{ $variant->name }}
                                                                                </option>
                                                                            @endif
                                                                        @endforeach
                                                                    </select>
                                                                </div>
                                                                @if ($hidePrice && $defaultLastRange && $defaultFirstRange && $defaultVariant)
                                                                    <div
                                                                        class="d-flex align-items-center gap-1 input-btn-group{{ $defaultVariant == null ? ' d-none ' : '' }}">
                                                                        <button
                                                                            class="rounded-circle btn btn-sm border bg-white"
                                                                            onclick="updateQuantity(this,'-')">
                                                                            <i class="bi bi-dash"></i>
                                                                        </button>
                                                                        <input type="number"
                                                                            id="{{ $defaultVariant->id }}"
                                                                            class="form-control form-control-sm  border-secondary-subtle addon-quantity text-center"
                                                                            style="width: 60px;" placeholder="1"
                                                                            value="{{ $defaultFirstRange->minQty ?? 1 }}"
                                                                            readonly min="1"
                                                                            defaultFirstRange={{ $defaultFirstRange }}
                                                                            data-changeby="{{ $defaultVariant->qtyMultiple ?? 1 }}"
                                                                            data-pid="{{ $pItem->id }}"
                                                                            data-variant="{{ $defaultFirstRange->product_variant_id ?? '' }}"
                                                                            data-min="{{ $defaultFirstRange->minQty ?? 1 }}"
                                                                            data-max="{{ $defaultLastRange->maxQty ?? 1 }}"
                                                                            data-selling="{{ $defaultFirstRange->sellingPrice ?? 1 }}"
                                                                            data-mrp="{{ $defaultVariant->mrp ?? 1 }}"
                                                                            onchange="calculateAddOnTotal()">
                                                                        <button
                                                                            class="rounded-circle btn btn-sm border bg-white"
                                                                            onclick="updateQuantity(this,'+')">
                                                                            <i class="bi bi-plus-lg"></i>
                                                                        </button>
                                                                    </div>
                                                                @endif
                                                            </div>
                                                            <div class="">
                                                                @if ($hidePrice && $defaultFirstRange && $defaultVariant)
                                                                    <div
                                                                        class="d-flex align-items-center gap-2  fs-15p mt-2 amount-box flex-nowrap text-nowrap d-none">
                                                                        <div class="text-secondary addon-mrp-box">
                                                                            ₹<del
                                                                                class=" addon-mrp">{{ $defaultVariant->mrp }}</del>
                                                                        </div>
                                                                        @php
                                                                            $discount =
                                                                                (($defaultVariant->mrp -
                                                                                    $defaultFirstRange->sellingPrice) /
                                                                                    $defaultVariant->mrp) *
                                                                                100;
                                                                        @endphp
                                                                        <div class="text-success addon-discount">
                                                                            {{ round($discount) }}% Off
                                                                        </div>

                                                                        <div class="fw-semibold addon-selling-price">
                                                                            ₹{{ $defaultFirstRange->sellingPrice }}
                                                                        </div>

                                                                    </div>
                                                                @endif
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    @endif
                                @endforeach
                            </div>
                            @if ($hidePrice)
                                <div class="border-top pt-3  px-3 py-2 ">
                                    <div class="fs-6 invalid-selection text-danger text-center d-none">
                                        Select Valid Quantity or Variant
                                    </div>
                                    <div class="fs-6 error-box text-danger text-center d-none">

                                    </div>

                                    <div
                                        class="d-flex flex-wrap justify-content-around align-items-center flex-column flex-md-row">
                                        <div class="d-flex align-items-center gap-3 items-div d-none">
                                            <div class="text-center">
                                                <div class="text-muted small">1 Item</div>
                                                <div class="fw-semibold">Added</div>
                                            </div>
                                            <div class="fs-4 fw-bold addon-plus d-none">+</div>
                                            <div class="text-center addon-div d-none">
                                                <div class="text-muted small ">
                                                    <span class="addon-count">
                                                        0
                                                    </span>
                                                    Add-ons
                                                </div>
                                                <div class="fw-semibold">
                                                    ₹
                                                    <span class="addon-total">
                                                        0
                                                    </span>
                                                </div>
                                            </div>
                                            <div class="fs-4 fw-bold">=</div>
                                            <div class="text-center">
                                                <div class="text-muted small ">Total</div>
                                                <div class="fw-semibold">₹
                                                    <span class="addon-product-total">
                                                        {{ $productVariant->sellingPrice }}
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="empty-items-div">
                                            Enhance Your Experience With Our Addons
                                        </div>
                                        <button class="text-white btn btn-lg py-1  rounded-pill d-flex gap-2 px-4"
                                            style="background-color: #ff6b01;" id="getSelectedProducts">
                                            <div class="btn-span lh-sm">
                                                <svg xmlns="http://www.w3.org/2000/svg" height="24px"
                                                    viewBox="0 0 24 24" width="24px" fill="#fff">
                                                    <path d="M0 0h24v24H0V0z" fill="none" />
                                                    <path
                                                        d="M15.55 13c.75 0 1.41-.41 1.75-1.03l3.58-6.49c.37-.66-.11-1.48-.87-1.48H5.21l-.94-2H1v2h2l3.6 7.59-1.35 2.44C4.52 15.37 5.48 17 7 17h12v-2H7l1.1-2h7.45zM6.16 6h12.15l-2.76 5H8.53L6.16 6zM7 18c-1.1 0-1.99.9-1.99 2S5.9 22 7 22s2-.9 2-2-.9-2-2-2zm10 0c-1.1 0-1.99.9-1.99 2s.89 2 1.99 2 2-.9 2-2-.9-2-2-2z" />
                                                </svg>
                                            </div>
                                            <span class="add-to-cart-text fs-6 lh-lg">
                                                ADD TO CART
                                            </span>
                                        </button>

                                    </div>
                                </div>
                            @else
                                <div class="border-top pt-3  px-3 py-2 ">
                                    <div class="text-center">
                                        <a href="{{ route('quotation.create', ['product' => $product->id]) }}"
                                            class="text-white btn w-50 rounded-pill" style="background-color: #ff6b01;">
                                            Get Quotation
                                        </a>
                                    </div>
                                </div>
                            @endif
                        </div>
                    @else
                        @if ($hidePrice)
                            <div class="fs-6 error-box text-danger text-center d-none">

                            </div>

                            <div class="d-flex justify-content-center">
                                <button class="text-white btn btn-lg py-1  rounded-pill d-flex gap-2 px-4"
                                    style="background-color: #ff6b01;" id="getSelectedProducts">
                                    <div class="btn-span lh-sm">
                                        <svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 0 24 24"
                                            width="24px" fill="#fff">
                                            <path d="M0 0h24v24H0V0z" fill="none" />
                                            <path
                                                d="M15.55 13c.75 0 1.41-.41 1.75-1.03l3.58-6.49c.37-.66-.11-1.48-.87-1.48H5.21l-.94-2H1v2h2l3.6 7.59-1.35 2.44C4.52 15.37 5.48 17 7 17h12v-2H7l1.1-2h7.45zM6.16 6h12.15l-2.76 5H8.53L6.16 6zM7 18c-1.1 0-1.99.9-1.99 2S5.9 22 7 22s2-.9 2-2-.9-2-2-2zm10 0c-1.1 0-1.99.9-1.99 2s.89 2 1.99 2 2-.9 2-2-.9-2-2-2z" />
                                        </svg>
                                    </div>
                                    Add To Cart
                                </button>
                            </div>
                        @else
                            <div class="text-center">
                                <a href="{{ route('quotation.create', ['product' => $product->id]) }}"
                                    class="text-white btn w-50 rounded-pill" style="background-color: #ff6b01;">
                                    Get Quotation
                                </a>
                            </div>
                        @endif
                    @endif
                </div>
            </div>
            <div class="my-5">
                {!! $product->details !!}
            </div>
        </div>
    @endsection

    @section('PAGE-script')
        @if ($hidePrice)
            <script>
                const variantDiscounts = @json($addonsRanges);
                const bulkDiscounts = @json($bulkDiscounts);

                $(document).ready(function() {
                    calculateAddOnTotal();
                });

                // update Main Quantity
                function updateSelling() {
                    const mainSellingEle = $('.selling');
                    const quantity = $('#qtyInput').val();
                    const mainMrp = Number($('#mainMrp').text())
                    const mainSellingValue = Number(mainSellingEle.text());
                    const discount = Math.round((mainMrp - mainSellingValue) / mainMrp * 100);
                    const sortedDiscount = bulkDiscounts.sort((a, b) => b.minQty - a.minQty);
                    const applicableDiscount = sortedDiscount.find(discount => discount.minQty <= quantity);
                    if (applicableDiscount) {
                        $('#mainDiscount').text(discount + '% off');
                        mainSellingEle.text(applicableDiscount.sellingPrice);
                    }
                }

                function removeTooltip() {
                    $('button').each(function() {
                        const instance = bootstrap.Tooltip.getInstance(this);
                        if (instance) {
                            instance.hide();
                        }
                    });
                }

                function showTooltip(ele, text) {
                    const $btn = $(ele);
                    if (!$btn.data('bs.tooltip')) {
                        new bootstrap.Tooltip($btn[0], {
                            trigger: 'manual',
                            placement: 'top',
                            title: text,
                            customClass: 'custom-tooltip-danger'
                        });
                    }

                    $btn.tooltip('show');
                    setTimeout(() => {
                        $btn.tooltip('hide');
                    }, 3000);
                }

                // update Main Quantity
                function updateMainQuantity(operation, target, changeBy, e) {
                    removeTooltip();
                    const targetEle = $(`${target}`);
                    const value = Number(targetEle.val());
                    const minValue = Number(targetEle.attr('min'));
                    const maxValue = Number(targetEle.attr('max'));


                    let newQty = value;
                    const ttText = operation === '+' ?
                        `You can select Maximum ${maxValue} quantities.` :
                        `You can select Minimum ${minValue} quantities.`;

                    if (operation === '+') {

                        newQty = value + changeBy;
                        if (value < minValue || newQty % changeBy !== 0) {
                            newQty = changeBy;
                        }
                    } else if (operation === '-' && value > 1) {
                        newQty = value - changeBy;
                    }

                    // Clamp value between min and max
                    if (newQty < minValue) {
                        targetEle.val(minValue);
                        showTooltip(e, ttText);
                    } else if (newQty > maxValue) {
                        targetEle.val(maxValue);
                        showTooltip(e, ttText);
                    } else {
                        targetEle.val(newQty);
                        updateSelling();
                    }

                    calculateAddOnTotal();
                }
                // for addon
                function updateQuantity(e, operation) {
                    removeTooltip(); // Use existing removeTooltip function

                    const productCard = $(e).closest('.card');
                    const groupInput = $(e).closest('.input-btn-group').find('.addon-quantity');
                

                    const {
                        selling,
                        mrp,
                        max,
                        min,
                        variant,
                        changeby
                    } = groupInput.data();
                  
                    const value = Number(groupInput.val());
                    let newValue = 0;
                    if (operation === '+') {
                        newValue = value + Number(changeby);
                        if (value < min || newValue % changeby !== 0) {
                            newValue = Number(changeby);
                        }
                    } else {
                        newValue = value - Number(changeby);
                    }

                    const ttText = operation === '+' ?
                        `Maximum ${max} quantities allowed.` :
                        `Minimum ${min} quantity required.`;

                    const filteredRanges = variantDiscounts
                        .filter(v => v.product_variant_id == variant)
                        .sort((a, b) => a.minQty - b.minQty);

                    const currentPriceData = filteredRanges.find(v => newValue <= v.maxQty && newValue >= v.minQty);
                    

                    if (!currentPriceData) {
                        if (newValue < min) {
                            groupInput.val(min);
                        }
                        if (newValue > max) {
                            groupInput.val(max);
                        }
                        showTooltip(e, ttText);
                        return;
                    };

                    const {

                        sellingPrice: vSelling
                    } = currentPriceData;
                    const vDiscount = Math.round(((mrp - Number(vSelling)) / mrp) * 100);


                    // Update UI elements
                    productCard.find('.addon-selling-price').text(`₹${vSelling}`);

                    const vMrpElements = {
                        box: productCard.find('.addon-mrp-box'),
                        price: productCard.find('.addon-mrp'),
                        discount: productCard.find('.addon-discount')
                    };

                    if (mrp > vSelling) {
                        vMrpElements.box.removeClass('d-none').val(mrp);
                        vMrpElements.price.text(mrp);
                    } else {
                        vMrpElements.box.addClass('d-none');
                        vMrpElements.discount.addClass('d-none');
                    }

                    if (vDiscount > 0) {
                        vMrpElements.discount.removeClass('d-none').text(`${vDiscount}% Off`);
                    } else {
                        vMrpElements.discount.addClass('d-none');
                    }

                    // Update quantity if within bounds
                    if (newValue > max) {
                        groupInput.val(max);
                        showTooltip(e, ttText);
                    }
                    if (newValue >= min && newValue <= max) {
                        groupInput.val(newValue);
                        calculateAddOnTotal();
                    } else {
                        showTooltip(e, ttText);
                    }
                }

                function updateCart(selectedProducts) {

                    const submitBtn = $('.btn-span');
                    const oldSubmitBtnHtml = submitBtn.html();

                    $('.btn-span').html(
                        `<div class="spinner-border text-light" role="status"><span class="sr-only">Loading...</span></div>`
                    );

                    fetch("{{ route('cartAddMultiple') }}", {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                            },
                            body: JSON.stringify({
                                products: selectedProducts
                            })
                        })
                        .then(response => response.json())
                        .then(res => {

                            if (res.status) {
                                window.location.href = "{{ route('u.cart') }}";
                            } else {
                                $('.error-box').text(res['message']).removeClass('d-none');
                                console.error('Error:', res);
                                submitBtn.html(oldSubmitBtnHtml);
                            }
                        })
                        .catch(error => {
                            console.error('Fetch error:', error);
                        });

                }

                function calculateAddOnTotal() {

                    let total = 0;
                    let addonCount = 0;
                    const mainQtyInput = Number($('#qtyInput').val());
                    const mainSelling = Number($('.selling').text());
                    const mainTotal = mainQtyInput * mainSelling;


                    $('.variant-checkbox:checked').each(function() {
                        const card = $(this).closest('.card');
                        const variantSelect = card.find('.variant-select');
                        const selectedOption = variantSelect.find('option:selected');
                        const addonQty = card.find('.addon-quantity');

                        if (selectedOption.val()) {
                            const price = Number(addonQty.data('selling')) || 0;
                            const qty = Number(addonQty.val()) || 0;
                            addonCount++;
                            total += price * qty;
                        }
                    });

                    $('.addon-plus').toggleClass('d-none', addonCount == 0);
                    $('.addon-div').toggleClass('d-none', addonCount == 0);
                    $('.items-div').toggleClass('d-none', addonCount == 0);
                    $('.empty-items-div').toggleClass('d-none', addonCount > 0);

                    if (addonCount > 0) {
                        $('.add-to-cart-text').text(`ADD ${addonCount+1} ITEMS TO CART`);
                        $('.addon-count').text(addonCount);

                    } else {
                        $('.add-to-cart-text').text('ADD TO CART');
                    }

                    $('.addon-total').text(total.toFixed(2));
                    $('.addon-product-total').text((mainTotal + total).toFixed(2));
                }

                $('.variant-checkbox').on('change', function() {
                    const card = $(this).closest('.card');
                    card.find('.qty-variants-div').toggleClass('d-none', !this.checked);
                    card.find('.amount-box').toggleClass('d-none', !this.checked);
                    calculateAddOnTotal();
                });
                // calculate addon and main total


                $('.addon-quantity').on('change input',
                    function(e) {
                        calculateAddOnTotal();
                    }
                );

                // on-click add to cart
                $('#getSelectedProducts').on('click', function() {
                    let selectedProducts = {};
                    let isValidSelection = true;
                    $('.variant-checkbox').each(function() {
                        const checkedVariant = $(this).prop('checked')
                        if (checkedVariant) {
                            const card = $(this).closest('.card');
                            const selectedOption = card.find('.addon-v-select');
                            const selectedOptionVal = selectedOption.find('option:selected').val();
                            const qty = Number(card.find('.addon-quantity').val()) || 0;
                            const id = selectedOption.find('option:selected').data('pid');
                            if (selectedOptionVal && qty > 0) {
                                selectedProducts[id] = {
                                    variant: selectedOptionVal,
                                    qty: qty,
                                    isParent: false
                                };
                            } else {
                                isValidSelection = false;
                            }
                        }
                    });
                    $('.invalid-selection').toggleClass('d-none', isValidSelection);

                    if (isValidSelection) {
                        selectedProducts["{{ $product->id }}"] = {
                            variant: "{{ $productVariant->id }}",
                            qty: $('#qtyInput').val(),
                            isParent: true
                        };
                        updateCart(selectedProducts);
                    }
                });

                // on-change variant
                $('.variant-select').on('change', function() {
                    const $this = $(this);
                    const selectedOption = $this.find('option:selected');
                    const variantData = selectedOption.data();

                    const productCard = $this.closest('.card');

                    const {
                        mrp,
                        selling
                    } = variantData;
                    const discount = mrp > selling ? Math.round(((mrp - selling) / mrp) * 100) : 0;

                    // Cache DOM queries
                    const $elements = {
                        mrpDiv: productCard.find('.addon-mrp'),
                        mrpDivBox: productCard.find('.addon-mrp-box'),
                        sellingDiv: productCard.find('.addon-selling-price'),
                        discountDiv: productCard.find('.addon-discount'),
                        addonQuantity: productCard.find('.addon-quantity'),
                        qtyBox: productCard.find('.input-btn-group')
                    };

                    // Update MRP and discount display
                    if (mrp > selling) {
                        $elements.mrpDivBox.removeClass('d-none').val(mrp);
                        $elements.mrpDiv.text(mrp);
                        if (discount > 0) {
                            $elements.discountDiv.removeClass('d-none').text(`${discount}% Off`);
                        }
                    } else {
                        $elements.mrpDivBox.addClass('d-none');
                        $elements.discountDiv.addClass('d-none');
                    }

                    // Update quantity and selling price
                    if ($elements.sellingDiv.length) {
                        $elements.sellingDiv.text(selling ? `₹ ${selling}` : '');
                        $elements.addonQuantity
                            .data(variantData)
                            .val(variantData.min);
                    } else {
                        $elements.qtyBox.addClass('d-none');
                        $elements.addonQuantity
                            .data(variantData)
                            .val(1);
                        $elements.sellingDiv.text('');
                    }
                });
            </script>
        @endif
        @if ($product->images->count() > 1)
            <script src="https://cdn.jsdelivr.net/npm/@splidejs/splide@3.6.12/dist/js/splide.min.js"></script>

            <script>
                document.addEventListener('DOMContentLoaded', function() {
                    var main = new Splide('#main-slider', {
                        type: 'fade',
                        height: 'auto', // Set a fixed height or use heightRatio
                        pagination: false,
                        arrows: false,
                        cover: false, // Set this to false to avoid image zoom/cropping
                    });

                    var thumbnails = new Splide('#thumbnail-slider', {
                        fixedWidth: 100,
                        fixedHeight: 100,
                        isNavigation: true,
                        gap: 10,
                        focus: 'center',
                        pagination: false,
                        cover: true,
                        breakpoints: {
                            400: {
                                fixedWidth: 100,
                                fixedHeight: 100,
                            },
                            600: {
                                fixedWidth: 100,
                                fixedHeight: 100,
                            }
                        },
                    });

                    main.sync(thumbnails);
                    main.mount();
                    thumbnails.mount();
                });
            </script>
        @endif
    @endsection

    @section('PAGE-CSS')
        @if ($product->images->count() > 1)
            <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@splidejs/splide@3.6.12/dist/css/splide.min.css" />
        @endif
    @endsection
