<?php

namespace App\Http\Controllers\Graphic;

use App\Http\Controllers\Controller;
use App\Models\Graphic\Design;
use App\Models\Graphic\DesignCategory;
use App\Models\Graphic\DesignOrder;
use App\Models\Graphic\DesignOrderItem;
use App\Models\GraphicLeadBrand;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class DesignController extends Controller
{
    //

    public function orderItemsSelect(Request $request, $order)
    {
        try {
            $data['orderId'] = $order;

            $data['order'] = DesignOrder::where(['id' => $order, 'lead_id' => Auth::id()])->firstOrFail();

            $data['activeCategory'] =  $request->query('category');

            $data['categories'] = DesignCategory::where('parent_id', null)
                ->withCount('subcategories')
                ->with(['subcategories'])
                ->orderBy('name')
                ->get();
            if ($data['activeCategory']) {
                $data['category'] = DesignCategory::where('id', $data['activeCategory'])->with(['subcategories'])->first();
                if ($data['category']) {
                    $data['activeSubCategory'] = $data['category']->subcategories->pluck('id')->toArray();
                }
            }

            $data['designs'] = Design::query()
                ->where('isApproved', 1)
                ->with('category')
                ->orderBy('id', 'desc')
                ->when($data['activeCategory'] && isset($data['category']), function ($query) use ($data) {
                    if ($data['category']->subcategories->count() > 0) {
                        $query->where(function ($q) use ($data) {
                            $q->whereIn('graphic_design_category_id', $data['activeSubCategory'])
                                ->orWhere('graphic_design_category_id', $data['activeCategory']);
                        });
                    } else {
                        $query->where('graphic_design_category_id', $data['activeCategory']);
                    }
                })
                ->paginate(9);
            $data['brands'] = GraphicLeadBrand::where('lead_id', Auth::user()->id)->get();
            // dd($data['brands']->toArray());
            return view('userpages.projects.graphic.select', $data);
        } catch (Exception $e) {
            return redirect('/')->with('error', ErrMsg());
        }
    }

    public function orderItemsStore(Request $request)
    {
        // dd($request->all());
        try {
            $dOrder = DesignOrder::where(['id' => $request->graphicOrderId, 'lead_id' => Auth::id()])->firstOrFail();

            $storeArray = [];
            foreach ($request->selectedImages as $k => $img) {
                $storeArray[$k] = [
                    'graphic_design_order_id' => $request->graphicOrderId,
                    'graphic_design_id' => $img,
                    'graphic_lead_brand_id' => $request->brand_id
                ];
            }

            $dOrder->items()->createMany($storeArray);

            return response()->json(['status' => true, 'message' => 'Order items added successfully.']);
        } catch (Exception $e) {
            // dd($e);
            return response()->json(['status' => false, 'message' => 'Something went wrong.']);
        }
    }

    public function orderItemsView(Request $request, $order)
    {
        $data['orderId'] = $order;
        $data['order'] = DesignOrder::where(['id' => $order, 'lead_id' => Auth::id()])->with('orderItem:id,name')->firstOrFail();
        $data['items'] = DesignOrderItem::where('graphic_design_order_id', $order)
            ->with('design.category')->orderBy('id', 'DESC')
            ->paginate(12);


        return view('userpages.projects.graphic.orderView', $data);
    }
}
