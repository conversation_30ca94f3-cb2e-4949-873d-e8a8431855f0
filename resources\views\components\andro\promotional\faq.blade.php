<div class="container-fluid p-lg-5 p-lg-5 pt-4 pb-4 py-5 bg-light-shade">
    <div class="container-lg p-0">
        <h2 class="text-center fw-bold h1 text-pink">Frequently Asked Questions</h2>

        <div class="faq-accordion pt-5" id="accordion1">
            <button class="btn faq-btn btn-link" data-bs-toggle="collapse" data-bs-target="#collapseSeven"
                aria-expanded="true" aria-controls="collapseSeven">
                <a aria-label="link" class="faq-content">
                    <div class="faq-card-header" id="headingSeven">
                        <div class="faq-heading fs-5 text-start text-dark-color">What is the Desktop-Based version of AndroSMS?</div>
                    </div>
                </a>
            </button>
            <div id="collapseSeven" class="accordion-collapse collapse show" aria-labelledby="headingSeven" data-bs-parent="#accordion1">
                <div class="faq-div d-flex faq-para flex-lg-row flex-md-column flex-column mb-3 mb-3-para">
                        <p class=" fs-6 text-dark-color">The Desktop-Based AndroSMS tool is a Windows/macOS application that connects with Android phones to send bulk SMS and RCS messages directly from your computer. It’s ideal for businesses that want local control without relying on cloud infrastructure.</p>
                </div>
            </div>
            <button class="btn faq-btn btn-link collapsed" data-bs-toggle="collapse" data-bs-target="#collapseEight"
                aria-expanded="false" aria-controls="collapseEight">
                <a aria-label="link" class="faq-content">
                    <div class="faq-card-header" id="headingEight">
                        <div class="faq-heading fs-5 text-start text-dark-color"> Do I need internet on the desktop app?
                        </div>
                    </div>
                </a>
            </button>
            <div id="collapseEight" class="accordion-collapse collapse" aria-labelledby="headingEight" data-bs-parent="#accordion1">
                <div class="faq-div">
                    <p class="faq-para fs-6 text-dark-color">Yes, your desktop must be connected to the internet for syncing with your Android devices and managing the campaign dashboard.
                    </p>
                </div>
            </div>
            <button class="btn faq-btn btn-link collapsed" data-bs-toggle="collapse" data-bs-target="#collapseNine"
                aria-expanded="false" aria-controls="collapseNine">
                <a aria-label="link" class="faq-content">
                    <div class="faq-card-header" id="headingNine">
                        <div class="faq-heading fs-5 text-start text-dark-color"> Does the desktop version support multiple devices at once?
                        </div>
                    </div>
                </a>
            </button>
            <div id="collapseNine" class="accordion-collapse collapse" aria-labelledby="headingNine" data-bs-parent="#accordion1">
                <div class="faq-div">
                    <div class="faq-para fs-6 mb-3 text-dark-color">
                        <p class="faq-para fs-6 ">Yes. You can connect multiple Android phones simultaneously. This increases your sending capacity and adds failover reliability in case one device goes offline.

</p>
                    </div>
                </div>
            </div>
            <button class="btn faq-btn btn-link collapsed" data-bs-toggle="collapse" data-bs-target="#collapseTen"
                aria-expanded="false" aria-controls="collapseTen">
                <a aria-label="link" class="faq-content">
                    <div class="faq-card-header" id="headingTen">
                        <div class="faq-heading fs-5 text-start text-dark-color">Is bulk contact upload supported on desktop?</div>
                    </div>
                </a>
            </button>
            <div id="collapseTen" class="accordion-collapse collapse" aria-labelledby="headingTen" data-bs-parent="#accordion1">
                <div class="faq-div">
                    <p class="faq-para fs-6 text-dark-color">Yes. You can upload Excel or CSV files directly into the desktop interface. The system maps fields automatically, simplifying large imports.</p>
                </div>
            </div>
            <button class="btn faq-btn btn-link collapsed" data-bs-toggle="collapse" data-bs-target="#collapseEleven"
                aria-expanded="false" aria-controls="collapseEleven">
                <a aria-label="link" class="faq-content">
                    <div class="faq-card-header" id="headingEleven">
                        <div class="faq-heading fs-5 text-start text-dark-color">Can I schedule messages using the desktop tool?
                        </div>
                    </div>
                </a>
            </button>
            <div id="collapseEleven" class="accordion-collapse collapse" aria-labelledby="headingEleven" data-bs-parent="#accordion1">
                <div class="faq-div">
                    <p class="faq-para fs-6 text-dark-color">Absolutely. You can create and schedule one-time or recurring campaigns directly from your desktop. No internet is required during the scheduled time if the Android device remains connected.

</p>
                </div>
            </div>
            <button class="btn faq-btn btn-link collapsed" data-bs-toggle="collapse" data-bs-target="#collapseTwelve"
                aria-expanded="false" aria-controls="collapseTwelve">
                <a aria-label="link" class="faq-content">
                    <div class="faq-card-header" id="headingTwelve">
                        <div class="faq-heading fs-5 text-start text-dark-color">Can I monitor delivery and status on desktop?</div>
                    </div>
                </a>
            </button>
            <div id="collapseTwelve" class="accordion-collapse collapse" aria-labelledby="headingTwelve" data-bs-parent="#accordion1">
                <div class="faq-div">
                    <p class="faq-para fs-6 text-dark-color">Yes. You’ll get real-time delivery reports, status tracking, and failure reasons all from the desktop dashboard.</p>
                </div>
            </div>
            <button class="btn faq-btn btn-link collapsed" data-bs-toggle="collapse" data-bs-target="#collapsethirteen"
            aria-expanded="false" aria-controls="collapsethirteen">
            <a aria-label="link" class="faq-content">
                <div class="faq-card-header" id="headingthirteen">
                    <div class="faq-heading fs-5 text-start text-dark-color"> Does it support rich media and RCS features?</div>
                </div>
            </a>
        </button>
        <div id="collapsethirteen" class="accordion-collapse collapse" aria-labelledby="headingthirteen" data-bs-parent="#accordion1">
            <div class="faq-div">
                <p class="faq-para fs-6 text-dark-color">Yes, if the recipient supports RCS. You can send media-rich content such as images, buttons, suggested replies, and carousels via the desktop interface.</p>
            </div>
        </div>
        </div>
    </div>
</div>

