<?php if($data->lastPage() > 1): ?>
    <?php
        $currentPage = $data->currentPage();
        $lastPage = $data->lastPage();
        $navButtonClass = "m-0 p-0 rounded-circle bg-white d-flex align-items-center justify-content-center";
        $navStyle = "width:33px; height: 33px;";
        $linkClass = "text-decoration-none pb-0";
    ?>
    <div class="m-3">
        <div class="mt-2 mb-2">
            <div class="rounded-pill d-flex align-items-center justify-content-center" style="height: 33px;">
                
                <?php if($currentPage != 1): ?>
                    <div class="<?php echo e($navButtonClass); ?>" style="<?php echo e($navStyle); ?>">
                        <a href="<?php echo e($data->url(1)); ?>" class="<?php echo e($linkClass); ?> text-dark d-flex">
                            <i class="fa-solid fa-angle-left d-flex align-items-center"></i>
                            <i class="fa-solid fa-angle-left d-flex align-items-center"></i>
                        </a>
                    </div>
                <?php endif; ?>

                
                <div class="<?php echo e($navButtonClass); ?> me-2" style="<?php echo e($navStyle); ?>">
                    <a href="<?php echo e($currentPage == 1 ? '#' : $data->url($currentPage - 1)); ?>"
                       class="<?php echo e($linkClass); ?> <?php echo e($currentPage == 1 ? 'text-secondary' : 'text-dark'); ?>">
                        <i class="fa-solid fa-angle-left d-flex align-items-center"></i>
                    </a>
                </div>

                
                <ul class="bg-white d-flex align-items-center justify-content-center gap-3 rounded-pill px-3 py-1">
                    <?php
                        $start = $data->total() <= 6 ? 1 : max(1, $currentPage - 2);
                        $end = $data->total() <= 6 ? $lastPage : min($lastPage, $currentPage + 2);
                    ?>
                    <?php for($i = $start; $i <= $end; $i++): ?>
                        <li class="m-0 p-0 rounded" <?php if($currentPage == $i): ?> style="background-color: #194DAB;" <?php endif; ?>>
                            <a href="<?php echo e($data->url($i)); ?>"
                                class="pb-0 px-2 text-decoration-none <?php echo e($currentPage == $i ? 'text-white' : 'text-dark'); ?>"
                                <?php if($currentPage == $i): echo 'disabled'; endif; ?>>
                                <?php echo e($i); ?>

                            </a>
                        </li>
                    <?php endfor; ?>
                </ul>

                
                <div class="<?php echo e($navButtonClass); ?> ms-2" style="<?php echo e($navStyle); ?>">
                    <a href="<?php echo e($currentPage == $lastPage ? '#' : $data->url($currentPage + 1)); ?>"
                       class="<?php echo e($linkClass); ?> <?php echo e($currentPage == $lastPage ? 'text-secondary' : 'text-dark'); ?>">
                        <i class="fa-solid fa-angle-right d-flex align-items-center"></i>
                    </a>
                </div>

                
                <?php if($currentPage != $lastPage): ?>
                    <div class="<?php echo e($navButtonClass); ?> me-3" style="<?php echo e($navStyle); ?>">
                        <a href="<?php echo e($data->url($lastPage)); ?>" class="<?php echo e($linkClass); ?> text-dark d-flex">
                            <i class="fa-solid fa-angle-right d-flex align-items-center"></i>
                            <i class="fa-solid fa-angle-right d-flex align-items-center"></i>
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
<?php endif; ?>
<?php /**PATH C:\Users\<USER>\Desktop\live\websites_laravel\resources\views/components/showPagination.blade.php ENDPATH**/ ?>