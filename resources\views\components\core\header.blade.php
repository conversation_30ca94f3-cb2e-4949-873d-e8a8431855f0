<div class="container-fluid global-header p-0">
    <div class="home d-flex align-items-center justify-content-between container-lg p-0" id="home"
        style="height: 35px;">
        @php
            $host = request()->getHttpHost();
            $isRapbooster = in_array($host, ['localhost:8009', 'rapbooster.com', 'rapbooster.orkia.in']);
        @endphp

        <div class="align-items-center d-flex">
            @if ($isRapbooster)
                <div class="align-items-center d-lg-flex d-md-flex d-none">
                    <span class="pe-2 ps-2"><i class="fa-solid fa-phone" style="color: #525260"></i></span>
                    <a href="tel:+91-9680440022" class="text-decoration-none fw-semibold font-sm-header"
                        style="color: #525260">Sales: +91-9680440022</a>
                    &nbsp;|&nbsp;
                    <a href="tel:+91-8824799800" class="text-decoration-none fw-semibold font-sm-header"
                        style="color: #525260">Support: +91-8824799800</a>
                </div>
            @else
                <a aria-label="link" class="navbar-brand" href="https://dunesfactory.com" target="_blank">
                    <img style="height: 30px;" class="m-0" src="{{ asset('assets/global/images/logo/df.png') }}"
                        alt="logo image">
                </a>
                <div class="first d-flex" id="first">
                    <div class="c-dropdown float-left">
                        <button id="allProductsBtn" class="c-dropbtn border-0 mb-1" onclick="dropDownGlobal()">
                            <span class="d-flex align-items-center gap-2 fw-bold"
                                style="font-size: 16px; color: black;">
                                All Products
                                <i id="changeIconDown" class="fa-solid fa-chevron-down"></i>
                                <i id="changeIconUp" class="fa-solid fa-chevron-up ml-2" style="display: none;"></i>
                            </span>
                        </button>
                        <div class="c-dropdown-content container-fluid position-absolute bg-white">
                            <div class="GH-all-product container mt-3 w-auto pb-3 p-0">
                                @include('components.global.allproductGrid')
                            </div>
                        </div>
                    </div>
                </div>
            @endif
        </div>

        <div class="d-flex">
            @auth
                @unless (Auth::user()->hidePrice)
                    <div class="d-flex align-items-center me-3">
                        <a class="position-relative pe-2" aria-label="cart" href="{{ route('u.cart') }}">
                            <i class="fa-solid fs-5 lh-sm fa-cart-shopping" style="color: #525260"></i>
                            <span class="position-absolute start-100 translate-middle badge rounded-pill bg-danger"
                                style="font-size: 11px; padding: 2px 5px; top: 20%;">
                                {{ auth()->user()->carts->count() }}
                            </span>
                        </a>
                    </div>
                @endunless

                <div class="dropdown">
                    <button class="border-0 fw-semibold me-2 px-2 rounded-circle text-uppercase text-white"
                        style="background-color: #F78200;" type="button" data-bs-toggle="dropdown">
                        {{ auth()->user()->name[0] ?? 'U' }}
                    </button>
                    <div class="dropdown-menu m-2 me-0 rounded shadow-sm" style="width: max-content;">
                        @foreach ([['route' => 'user.profile', 'icon' => 'fa-user', 'title' => 'Profile'], ['route' => 'user.address', 'icon' => 'fa-user-tag', 'title' => 'Billing Profile'], ['route' => 'user.s.tickets', 'icon' => 'fa-flag', 'title' => 'Support Tickets'], ['route' => 'user.invoices', 'icon' => 'fa-clipboard-list', 'title' => 'Invoices'], ['route' => 'user.licences', 'icon' => 'fa-address-card', 'title' => 'Subscriptions'], ['route' => 'user.transactions', 'icon' => 'fa-brands fa-cc-mastercard', 'title' => 'Transactions'], ['route' => 'graphics.orders', 'icon' => 'fa-briefcase', 'title' => 'Graphic Project'], ['route' => 'brand.index', 'icon' => 'bi bi-box2-heart-fill', 'title' => 'Graphic Brand']] as $item)
                            <a href="{{ route($item['route']) }}"
                                class="dropdown-item ps-2 pe-4 py-2 {{ Route::is($item['route']) ? 'c-bg-light-blue' : 'text-secondary' }}">
                                <div class="d-flex align-items-center fs-5">
                                    <i class="fa-solid {{ $item['icon'] }} me-2" style="width: 30px;"></i>
                                    {{ $item['title'] }}
                                </div>
                            </a>
                        @endforeach

                        <div class="border-top mt-2"></div>
                        <a href="/logout" class="dropdown-item ps-2 pe-4 py-2 text-secondary">
                            <div class="d-flex align-items-center fs-5">
                                <i class="fa-solid fa-circle-left me-2" style="width: 30px;"></i>
                                Sign Out
                            </div>
                        </a>
                    </div>
                </div>
            @else
                <div class="getstarted">
                    <a href="{{ route('login') }}" class="btn text-dark">Log In/Sign Up</a>
                </div>
            @endauth
        </div>
    </div>
</div>
