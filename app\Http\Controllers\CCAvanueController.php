<?php

namespace App\Http\Controllers;

use Exception;
use App\Models\Invoice;
use Illuminate\Support\Str;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\Redirect;

class CCAvanueController extends Controller
{

    public function charge(Request $request, $invoice = null)
    {
        try {
            $str = json_decode(Crypt::decryptString($request->PaymentKey), true);
            $hostdata = hostData();
        } catch (Exception $e) {
            return redirect()->back()->with('error', 'Invalid Key');
        }

        try {

            if (isset($str['mode']) && $str['mode'] == 'addFund') {

                $invoice = null;
            } else {
                if (isset($str['invoice_id']) && $str['invoice_id'] != null) {
                    $inv_id = $str['invoice_id'];
                } else {
                    $inv = generateInvoice($str['lead_id'] ?? auth()->id());
                    

                    if ($inv['status'] == true) {
                        $inv_id = $inv['id'];
                    } else {
                        return redirect()->back()->with('error', ErrMsg());
                    }
                }
                $invoice = Invoice::find($inv_id ?? null);
                if ($invoice) {
                    if ($invoice->isPaid == 1) {
                        return redirect()->route('alreadyPaid');
                    }
                }
            }

            $lead = auth()->user();
            $amt = ($str['amount']);
            $data['order_id'] = time() . '_CCA_' . Str::random(8);
            $data['currency'] = 'INR';
            // $data['redirect_url'] = url("ccavResponseHandler");
            // $data['cancel_url'] =  url("ccavResponseHandler");
            $data['redirect_url'] = url("ccavResponseHandler");
            $data['cancel_url'] = url("ccavResponseHandler");
            DB::table('ccavenue_txn')->insert([
                'amount' => $amt,
                'currency' => $data['currency'],
                'orderId' => $data['order_id'],
                'lead_id' => $invoice->lead_id ?? auth()->id(),
                'cancelUrl' => $data['redirect_url'],
                'redirectUrl' => $data['redirect_url'],
                'invoice_id' => $invoice->id ?? null,
            ]);
            $data['amount'] = $amt;
            $data['language'] = 'EN';
            $data['billing_name'] = ($invoice ? $invoice->name : null) ?? ($invoice ? $invoice->lead->name : null) ?? $lead->name ?? null;
            $data['billing_address'] = $invoice->address ?? $lead->address ?? null;
            $data['billing_city'] = ($invoice ? $invoice->city : null) ?? $lead->city ?? null;
            $data['billing_state'] = ($invoice ? $invoice->state : null) ?? $lead->state ?? null;
            $data['billing_zip'] = ($invoice ? $invoice->pincode : null) ?? $lead->pincode ?? null;
            $data['billing_country'] = ($invoice ? $invoice->country : null) ?? $lead->country ?? null;
            $data['billing_tel'] = ($invoice ? ($invoice->mobile ?? $invoice->lead->mobile) : null) ?? $lead->mobile ?? null;
            $data['billing_email'] = ($invoice ? $invoice->email : null) ?? ($invoice ? cleanEmail($invoice->lead->email) : null) ?? $lead->email ?? $hostdata->smtpUsername;
            $data['merchant_param1'] = $hostdata->name;
            $data['customer_identifier'] = $invoice->lead_id ?? auth()->id();
            $data['merchant_id'] = $hostdata->ccAvenueMerchantId;
            $access_code = $hostdata->ccAvenueAccessCode;
            $working_key = $hostdata->ccAvenueWorkingKey;
            $merchant_data = http_build_query($data);
            $encrypted_data = $this->encryptCCA($merchant_data, $working_key);

            return Redirect::away("https://secure.ccavenue.com/transaction/transaction.do?command=initiateTransaction&encRequest=$encrypted_data&access_code=$access_code");

            /*
            For Live The post action URL must be https://secure.ccavenue.com/transaction/transaction.do?command=initiateTransaction

            For Test The post action URL must be https://test.ccavenue.com/transaction/transaction.do?command=initiateTransaction
            */
        } catch (Exception $e) {


            return Redirect::back()->with("error", ErrMsg());
        }
    }
    public function ccavResponseHandler(Request $request)
    {
        try {
            $hostdata = hostData();
            $working_key = $hostdata->ccAvenueWorkingKey;
            $rcvdString = ($this->decryptCCA($request->encResp, $working_key));
            parse_str($rcvdString, $resarray);
            $responce = json_encode($resarray);
            

            $record = DB::table('ccavenue_txn')->where('orderId', $resarray['order_id'])->first();
            
            if ($resarray['order_status'] == 'Invalid' || $resarray['order_status'] == 'Failure') {
                return redirect()->route('invoice.Payment', ['invoice_id' => $record->invoice_id])->with('error', 'CCAvanue : ' . $resarray['status_message']);
            }
            $record2 = DB::table('ccavenue_txn')->where('orderId', $resarray['order_id'])->update([
                'response' => $responce,
                'amount' => $resarray['amount'],
                'currency' => $resarray['currency'],
                'trackingId' => $resarray['tracking_id'],
            ]);

            // $msg = $resarray['order_status'];

            return redirect(route('payment.captured', ['invoice_id' => Crypt::encryptString($record->invoice_id)]));


        } catch (Exception $e) {
            
            return redirect('/')->with('info', "The transaction status will be reflected within a short period.");
            if (Auth::check()) {
            } else {
                return redirect()->route('login')->with('info', "The transaction status will be reflected within a short period.");
            }
        }
    }
    function encryptCCA($plainText, $key)
    {
        $key = $this->hextobin(md5($key));
        $initVector = pack("C*", 0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f);
        $openMode = openssl_encrypt($plainText, 'AES-128-CBC', $key, OPENSSL_RAW_DATA, $initVector);
        $encryptedText = bin2hex($openMode);
        return $encryptedText;
    }
    function hextobin($hexString)
    {
        $length = strlen($hexString);
        $binString = "";
        $count = 0;
        while ($count < $length) {
            $subString = substr($hexString, $count, 2);
            $packedString = pack("H*", $subString);
            if ($count == 0) {
                $binString = $packedString;
            } else {
                $binString .= $packedString;
            }

            $count += 2;
        }
        return $binString;
    }

    function decryptCCA($encryptedText, $key)
    {
        $key = $this->hextobin(md5($key));
        $initVector = pack("C*", 0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f);
        $encryptedText = $this->hextobin($encryptedText);
        $decryptedText = openssl_decrypt($encryptedText, 'AES-128-CBC', $key, OPENSSL_RAW_DATA, $initVector);
        return $decryptedText;
    }
}
