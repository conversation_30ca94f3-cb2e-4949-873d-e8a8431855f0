@php
    $faqs = [
        [
            'question' => 'How is WhatsApp Business API different from the WhatsApp Business app?',
            'answer' =>
                'WhatsApp Business App: It is used by small businesses for one-on-one & group messaging with templates. Available on iOS & Android mobile platforms. Accounts on the business app are created using the mobile number of the business (must be different from WhatsApp Messenger). Supports voice & video calls. Automation options include: Greeting Messages, Away Messages, Quick Replies. Contact list is accessed from phone contacts & labels. Broadcasts are limited to 256 contacts. Free to download from Play Store or App Store. WhatsApp Business API: Used by medium & large businesses for automated processes via APIs. No mobile interface. Requires application via WhatsApp Business Solution Partners (e.g., WA Bhai). No voice/video calling. Automation via API integrations. Contacts received through API. Conversation-based pricing. No limit on number of broadcast recipients.',
        ],
        [
            'question' => 'What are the prerequisites to go live with the WhatsApp API?',
            'answer' =>
                'Three things are essential to go live with WhatsApp Business API: Facebook Business Manager, WhatsApp Business Account (WABA), and Line of Credit. WA Bhai can assist if you don’t have them.',
        ],
        [
            'question' => 'Can business messages be sent over WhatsApp for free?',
            'answer' =>
                'Messages sent via the WhatsApp Business app are free. Messages sent via the WhatsApp Business API are subject to pricing.',
        ],
        [
            'question' =>
                'Should I do away with my existing customer engagement channels to go live with WhatsApp Business API?',
            'answer' =>
                'Not at all. WhatsApp complements your existing channels. Omnichannel communication is encouraged.',
        ],
        [
            'question' => 'How do I get access to WhatsApp API?',
            'answer' =>
                'Apply for the WhatsApp Business API through the official website and follow the application process.',
        ],
        [
            'question' => 'What functions can be integrated with the WhatsApp Business API to optimize workflows?',
            'answer' =>
                'You can integrate customer support, CRM systems, order management, payment gateways, notification systems, feedback/surveys, and analytics/reporting to optimize workflows and improve customer experience.',
        ],
    ];

@endphp


<div class="container-fluid pb-lg-5 bg-faq pe-0 ps-0">
    <div class="container-lg pt-5 pb-5">
        <h2 class="text-center text-dark-color">Frequently Asked Questions</h2>
        <div class="container-lg">
            <x-faq-accordion :faqs="$faqs" collapsed-color="#ffffff" active-color="#153230" />
        </div>
    </div>
</div>
