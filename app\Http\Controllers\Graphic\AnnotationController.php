<?php

namespace App\Http\Controllers\Graphic;

use App\Http\Controllers\Controller;
use App\Models\Graphic\Annotation;
use App\Models\Graphic\DesignOrder;
use App\Models\Graphic\DesignOrderItem;
use Illuminate\Http\Request;
use Exception;
use Illuminate\Support\Facades\Auth;

class AnnotationController extends Controller
{
    public function getByImage($image_id)
    {
        try {
            $annotations = Annotation::where('graphic_design_order_item_id', $image_id)
                ->with('byLead', 'byUser')
                ->orderBy('datetime')
                ->get();
            return response()->json([
                'annotations' => $annotations,
                'success' => true
            ]);
        } catch (Exception $e) {
            // 
            return response()->json(['error' => 'Failed to get annotations'], 500);
        }
    }

    public function store(Request $request)
    {
        // dd($request->all());
        try {

            // Handle the new integrated structure
            $request->validate([
                'annotationId' => 'nullable|string',
                'comment' => 'required',
                'graphic_design_order_item_id' => 'required',
            ]);
            $insert = [
                'graphic_design_order_item_id' => $request->graphic_design_order_item_id,
                'annotationId' => $request->annotationId ?? null,
                'comment' => $request->comment,
                'lead_id' => Auth::id(),
                'annotationJson' => $request->annotationJson ?? null,
            ];
            if ($request->has('annotationId')) {

                $annotation = Annotation::updateOrCreate(
                    [
                        'graphic_design_order_item_id' => $request->graphic_design_order_item_id,
                        'annotationId' => $request->annotationId
                    ],
                    $insert
                );
            } else {

                $annotation = Annotation::create($insert);
            }
            return response()->json([
                'message' => 'Annotation saved',
                'annotation' => $annotation,
                'status' => true
            ]);
        } catch (Exception $e) {
            // 
            dd($e);
            return response()->json(['message' => 'Failed to store annotation', 'status' => false], 500);
        }
    }

    public function update(Request $request, $id)
    {
        try {
            $data = $request->validate([
                'annotation' => 'required|array'
            ]);

            $annotation = Annotation::findOrFail($id);
            $annotation->update([
                'data' => $data['annotation']
            ]);

            return response()->json(['message' => 'Annotation updated']);
        } catch (Exception $e) {

            return response()->json(['error' => 'Failed to update annotation'], 500);
        }
    }

    public function destroy($id)
    {
        try {
            $annotation = Annotation::where('annotationId', $id)->first();
            if ($annotation) {
                $annotation->delete();
            }
            return response()->json(['status' => true, 'message' => 'Annotation deleted']);
        } catch (Exception $e) {
            return response()->json(['status' => false, 'message' => 'Failed to delete annotation'], 500);
        }
    }

    public function destroyAll($id)
    {
        // DesignOrderItem id
        try {
            $annotation = Annotation::where('graphic_design_order_item_id', $id)->get();
            // dd($annotation->toArray(), $annotation->count(), );
            if ($annotation->count()) {
                Annotation::where('graphic_design_order_item_id', $id)->delete();
            }
            return response()->json(['status' => true, 'message' => 'Annotation deleted']);
        } catch (Exception $e) {

            return response()->json(['status' => false, 'message' => 'Failed to delete annotation'], 500);
        }
    }


    public function removeComment($id)
    {
        try {
            $annotation = Annotation::findOrFail($id);
            // dd($id,    $annotation);
            $annotation->delete();
            return response()->json(['status' => true, 'message' => 'Annotation deleted']);
        } catch (Exception $e) {
            return response()->json(['status' => false, 'error' => 'Failed to delete annotation'], 500);
        }
    }

    public function demoOrderView($id)
    {
        // dd($id);
        $data['orderItem'] = DesignOrderItem::where('id', $id)
            ->with([
                'design',
                'comments',
                'annotations',
                'annotations.byLead:id,name',
                'annotations.byUser:id,name',
                'comments.byLead:id,name',
                'comments.byUser:id,name'
            ])
            ->firstOrFail();
            
            $data['orderItem']->annotations = $data['orderItem']->annotations->map(function ($annotation, $index) {
                $annotation->tag = $index + 1;
                return $annotation;
            });
            // dd($data['orderItem']->annotations->toJson());

        if ($data['orderItem']->image) {
            $data['previewImage'] = $data['orderItem']->image;
        } else {
            $data['previewImage'] = $data['orderItem']->design->image;
        }

        return view('userpages.projects.graphic.orderImageAnnotaion', $data);
    }
}
