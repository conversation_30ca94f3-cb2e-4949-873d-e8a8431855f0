<?php

namespace App\Models\Graphic;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class DesignCategory extends Model
{
    use HasFactory;
    protected $table = "graphic_design_category";
    public $timestamps = false;

    public function designs()
    {
        return $this->hasMany(Design::class, 'graphic_design_category_id', 'id')->where('isApproved', 1);
    }
    public function subcategories()
    {
        return $this->hasMany(DesignCategory::class, 'parent_id', 'id')->orderBy('name')->withCount('designs');
    }
}
