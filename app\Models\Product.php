<?php

namespace App\Models;


use App\Models\productAddon;
use App\Models\ProductFeature;
use App\Models\ProductGallery;
use App\Models\ProductVariant;
use App\Models\ProductCategory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Product extends Model
{
    use HasFactory;
    protected $table = 'product';
    public $timestamps = false;

    public function images()
    {
        return $this->hasMany(ProductGallery::class, 'product_id', 'id')->orderBy('sortOrder', 'asc');
    }
    public function primaryImage()
    {
        return $this->hasOne(ProductGallery::class, 'product_id', 'id')->orderByDesc('isPrimary');
    }
    public function variants()
    {
        return $this->hasMany(ProductVariant::class, 'product_id', 'id')
            ->where(['isActive' => 1, 'isOnWebsite' => 1])->with('ranges')->orderBy('isDefault', 'DESC');
    }
    public function category()
    {
        return $this->belongsTo(ProductCategory::class, 'product_category_id', 'id');
    }

    public function features()
    {
        return $this->hasMany(ProductFeature::class, 'product_id', 'id')->where('isActive', 1);
    }

    public function addons()
    {
        return $this->hasMany(productAddon::class, 'product_id', 'id')->with('product');
    }

    public function addonsProducts()
    {
        return $this->hasManyThrough(Product::class, productAddon::class, 'product_id', 'id')
            ->where(['isActive' => 1, 'isOnWebsite' => 1])
            ->with('variants', 'variants.ranges');
    }
}
