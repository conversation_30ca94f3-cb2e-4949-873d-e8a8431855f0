@extends('userpages.main')
@section('title','Billing Profile')
@section('userpagesection')

<div class="ms-lg-2 m-0">
    <div class="">
        <div class="bg-white pt-2 pb-2 pe-3 ps-3 mb-3 d-flex align-items-center justify-content-between" style="border-radius: 7px;">
            <h4 class="heading-font">Billing Profile</h4>
            <div style="width: fit-content;">
                {{-- <a href="{{ route('billingAddress.create') }}"> --}}
                <button class="text-white rounded-pill border-0 px-3 pt-1 pb-1 d-flex align-items-center gap-2 btn-bg-blue" data-bs-toggle="modal" data-bs-target="#addAddress">
                    <i class="fa-solid fa-plus"></i>
                    Create Billing Profile
                </button>
                {{-- </a> --}}
            </div>
        </div>

        @if ($addresses->count() != 0)
        <div class="overflow-auto change-scroll" style="border-radius: 7px; height: 64vh;">
            <div class="billing-grid">
                @foreach ($addresses as $address)
                <div class="billing-grid-items p-3 overflow-hidden" style="min-height: 220px">
                    @if ($address->isPrimary == 1)
                    <div class="ribbon">Default</div>
                    @endif
                    <div class="d-flex align-items-center justify-content-between h-100 flex-column @if ($address->address == null)  @endif">
                        <div class="align-self-baseline">
                            <h4 style="font-size: 20px !important; font-weight:500 !important;">
                                {{ $address->name ?? '-' }}
                            </h4>
                            <div class="d-flex align-items-center gap-2">
                                <h4 class="mb-0" style="font-size: 20px !important; font-weight:500 !important;">
                                    {{ $address->company ?? '-' }}
                                </h4>
                                <span class="gray-dark-text">{{ $address->gst ?? '-' }}</span>
                            </div>
                            <p class="gray-dark-text mb-0">
                                {{-- {{ $address->id }} --}}
                                {{ $address->address ? $address->address . ', ' : '' }}
                                <br>
                                {{ $address->city ? $address->city . ', ' : '' }}
                                {{ $address->state ? $address->state . ', ' : '' }}
                                {{ $address->country ? $address->country : '' }}
                                {{ $address->pincode ? ' (' . $address->pincode . ') ' : '' }}

                            </p>
                        </div>
                        <div class="d-flex align-items-center align-self-baseline gap-2">
                            <button type="button" class="btn btn-link text-decoration-none p-0 a billingProfileEditBtn" style="color: #194DAB;" value="{{ $address->id }}">
                                Edit
                            </button>
                            <div class="border-verti"></div>

                            <form action="{{ route('billingAddress.destroy', ['billingAddress' => $address->id]) }}" method="POST">
                                @csrf
                                @method('DELETE')
                                <button type="submit" class="btn btn-link text-decoration-none px-0" style="color: #194DAB;">Delete</button>
                            </form>
                            @if ($address->isPrimary != 1)
                            <div class="border-verti"></div>
                            <form action="{{ route('billingAddress.setasDefault') }}" method="post">
                                @csrf
                                <input type="hidden" name="id" value="{{ $address->id }}" />
                                <input type="hidden" name="event" value="setAsDefault" />
                                <button type="submit" class="btn btn-link text-decoration-none px-0" style="color: #194DAB;">Set as
                                    default</button>
                            </form>
                            @endif

                        </div>
                    </div>
                </div>
                @endforeach
            </div>
        </div>
        @else
        <div class="d-flex align-items-center flex-column bg-white justify-content-center">
            <img src="{{ asset('assets/userpages/images/billing.png') }}" alt="blank_page_img" class="img-fluid">
            <button class="bg-transparent fs-4 fw-medium mb-4 border border-0 border-bottom text" style="color: #194dab;" data-bs-toggle="modal" data-bs-target="#addAddress">
                Create Billing Profile
            </button>
        </div>
        @endif
    </div>


    <x-showPagination :data=$addresses />

</div>
@endsection
@section('PAGE-script')
@include('userpages.billingAddress.addModal')

@include('userpages.billingAddress.EditModal')

@if (session('isEditModalShow') == true)
<script>
    $(document).ready(function() {
        new bootstrap.Modal($('#billingProfileEdit')).show()
    });
</script>
@endif
@if (session('isAddModalShow') == true)
<script>
    $(document).ready(function() {
        new bootstrap.Modal($('#addAddress')).show()
    });
</script>
@endif



@endsection
