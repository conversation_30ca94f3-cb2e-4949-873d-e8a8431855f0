@extends('components.andro.main')


@php
    $product_id = 5;
@endphp

@section('andro-css')
    <style>
        .bg-purple {
            background-color: #5a189a;

        }

        .output-box {
            background-color: #6a1b9a;
            color: #ffd700;
        }

        .form-range {
            background-color: transparent;
        }

        /* calculator */
        .calculator {
            background: #3f1651;
            border-radius: 10px;
            padding: 2rem;
            max-width: 800px;
            margin: 3rem auto;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            color: white;
        }

        .highlight-box {
            background-color: #e0ffe0;
            padding: 1rem;
            border-radius: 10px;
            text-align: center;
            font-size: 1.4rem;
            font-weight: bold;
            color: #2e7d32;
            margin-top: 1.5rem;
        }
    </style>

    {{-- new table  --}}

    <style>
        .Features {
            background-color: rgb(63, 22, 81);
            min-width: 284px;
            min-height: 622.188px;
        }

        .features-box {
            background-color: #E13362;
        }

        .price {
            background-color: #E13362;
        }

        .price:hover {
            background-color: #3F1651;
        }

        .boxes {
            flex: 0 0 auto;
            width: 20%;
            transition: box-shadow 0.3s ease;
            min-width: 181.59px;
        }

        .inner-box {
            border: #f8f9fa;
        }

        .most-popular-blue {
            border-radius: 10px 10px 0px 0px;
            background: #3F1651;
            color: white;
            width: 100%;
            border: 1px solid #1273EB;
            position: absolute;
            z-index: 0;
            margin-top: -60px;
            left: 23px;
        }

        .boxes:hover {
            box-shadow: 0 0.5rem 1rem #EEC7FF
        }

        @media (max-width: 1200px) {
            .custom-scroll-container {
                overflow-x: auto;
                -webkit-overflow-scrolling: touch;
            }

            .custom-scroll-container .d-flex {
                flex-wrap: nowrap;
            }
        }
    </style>
@endsection

@section('webpage')
    {{-- table srction  --}}
    {{-- <section class="container-fluid bg-light py-5">
        <div class="container-lg px-0 pt-3 mt-5 ">
            <h2 class="androsms-h2 text-center fw-bold pt-5 mb-5"> Plans That Fit Every Budget</h2>
            <div class="table-responsive">
                <table class="comparison-table table bg-white table-striped fs-5">
                    <thead>
                        <tr>
                            <th class="fs-5 ">Features</th>
                            <th class="fs-5 p-1">Transaction SMS</th>
                            <th class="fs-5 p-1">Promotional SMS</th>
                            <th class="highlight-header fs-5 p-1">Google RCS API</th>
                            <th class="highlight-header fs-5 p-1 text-nowrap">AndroSMS Desktop</th>
                            <th class="highlight-header fs-5 p-1">AndroSMS Cloud</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>Per Message Cost</td>
                            <td>Credit Based</td>
                            <td>Credit Based</td>
                            <td class="">Credit Based</td>
                            <td class="">Free</td>
                            <td class="">Free</td>
                        </tr>
                        <tr>
                            <td class="text-nowrap">Template Approval Required</td>
                            <td>Required</td>
                            <td>Required</td>
                            <td class="">Required</td>
                            <td class="">Not Required</td>
                            <td class="">Not Required</td>
                        </tr>
                        <tr>
                            <td>Sender ID</td>
                            <td>6 Digit</td>
                            <td>6 Digit</td>
                            <td class=" text-nowrap">Brand Name & Number</td>
                            <td class="">Your Number</td>
                            <td class="">Your Number</td>
                        </tr>
                        <tr>
                            <td class="text-nowrap">Button / Interactive Messages</td>
                            <td><i class="fas fa-times text-danger"></i></td>
                            <td><i class="fas fa-times text-danger"></i></td>
                            <td class=""><i class="fas fa-check text-success"></i></td>
                            <td><i class="fas fa-times text-danger"></i></td>
                            <td><i class="fas fa-times text-danger"></i></td>
                        </tr>
                        <tr>
                            <td>DLT Registration</td>
                            <td>Required</td>
                            <td>Required</td>
                            <td>Required</td>
                            <td class="">Not Required</td>
                            <td class="">Not Required</td>
                        </tr>

                        <tr>
                            <td>Campaign Management</td>
                            <td><i class="fas fa-check text-success"></i></td>
                            <td><i class="fas fa-check text-success"></i></td>
                            <td><i class="fas fa-check text-success"></i></td>
                            <td><i class="fas fa-check text-success"></i></td>
                            <td class=""><i class="fas fa-check text-success"></i></td>
                        </tr>
                        <tr>
                            <td>Delivery Reports</td>
                            <td><i class="fas fa-check text-success"></i></td>
                            <td><i class="fas fa-check text-success"></i></td>
                            <td><i class="fas fa-check text-success"></i></td>
                            <td><i class="fas fa-check text-success"></i></td>
                            <td><i class="fas fa-check text-success"></i></td>
                        </tr>
                        <tr>
                            <td>Auto Replies</td>
                            <td><i class="fas fa-times text-danger"></i></td>
                            <td><i class="fas fa-times text-danger"></i></td>
                            <td><i class="fas fa-check text-success"></i></td>
                            <td><i class="fas fa-check text-success"></i></td>
                            <td><i class="fas fa-check text-success"></i></td>
                        </tr>
                        <tr>
                            <td>API Integration</td>
                            <td><i class="fas fa-check text-success"></i></td>
                            <td><i class="fas fa-check text-success"></i></td>
                            <td><i class="fas fa-check text-success"></i></td>
                            <td><i class="fas fa-times text-danger"></i></td>
                            <td><i class="fas fa-check text-success"></i></td>
                        </tr>
                        @php
                            $getstartedTarget = Auth::check() ? route('andro.pricing') : route('login');
                        @endphp

                        <tr>
                            <td>Price</td>
                            <td class="fw-medium text-nowrap">
                                <a aria-label="link"
                                    class="text-decoration-none pink-btn text-white rounded-pill border-0 d-inline-flex align-items-center py-2 px-4 gap-2"
                                    href="{{ $getstartedTarget }}">
                                    See Price
                                </a>
                            </td>
                            <td class="fw-medium text-nowrap"><a aria-label="link"
                                    class="text-decoration-none pink-btn text-white rounded-pill border-0 d-inline-flex align-items-center py-2 px-4 gap-2"
                                    href="{{ $getstartedTarget }}">
                                    See Price
                                </a></td>
                            <td class="fw-medium text-nowrap"><a aria-label="link"
                                    class="text-decoration-none pink-btn text-white rounded-pill border-0 d-inline-flex align-items-center py-2 px-4 gap-2"
                                    href="{{ $getstartedTarget }}">
                                    See Price
                                </a></td>
                            <td class="fw-medium text-nowrap"><a aria-label="link"
                                    class="text-decoration-none pink-btn text-white rounded-pill border-0 d-inline-flex align-items-center py-2 px-4 gap-2"
                                    href="{{ $getstartedTarget }}">
                                    See Price
                                </a></td>
                            <td class="fw-medium text-nowrap"><a aria-label="link"
                                    class="text-decoration-none pink-btn text-white rounded-pill border-0 d-inline-flex align-items-center py-2 px-4 gap-2"
                                    href="{{ $getstartedTarget }}">
                                    See Price
                                </a></td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </section> --}}
    <div class="py-5 mt-5" style="background-color: #f8f9fa">
        <div class="container-fluid text-center mt-5 mb-4 ">
            <h1 class="fw-bold fs-1">Plans That Fit Every Budget</h1>
        </div>

        <div class="custom-scroll-container pb-4 pt-1">
            <div class="d-flex flex-row container g-5 lg:g-8 mt-5">
                <div class="box mx-3">
                    <div class="bg-[#3F1651] Features h-100 py-4 px-3 rounded-3">
                        <div class="features-box w-100 h-auto p-2 rounded-2 mt-1 mb-4">
                            <h3 class="text-white text-start">Features</h3>
                        </div>
                        <ul class="text-white text-start list-unstyled px-1">
                            <li class="py-3 fw-medium">Per Message Cost</li>
                            <li class="py-3 fw-medium">Template Approval Required</li>
                            <li class="py-3 fw-medium">Sender ID</li>
                            <li class="py-3 fw-medium">Button / Interactive Messages</li>
                            <li class="py-3 fw-medium">DLT Registration</li>
                            <li class="py-3 fw-medium">Campaign Management</li>
                            <li class="py-3 fw-medium">Delivery Reports</li>
                            <li class="py-3 fw-medium">Auto Replies</li>
                            <li class="py-3 fw-medium">API Integration</li>
                        </ul>
                    </div>
                </div>

                <div class="box">
                    <div class="d-flex flex-row">
                        <div class="boxes rounded-3 mx-1 bg-white py-3">
                            <div class="inner-box border-bottom text-center pb-3 mb-2">
                                <h6 class="py-1 mb-0 fw-bold">Transaction SMS</h6>

                                @if (auth()->check())
                                    <p class="fs-3 fw-semibold lh-sm flex flex-column mb-0">₹ 800 <span
                                            class="fs-6 fw-normal">per Month</span> </p>
                                @else
                                    <a href="{{ route('login') }}"
                                        class="btn price px-4 py-1 rounded-5 d-inline-block mx-auto mt-4 text-white">
                                        See Price
                                    </a>
                                @endif

                            </div>
                            <ul class="text-center list-unstyled">
                                <li class="pb-3">Credit Based</li>
                                <li class="py-3">Required</li>
                                <li class="py-3">6 Digit</li>
                                <li class="py-3 text-danger fs-5 fa-solid fa-xmark"></li>
                                <li class="py-3">Required</li>
                                <li class="py-3 text-success">
                                    <i class="fs-4 fa-solid fa-check"></i>
                                </li>
                                <li class="py-3 text-success">
                                    <i class="fs-4 fa-solid fa-check"></i>
                                </li>
                                <li class="py-3 text-danger">
                                    <i class="fs-4 fa-solid fa-xmark"></i>
                                </li>
                                <li class="py-3 text-success">
                                    <i class="fs-4 fa-solid fa-check"></i>
                                </li>
                                @if (auth()->check())
                                    <li class="py-3">
                                        <a href="{{ route('cart.add', ['product_id' => $product_id]) }}"
                                            class="text-decoration-none rounded-5 py-2 px-4  price-items-2-btn mb-3">Add to
                                            cart</a>
                                    </li>
                                @endif
                            </ul>
                        </div>

                        <div class="boxes rounded-3 mx-1 bg-white py-3">
                            <div class="inner-box border-bottom text-center pb-3 mb-2">
                                <h6 class="py-1 mb-0 fw-bold">Promotional SMS</h6>
                                @if (auth()->check())
                                    <p class="fs-3 fw-semibold lh-sm flex flex-column mb-0">₹ 800 <span
                                            class="fs-6 fw-normal">per Month</span> </p>
                                @else
                                    <a href="{{ route('login') }}"
                                        class="btn price px-4 py-1 rounded-5 d-inline-block mx-auto mt-4 text-white">
                                        See Price
                                    </a>
                                @endif
                            </div>
                            <ul class="text-center list-unstyled">
                                <li class="pb-3">Credit Based</li>
                                <li class="py-3">Required</li>
                                <li class="py-3">6 Digit</li>
                                <li class="py-3 text-danger fs-5 fa-solid fa-xmark"></li>
                                <li class="py-3">Required</li>
                                <li class="py-3 text-success">
                                    <i class="fs-4 fa-solid fa-check"></i>
                                </li>
                                <li class="py-3 text-success">
                                    <i class="fs-4 fa-solid fa-check"></i>
                                </li>
                                <li class="py-3 text-danger">
                                    <i class="fs-4 fa-solid fa-xmark"></i>
                                </li>
                                <li class="py-3 text-success">
                                    <i class="fs-4 fa-solid fa-check"></i>
                                </li>
                                @if (auth()->check())
                                    <li class="py-3">
                                        <a href="{{ route('cart.add', ['product_id' => $product_id]) }}"
                                            class="text-decoration-none rounded-5 py-2 px-4 price-items-2-btn mb-3">Add to
                                            cart</a>
                                    </li>
                                @endif
                            </ul>
                        </div>

                        <div class="boxes rounded-3 mx-1 bg-white py-3">
                            <div class="inner-box border-bottom text-center pb-3 mb-2">
                                <h6 class="py-1 mb-0 fw-bold">Google RCS API</h6>
                                @if (auth()->check())
                                    <p class="fs-3 fw-semibold lh-sm flex flex-column mb-0">₹ 800 <span
                                            class="fs-6 fw-normal">per Month</span> </p>
                                @else
                                    <a href="{{ route('login') }}"
                                        class="btn price px-4 py-1 rounded-5 d-inline-block mx-auto mt-4 text-white">
                                        See Price
                                    </a>
                                @endif
                            </div>
                            <ul class="text-center list-unstyled">
                                <li class="pb-3">Credit Based</li>
                                <li class="py-3">Required</li>
                                <li class="py-3">Brand Name & Number</li>
                                <li class="pb-3 pt-2 text-success">
                                    <i class="fs-4 fa-solid fa-check"></i>
                                </li>
                                <li class="py-3">Required</li>
                                <li class="py-3 text-success">
                                    <i class="fs-4 fa-solid fa-check"></i>
                                </li>
                                <li class="py-3 text-success">
                                    <i class="fs-4 fa-solid fa-check"></i>
                                </li>
                                <li class="py-3 text-success">
                                    <i class="fs-4 fa-solid fa-check"></i>
                                </li>
                                <li class="pt-3 text-success">
                                    <i class="fs-4 fa-solid fa-check"></i>
                                </li>
                                @if (auth()->check())
                                    <li class="py-3 my-3">
                                        <a href="{{ route('cart.add', ['product_id' => $product_id]) }}"
                                            class="text-decoration-none rounded-5 py-2 px-4 price-items-2-btn mb-3">Add to
                                            cart</a>
                                    </li>
                                @endif
                            </ul>
                        </div>

                        <div class="boxes rounded-3 mx-1 bg-white py-3 position-relative">
                            <div class="most-popular-blue pt-1 pb-2 pe-4 ps-4 mb-3 text-center fs-5 fw-semibold">
                                Best Seller
                            </div>
                            <div class="inner-box border-bottom pb-3 mb-2 text-center">
                                <h6 class="py-1 mb-0 fw-bold">AndroSMS Desktop</h6>
                                @if (auth()->check())
                                    <p class="fs-3 fw-semibold lh-sm flex flex-column mb-0">₹ 800 <span
                                            class="fs-6 fw-normal">per Month</span> </p>
                                @else
                                    <a href="{{ route('login') }}"
                                        class="btn price px-4 py-1 rounded-5 d-inline-block mx-auto mt-4 text-white">
                                        See Price
                                    </a>
                                @endif
                            </div>
                            <ul class="text-center list-unstyled">
                                <li class="pb-3">Free</li>
                                <li class="py-3">Not Required</li>
                                <li class="py-3">Your Number</li>
                                <li class="py-3 text-danger fs-5 fa-solid fa-xmark"></li>
                                <li class="py-3">Not Required</li>
                                <li class="py-3 text-success">
                                    <i class="fs-4 fa-solid fa-check"></i>
                                </li>
                                <li class="py-3 text-success">
                                    <i class="fs-4 fa-solid fa-check"></i>
                                </li>
                                <li class="py-3 text-success">
                                    <i class="fs-4 fa-solid fa-check"></i>
                                </li>
                                <li class="pt-3 text-danger">
                                    <i class="fs-4 fa-solid fa-xmark"></i>
                                </li>
                                @if (auth()->check())
                                    <li class="py-3 my-3">
                                        <a href="{{ route('cart.add', ['product_id' => $product_id]) }}"
                                            class="text-decoration-none rounded-5 py-2 px-4 price-items-2-btn mb-3">Add to
                                            cart</a>
                                    </li>
                                @endif
                            </ul>
                        </div>

                        <div class="boxes rounded-3 mx-1 bg-white py-3 table-hover">
                            <div class="inner-box border-bottom text-center pb-3 mb-2">
                                <h6 class="text-center py-1 mb-0 fw-bold">AndroSMS Cloud</h6>
                                @if (auth()->check())
                                    <p class="fs-3 fw-semibold lh-sm flex flex-column mb-0">₹ 800 <span
                                            class="fs-6 fw-normal">per Month</span> </p>
                                @else
                                    <a href="{{ route('login') }}"
                                        class="btn price px-4 py-1 rounded-5 d-inline-block mx-auto mt-4 text-white">
                                        See Price
                                    </a>
                                @endif
                            </div>
                            <ul class="text-center list-unstyled">
                                <li class="pb-3">Free</li>
                                <li class="py-3">Not Required</li>
                                <li class="py-3">Your Number</li>
                                <li class="py-3 text-danger fs-5 fa-solid fa-xmark"></li>
                                <li class="py-3">Not Required</li>
                                <li class="py-3 text-success">
                                    <i class="fs-4 fa-solid fa-check"></i>
                                </li>
                                <li class="py-3 text-success">
                                    <i class="fs-4 fa-solid fa-check"></i>
                                </li>
                                <li class="py-3 text-success">
                                    <i class="fs-4 fa-solid fa-check"></i>
                                </li>
                                <li class="pt-3 text-success">
                                    <i class="fs-4 fa-solid fa-check"></i>
                                </li>
                                @if (auth()->check())
                                    <li class="py-3 my-3">
                                        <a href="{{ route('cart.add', ['product_id' => $product_id]) }}"
                                            class="text-decoration-none rounded-5 py-2 px-4 price-items-2-btn mb-3">Add to
                                            cart</a>
                                    </li>
                                @endif
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    {{-- Our All Plans Include Section --}}
    <section class="container-fluid pt-3 pt-md-4 pt-lg-5 my-3 my-md-4 my-lg-5">
        <div class="container-lg px-3 px-md-4 py-3 py-md-4 py-lg-5">
            <h2 class="text-center mb-2 mb-md-3 mb-lg-4 androsms-h2 fw-bold">Our All Plans Include</h2>
            <div class="row row-cols-1 row-cols-md-2 row-cols-lg-3 g-3 g-md-4">
                <!-- Left Column - Bulk SMS Marketing -->
                <div class="col">
                    <div class="h-100 p-3 p-md-4  rounded-3 ">
                        <h3 class="fw-bold mb-3 mb-md-4 text-primary fs-4 ">Easy Messaging & Smart Scheduling</h3>
                        <ul class="list-unstyled mb-0">
                            <li class="mb-2 d-flex align-items-start">
                                <i class="fas fa-check-circle text-success me-2  mt-2"></i>
                                <span class="fs-5">Unlimited Messages</span>
                            </li>
                            <li class="mb-2 d-flex align-items-start">
                                <i class="fas fa-check-circle text-success me-2 mt-2"></i>
                                <span class="fs-5">Texting Campaigns</span>
                            </li>
                            <li class="mb-2 d-flex align-items-start">
                                <i class="fas fa-check-circle text-success me-2 mt-2"></i>
                                <span class="fs-5">Personalized Message</span>
                            </li>
                            <li class="mb-2 d-flex align-items-start">
                                <i class="fas fa-check-circle text-success me-2 mt-2"></i>
                                <span class="fs-5">Media Messaging</span>
                            </li>
                            <li class="mb-0 d-flex align-items-start">
                                <i class="fas fa-check-circle text-success me-2 mt-2"></i>
                                <span class="fs-5">Schedule & Draft Campaigns</span>
                            </li>
                        </ul>
                    </div>
                </div>

                <!-- Middle Column - Campaign Management -->
                <div class="col">
                    <div class="h-100 p-3 p-md-4  rounded-3 ">
                        <h3 class="fw-bold mb-3 mb-md-4 text-primary ">Campaign Management</h3>
                        <ul class="list-unstyled mb-0">
                            <li class="mb-2 d-flex align-items-start">
                                <i class="fas fa-check-circle text-success me-2 mt-2"></i>
                                <span class="fs-5">Unlimited Contacts</span>
                            </li>
                            <li class="mb-2 d-flex align-items-start">
                                <i class="fas fa-check-circle text-success me-2 mt-2"></i>
                                <span class="fs-5">List Uploads</span>
                            </li>
                            <li class="mb-2 d-flex align-items-start">
                                <i class="fas fa-check-circle text-success me-2 mt-2"></i>
                                <span class="fs-5">Real-Time Campaign Status & Progress Bar</span>
                            </li>
                            <li class="mb-2 d-flex align-items-start">
                                <i class="fas fa-check-circle text-success me-2 mt-2"></i>
                                <span class="fs-5">QR code scanner</span>
                            </li>
                            <li class="mb-0 d-flex align-items-start">
                                <i class="fas fa-check-circle text-success me-2 mt-2"></i>
                                <span class="fs-5">Bulk Uploads via CSV, Excel, etc.</span>
                            </li>
                        </ul>
                    </div>
                </div>

                <!-- Right Column - Productivity Boosts -->
                <div class="col">
                    <div class="h-100 p-3 p-md-4  rounded-3 ">
                        <h3 class="fw-bold mb-3 mb-md-4 text-primary ">Productivity Boosts</h3>
                        <ul class="list-unstyled mb-0">
                            <li class="mb-2 d-flex align-items-start">
                                <i class="fas fa-check-circle text-success me-2 mt-2"></i>
                                <span class="fs-5">Automated Workflows</span>
                            </li>
                            <li class="mb-2 d-flex align-items-start">
                                <i class="fas fa-check-circle text-success me-2 mt-2"></i>
                                <span class="fs-5">Live Support via Phone, Chat, or Email</span>
                            </li>
                            <li class="mb-2 d-flex align-items-start">
                                <i class="fas fa-check-circle text-success me-2 mt-2"></i>
                                <span class="fs-5">Advanced Analytics</span>
                            </li>
                            <li class="mb-2 d-flex align-items-start">
                                <i class="fas fa-check-circle text-success me-2 mt-2"></i>
                                <span class="fs-5">Dedicated Customer Success Manager</span>
                            </li>
                            <li class="mb-0 d-flex align-items-start">
                                <i class="fas fa-check-circle text-success me-2 mt-2"></i>
                                <span class="fs-5">Training & Onboarding Sessions</span>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </section>

    {{-- demo now section  --}}
    <div class="container-fluid my-4 py-4">
        <div class="container-lg ">
            <div class=" features-box rounded-4 p-3 w-90 m-auto">
                <div class="row align-items-center">
                    <!-- Icon and Content -->
                    <div class="col-lg-10 col-md-9">
                        <div class="d-flex align-items-start">
                            <div class="bg-white mt-2 bg-opacity-25 rounded-circle d-flex align-items-center justify-content-center me-3 flex-shrink-0"
                                style="width: 48px; height: 48px;">
                                <i class="bi bi-chat-dots fs-4  text-white"></i>
                            </div>
                            <div class="mb-3 mb-md-0">
                                <h3 class="text-white fw-bold fs-4 mb-0">Still need help deciding?</h3>
                                <p class="text-white fs-5 mb-0">
                                    Need a personalized solution for your business? Reach out for a custom-built
                                    quote.
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- Button -->
                    <div class="col-lg-2 col-md-3 text-end">
                        <a href="{{ route('andro.contact') }}" class="text-decoration-none btn btn-light text-dark fw-medium px-4 py-2 rounded-pill text-nowrap">
                                Book Demo Now
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    {{-- calculator section  --}}
    <div class="container-fluid my-5 pt-4">
        <div class="container-lg">
            <h2 class="androsms-h2 fw-bold text-center mb-3">Discover Your Annual Savings, with AndroSMS</h2>
            <p class="text-center fs-5">Estimate how much you'd save yearly by using <strong>AndroSMS</strong> instead of
                traditional SMS charges at
                ₹0.20/message.</p>

            <div class="calculator">
                <h2 class="text-center mb-4">💬 SMS Cost Saving Calculator</h2>
                <p class="text-center">
                    <strong>1 Device = 2 SIMs | 1 SIM = 100 Free SMS/day</strong>
                </p>

                <div class="mb-4">
                    <label for="deviceCount" class="form-label">Number of Devices</label>
                    <input type="number" class="form-control" id="deviceCount" min="1" value="10">
                </div>

                <div class="row g-3">
                    <div class="col-md-6">
                        <label class="form-label">SIM Cards (2 per Device)</label>
                        <input type="text" id="simCards" class="form-control" readonly>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">Daily Free SMS (100 per SIM)</label>
                        <input type="text" id="dailySms" class="form-control" readonly>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">Monthly Free SMS</label>
                        <input type="text" id="monthlySms" class="form-control" readonly>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">Yearly Free SMS</label>
                        <input type="text" id="yearlySms" class="form-control" readonly>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">Traditional Monthly SMS Cost (₹0.20/SMS)</label>
                        <input type="text" id="monthlyCost" class="form-control" readonly>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">Traditional Yearly SMS Cost (₹0.20/SMS)</label>
                        <input type="text" id="yearlyCost" class="form-control" readonly>
                    </div>
                </div>

                <div class="highlight-box" id="savingsOutput">
                    You Save ₹0 Every Year with AndroSMS
                </div>
            </div>
        </div>
    </div>





    {{-- Pricing-Section End --}}
    @php
        $faqs = [
            [
                'question' => 'Does your software support both SMS and RCS campaigns?',
                'answer' =>
                    'Absolutely. You can create, manage, and automate both SMS and RCS campaigns from a single unified platform.',
            ],
            [
                'question' => 'Is there a limit to how many messages I can send through the platform?',
                'answer' => 'No platform-imposed limits.',
            ],
            [
                'question' => ' Is there a trial or demo version available?',
                'answer' => 'Yes, we offer live demo so you can explore the platform before committing to a plan.',
            ],
            [
                'question' => ' Do I need to pay for updates or support?',
                'answer' =>
                    'All software updates are included in your subscription. Support is also included, with priority assistance for all our customers.',
            ],
        ];
    @endphp
    <section class="container-fluid py-4 w-90 rounded-5 mb-2">
        <div class="container-lg ">
            <h3 class="fw-bold text-center androsms-h2 mb-4">Frequently Asked Questions</h3>
            <x-faq-accordion :faqs="$faqs" collapsed-color="#f9f9f9" active-color="#e13362" />
        </div>
    </section>
    @include('components.andro.earn')
@endsection

@section('andro-script')
    <script>
        // comparison table script
        document.addEventListener('DOMContentLoaded', function() {
            const table = document.querySelector('.comparison-table');
            const rows = table.querySelectorAll('tr');
            const hoverableColumns = [1, 2, 3, 4, 5]; // Only these columns are interactive

            function highlightColumn(colIndex) {
                if (!hoverableColumns.includes(colIndex)) return;

                rows.forEach((row, rowIndex) => {
                    const cells = row.querySelectorAll('th, td');
                    if (cells[colIndex]) {
                        cells[colIndex].classList.add('column-hover');
                    }

                    // Add class to the header (top cell of the column)
                    if (rowIndex === 0 && cells[colIndex]) {
                        cells[colIndex].classList.add('column-hover-top');
                    }

                    // If it's the last row and column is hoverable, add special border
                    if (rowIndex === rows.length - 1 && cells[colIndex]) {
                        cells[colIndex].classList.add('column-hover-last');
                    }
                });
            }

            function removeHighlight(colIndex) {
                if (!hoverableColumns.includes(colIndex)) return;

                rows.forEach((row, rowIndex) => {
                    const cells = row.querySelectorAll('th, td');
                    if (cells[colIndex]) {
                        cells[colIndex].classList.remove('column-hover');
                    }

                    // Remove from header
                    if (rowIndex === 0 && cells[colIndex]) {
                        cells[colIndex].classList.remove('column-hover-top');
                    }

                    if (rowIndex === rows.length - 1 && cells[colIndex]) {
                        cells[colIndex].classList.remove('column-hover-last');
                    }
                });
            }

            rows.forEach(row => {
                const cells = row.querySelectorAll('th, td');
                cells.forEach((cell, colIndex) => {
                    cell.addEventListener('mouseenter', () => highlightColumn(colIndex));
                    cell.addEventListener('mouseleave', () => removeHighlight(colIndex));
                });
            });
        });

        // calculator script
        const deviceInput = document.getElementById('deviceCount');
        const simOutput = document.getElementById('simCards');
        const dailyOutput = document.getElementById('dailySms');
        const monthlyOutput = document.getElementById('monthlySms');
        const yearlyOutput = document.getElementById('yearlySms');
        const monthlyCost = document.getElementById('monthlyCost');
        const yearlyCost = document.getElementById('yearlyCost');
        const savingsOutput = document.getElementById('savingsOutput');

        function updateSavings() {
            const devices = parseInt(deviceInput.value);
            const sims = devices * 2;
            const dailySms = sims * 100;
            const monthlySms = dailySms * 30;
            const yearlySms = dailySms * 365;
            const monthlyCharge = monthlySms * 0.2;
            const yearlyCharge = yearlySms * 0.2;

            simOutput.value = sims;
            dailyOutput.value = dailySms;
            monthlyOutput.value = monthlySms;
            yearlyOutput.value = yearlySms;
            monthlyCost.value = `₹${monthlyCharge.toLocaleString()}`;
            yearlyCost.value = `₹${yearlyCharge.toLocaleString()}`;
            savingsOutput.textContent = `You Save ₹${yearlyCharge.toLocaleString()} Every Year with AndroSMS`;
        }

        deviceInput.addEventListener('input', updateSavings);
        updateSavings();
    </script>
@endsection
