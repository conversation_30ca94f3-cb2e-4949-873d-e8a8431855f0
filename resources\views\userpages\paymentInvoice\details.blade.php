@extends('userpages.paymentInvoice.components.main')
@section('webpage')
    <div class="">
        <div class="row">
            <div class="col-lg-8  col-sm-12   col-md-12 ">
                <div class="mb-3">
                    <div class="bg-white mt-3"
                        style="box-shadow: 0px 4px 7.199999809265137px 0px #00000008; border-radius:7px;">
                        <div class="d-flex align-items-center gap-3 p-3"
                            style="background-color: #EAF1FF;border-radius:7px 7px 0 0;">
                            <span class="align-items-center justify-content-center badge d-flex rounded-circle text-white"
                                style="height: 30px;width: 30px;background-color:#194DAB;">1</span>
                            <h4 style="color: #194DAB;">Cart</h4>
                        </div>
                        @forelse ($invoice->items as $cart)
                            @if ($cart->productVariant)
                                <div class=" p-3">
                                    <div class="">
                                        <div class="h-100 border-0">
                                            <div class="d-flex align-items-center gap-4 ">
                                                <div class="" style="width: 70px;">
                                                    @if ($cart->productVariant && $cart->productVariant->product->images->count())
                                                        <img alt="product_img" class="img-fluid"
                                                            src="{{ s3_fileShow($cart->productVariant->product->images()->first()->image, 'product_gallery') }}">
                                                    @else
                                                        <img src="https://dummyimage.com/80/969696/ffffff.jpg&text={{ $cart->productVariant->name }}"
                                                            alt="..." class="img-fluid">
                                                    @endif
                                                </div>
                                                <div class="d-flex flex-column">
                                                    <h5 class="mb-0 fs-6">
                                                        {{ $cart->productVariant->product->name }}
                                                    </h5>
                                                    <div style="color: #626262" class="lh-1 fs-6">
                                                        {{ $cart->variant->name }}
                                                    </div>
                                                    <div>
                                                        <span class="fw-semibold">
                                                            Quantity :
                                                        </span>
                                                        <span style="color: #626262">
                                                            {{ $cart->quantity }}
                                                        </span>
                                                    </div>
                                                    <div class="d-flex align-items-center gap-2  fs-6">

                                                        @if ($cart->rate > $cart->mrp)
                                                            <h5 class="fw-bold mb-0 fs-6">
                                                                ₹ {{ $cart->rate ?? '' }}
                                                                {{-- <span class="fs-6">1 Year Incl. Tax</span> --}}
                                                            </h5>
                                                        @else
                                                            <del style="color: #626262">
                                                                ₹{{ $cart->mrp ?? '' }}
                                                            </del>
                                                            <h5 class="fw-bold mb-0 fs-6">
                                                                ₹ {{ $cart->rate ?? '' }}
                                                                {{-- <span class="fs-6">1 Year Incl. Tax</span> --}}
                                                            </h5>
                                                        @endif
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <hr class="m-0">
                            @endif
                        @empty
                            <div class="m-auto text-center p-4">
                                <div class="w-lg-25 w-sm-100 text-center m-auto">
                                    <img src="{{ asset('media/images/catbox.png') }}" alt="" class="img-fluid">
                                </div>
                                <h2 class="f-2" style="color: #df5959">Empty Bag !</h2>

                                <div class="text-end p-3">
                                    <a aria-label="link" href="{{ route('login') }}"
                                        class="btn p-2 ps-3 pe-3 rounded-pill text-white"
                                        style="background-color: #194DAB;">
                                        View cart
                                        <i class="fa-solid fa-chevron-right"></i>
                                    </a>
                                </div>
                            </div>
                        @endforelse
                        @if ($invoice->items)
                            <div class="text-end p-3">
                                <a aria-label="link" href="{{ route('pi.address', ['invoice' => $invoice->id]) }}"
                                    class="btn p-2 ps-3 pe-3 rounded-pill text-white"
                                    style="background-color: #194DAB;">Continue
                                    <i class="fa-solid fa-chevron-right"></i></a>
                            </div>
                        @endif
                    </div>
                </div>


            </div>
            <div class="col-lg-4  col-md-12 col-sm-12">
                @include('userpages.cart.cartCalculations')

            </div>
        </div>
    </div>
@endsection
