<?php

namespace App\Models;

use App\Models\Product;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Cart extends Model
{
    use HasFactory;
    protected $table = "cart";
    public $timestamps = false;

    protected  $fillable = ['lead_id',  'product_variant_id', 'quantity'];

    public function product()
    {
        return $this->belongsTo(Product::class, 'product_id', 'id')->with('images', 'addons', 'category');
    }
    public function CartProductAddons()
    {
        return $this->hasMany(Cart::class, 'parentCart_id', 'id')->with('product');
    }
   
}
