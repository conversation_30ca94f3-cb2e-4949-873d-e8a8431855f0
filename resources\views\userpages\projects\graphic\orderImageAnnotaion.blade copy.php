<!DOCTYPE html>
<html>

<head>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@annotorious/annotorious@latest/dist/annotorious.css">
    <script src="https://cdn.jsdelivr.net/npm/@annotorious/annotorious@latest/dist/annotorious.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <style>
        /* Minimal essential styles - using Bootstrap where possible */
        .annotation-comment {
            position: absolute;
            z-index: 1000;
            pointer-events: auto;
            transform: translateX(-50%);
            max-width: 280px;
        }

        .annotation-comment::before {
            content: '';
            position: absolute;
            top: -8px;
            left: 50%;
            transform: translateX(-50%);
            width: 0;
            height: 0;
            border-left: 8px solid transparent;
            border-right: 8px solid transparent;
            border-bottom: 8px solid rgba(0, 0, 0, 0.9);
        }

        .comment-actions {
            display: none;
            position: absolute;
            top: -30px;
            right: 0;
            z-index: 1002;
        }

        .annotation-comment:hover .comment-actions {
            display: flex;
        }

        .comment-edit-area {
            display: none;
            position: absolute;
            top: 100%;
            left: 50%;
            transform: translateX(-50%);
            z-index: 1001;
            min-width: 200px;
        }

        .image-container {
            position: relative;
        }
    </style>
</head>

<body>
    <div class="container-lg p-3 bg-secondary-subtle">
        <div class="row">
            <div class="col-sm-11 col-md-8 col-lg-8">
                <div class="d-flex h-100 flex-column justify-content-between">
                    <img id="my-image" src="https://images.pexels.com/photos/31241763/pexels-photo-31241763.jpeg"
                        alt="Annotatable image" class="img-fluid" />
                    {{-- <img id="my-image" src="{{ $previewImage }}" alt="Annotatable image" class="img-fluid" /> --}}
                    <div class="mt-3">
                        <button class="btn btn-outline-danger btn-sm" onclick="clearAllComments()">
                            <i class="fas fa-trash-alt me-2"></i>Clear All Annotations
                        </button>
                    </div>
                </div>
            </div>
            <div class="col-sm-11  col-md-4 col-lg-4">
                <div class="card w-100 h-100 border-0  bg-wite " style="min-height: 80vh;">
                    <h5 class="card-header border-0 bg-white">
                        Comments
                    </h5>
                    <div class="card-body  overflow-auto" id="modelCommentBox">
                        <div class="fs-5 text-secondary">
                            Add your comment here...
                        </div>
                        @foreach ($orderItem->comments as $comment)
                            @php
                                if ($comment->byUser) {
                                    $commentBy = $comment->byUser;
                                } else {
                                    $commentBy = $comment->byLead;
                                }
                            @endphp
                            <div class="p-1 border-top" id="commentId${id}">

                                <div class="p-2">
                                    <div class="d-flex flex-start align-items-center justify-content-between">
                                        <h6 class="fw-semibold  mb-0 "> {{ $commentBy->name }}</h6>
                                        <p class="text-muted small mb-0 " style="font-size: 12px;">
                                            <i class="bi bi-clock me-1"></i>{{ $comment->datetime }}
                                        </p>
                                    </div>
                                    <p class=" m-0 text-start" style=" font-size: 14px;">{{ $comment->comment }}</p>
                                </div>
                            </div>
                        @endforeach

                    </div>
                    <div class="card-footer bg-white border-0">
                        <div class="d-flex flex-start w-100">

                            <div class="w-100 form-floating">
                                <textarea class="form-control" id="modeltextarea" rows="3" placeholder="Add a comment..."></textarea>
                                <label for="floatingInputInvalid">Add a comment...</label>
                            </div>
                        </div>
                        <div class="float-end mt-2 pt-1 d-flex">
                            <div id="storeCommentErr"></div>
                            <button type="button" id="storeComment" class="btn btn-primary btn-sm">Post
                                comment</button>
                        </div>
                    </div>

                </div>

            </div>
        </div>
    </div>

    <script>
        $('#storeComment').on('click', function() {
            const modelTextareaValue = $('#modeltextarea').val();
            if (modelTextareaValue.length > 0) {
                commentStore({
                    comment: modelTextareaValue
                })
            } else {
                $('#storeCommentErr').text('Empty Comment.')
            }
        });

        function deleteComment(id) {
            fetch(`/annotations/comment/${id}`, {
                    method: 'DELETE',
                    headers: {
                        'X-CSRF-TOKEN': '{{ csrf_token() }}'
                    }
                })

                .then(res => res.json())
                .then(data => {
                    $(`#commentId${id}`).remove()
                });
        }

        function commentStore(data) {
            // const imageData = $('#modalImage').data();
            // data.item_id = imageData.imageId;
            data.graphic_design_order_item_id = "{{ $orderItem->id }}";

            fetch('/annotations', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': '{{ csrf_token() }}'
                    },
                    body: JSON.stringify(data)
                })
                .then(res => res.json())
                .then(data => {
                    console.log('cmt stored', data);
                    let commentObj = data.annotation
                    console.log(' commentObj', commentObj);


                    if (data.status && commentObj.comment) {

                        let commentStr = commentPreviewBox(commentObj.id, "{{ auth()->user()->name }}", getTimeAgo(
                                commentObj.datetime),
                            commentObj.comment)
                        $('#modelCommentBox').append(commentStr);
                        $("#modelCommentBox").animate({
                            scrollTop: $('#modelCommentBox').get(0).scrollHeight
                        }, 1000);
                        $('#modeltextarea').val('')
                    }
                });
        }

        function commentPreviewBox(id, commentBy, datetime, comment) {
            return `<div class="p-1 border-top" id="commentId${id}">
                        <div class="p-2">
                            <div class="d-flex flex-start align-items-center justify-content-between">                                                    
                                <h6 class="fw-semibold  mb-0 "> ${commentBy}</h6>
                                <p class="text-muted small mb-0 "  style="font-size: 12px;">
                                <i class="bi bi-clock me-1"></i>${datetime}
                                </p>
                            </div>
                            <p class=" m-0 text-start" style=" font-size: 14px;">${comment}</p>
                        </div>
                    </div>`
        }
    </script>


    <script>
        // Annotation with integrated comment management
        function getCommentFromStorage(annotationId) {
            let annotations = JSON.parse(localStorage.getItem('annotorious-annotations') || '[]');

            // Handle both old and new integrated structure
            const annotation = annotations.find(a =>
                a.annotation_id === annotationId ||
                a.annotationId === annotationId ||
                a.id === annotationId
            );

            if (annotation && annotation.comment) {
                return {
                    text: annotation.comment, // Use 'text' for backward compatibility
                    comment: annotation.comment,
                    user: annotation.user_name || annotation.annotationJson?.target?.creator?.name ||
                        '{{ auth()->user()->name }}',
                    timestamp: annotation.timestamp || annotation.datetime || new Date().toISOString(),
                    id: annotation.annotation_id || annotation.annotationId || annotation.id
                };
            }
            return null;
        }

        // Helper function to format time ago
        function getTimeAgo(timestamp) {
            if (!timestamp) return 'Unknown time';

            const now = new Date();
            const commentTime = new Date(timestamp);
            const diffInSeconds = Math.floor((now - commentTime) / 1000);

            if (diffInSeconds < 60) return 'Just now';
            if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;
            if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;
            if (diffInSeconds < 2592000) return `${Math.floor(diffInSeconds / 86400)}d ago`;

            return commentTime.toLocaleDateString();
        }

        function saveAnnotationToStorage(annotation, commentText = '', userName = 'sourabh') {
            const integratedAnnotation = {
                graphic_design_order_item_id: 1, // Replace with actual item ID
                datetime: new Date().toISOString(),
                comment: commentText,
                user_id: 123, // Replace with actual user ID
                lead_id: '{{ auth()->user()->id }}', // Replace with actual lead ID
                annotationId: annotation.id,
                annotationJson: annotation
            };
            fetch('/annotations', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify(integratedAnnotation)
                })
                .then(response => response.json())
                .then(data => {
                    let annotations = JSON.parse(localStorage.getItem('annotorious-annotations') || '[]');
                    annotations = annotations.filter(a => a.annotationId !== annotation.id);
                    annotations.push(integratedAnnotation);
                    localStorage.setItem('annotorious-annotations', JSON.stringify(annotations));
                })
                .catch(error => {
                    console.error('Error saving annotation with integrated comment:', error);
                    // Fallback to localStorage on error
                });
        }

        function loadAnnotationsFromStorage() {
            let annotations = JSON.parse(localStorage.getItem('annotorious-annotations') || '[]');
            return annotations.map(item => item.annotationJson || item);
        }

        function removeAnnotationFromStorage(annotationId) {

            fetch(`/annotations/${annotationId}`, {
                    method: 'DELETE',
                    headers: {
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                })
                .then(response => response.json())
                .then(data => {
                    let annotations = JSON.parse(localStorage.getItem('annotorious-annotations') || '[]');
                    annotations = annotations.filter(a => a.annotationId !== annotationId);
                    localStorage.setItem('annotorious-annotations', JSON.stringify(annotations));
                })
                .catch(error => {
                    console.error('Error deleting annotation with integrated comment:', error);
                    // Fallback to localStorage on error
                });
        }


        // Function to highlight annotation on the image
        function highlightAnnotationOnImage(annotationId) {
            if (!window.annotoriusInstance) return;

            const annotation = window.annotoriusInstance.getAnnotations().find(a => a.id === annotationId);
            if (annotation) {
                // First, clear any existing selections
                window.annotoriusInstance.clearSelection();

                // Select the annotation to highlight it
                window.annotoriusInstance.setSelected(annotation);

                // Add custom styling for enhanced visibility
                window.annotoriusInstance.setStyle((ann, state) => {
                    if (ann.id === annotationId && state.selected) {
                        return {
                            fill: '#ff6b6b',
                            fillOpacity: 0.3,
                            stroke: '#ff6b6b',
                            strokeWidth: 3,
                            strokeOpacity: 1
                        };
                    }
                    // Default style for other annotations
                    return {
                        fill: '#007bff',
                        fillOpacity: 0.2,
                        stroke: '#007bff',
                        strokeWidth: 2,
                        strokeOpacity: 0.8
                    };
                });

                console.log('Annotation highlighted on image:', annotationId);
            }
        }



        // Function to add persistent comment display under annotation
        function addAnnotationComment(annotation) {
            const annotationId = annotation.annotation_id || annotation.annotationId || annotation.id;

            let commentText = '';
            let commentData = null;

            if (annotation.comment) {
                commentText = annotation.comment;
                commentData = {
                    text: annotation.comment,
                    user: annotation.user_name || 'DemoUser',
                    timestamp: annotation.timestamp || new Date().toISOString()
                };
            } else {
                commentData = getCommentFromStorage(annotationId);
                commentText = commentData ? commentData.text : '';
            }

            if (!commentText || commentText.trim() === '') {
                return;
            }

            // Create a comment element
            const commentId = `comment-${annotationId}`;

            // Remove existing comment if any
            const existingComment = document.getElementById(commentId);
            if (existingComment) {
                existingComment.remove();
            }

            // Get annotation bounds and image element
            let bounds;
            if (annotation.annotation && annotation.annotation.target) {
                bounds = annotation.annotation.target.selector.geometry.bounds;
            } else if (annotation.target) {
                bounds = annotation.target.selector.geometry.bounds;
            } else if (annotation.annotationJson) {
                bounds = annotation.annotationJson.target.selector.geometry.bounds;
            } else {
                console.error('Could not find bounds for annotation:', annotation);
                return;
            }
            const imageElement = document.getElementById('my-image');

            // Get image dimensions and position
            const imageRect = imageElement.getBoundingClientRect();
            const imageContainer = imageElement.parentElement;

            // Calculate position relative to the container
            const scaleX = imageRect.width / imageElement.naturalWidth;
            const scaleY = imageRect.height / imageElement.naturalHeight;

            const commentX = (bounds.minX + (bounds.maxX - bounds.minX) / 2) * scaleX;
            const commentY = bounds.maxY * scaleY + 10; // 10px below the annotation

            // Create comment element
            const commentElement = document.createElement('div');
            commentElement.id = commentId;
            commentElement.className =
                'annotation-comment bg-dark text-white p-2 rounded shadow border border-primary small';
            commentElement.style.cssText = `
                left: ${commentX}px;
                top: ${commentY}px;
                display: none;
            `;

            // Get user info and format time
            const userName = commentData ? commentData.user || 'sourabh' : 'sourabh';
            const timeAgo = commentData ? getTimeAgo(commentData.timestamp) : 'Just now';

            // Create comment content structure
            commentElement.innerHTML = `
                <div class="d-flex align-items-center mb-1 opacity-75">
                    <span class="fw-bold me-2">${userName}</span>
                    <span class="small opacity-75">${timeAgo}</span>
                </div>
                <div class="bg-white text-dark p-1 rounded small">${commentText.trim() || 'No comment'}</div>
                
                <div class="comment-edit-area bg-white border border-primary rounded p-2 shadow" id="edit-area-${annotationId}">
                    <textarea class="form-control form-control-sm mb-2" id="edit-input-${annotationId}" placeholder="Edit comment..."
                              onkeydown="handleEditKeydown(event, '${annotationId}')" rows="3">${commentText}</textarea>
                    <div class="d-flex gap-2 justify-content-end">
                        <button class="btn btn-success btn-sm" onclick="saveAnnotationComment('${annotationId}')">
                            <i class="fas fa-save me-1"></i>Save
                        </button>
                        <button class="btn btn-secondary btn-sm" onclick="cancelEditAnnotationComment('${annotationId}')">
                            <i class="fas fa-times me-1"></i>Cancel
                        </button>
                    </div>
                </div>
            `;



            // Add hover event listeners to keep comment visible when hovering over it
            commentElement.addEventListener('mouseenter', function() {
                commentElement.style.display = 'block';
            });

            commentElement.addEventListener('mouseleave', function() {
                // Check if annotation is selected
                const isSelected = window.selectedAnnotations &&
                    window.selectedAnnotations.some(selected => selected.id === annotation.id);

                // Only hide if not editing and not selected
                const editArea = document.getElementById(`edit-area-${annotation.id}`);
                if ((!editArea || editArea.style.display === 'none') && !isSelected) {
                    commentElement.style.display = 'none';
                }
            });

            // Add to image container
            imageContainer.classList.add('image-container');
            imageContainer.appendChild(commentElement);
        }

        // Function to show comment input box for new annotation
        function showCommentInputBox(annotation) {
            const bounds = annotation.target.selector.geometry.bounds;
            const imageElement = document.getElementById('my-image');
            const imageContainer = imageElement.parentElement;

            // Calculate position
            const scaleX = imageElement.getBoundingClientRect().width / imageElement.naturalWidth;
            const scaleY = imageElement.getBoundingClientRect().height / imageElement.naturalHeight;

            const commentX = (bounds.minX + (bounds.maxX - bounds.minX) / 2) * scaleX;
            const commentY = bounds.maxY * scaleY + 10;

            // Create comment input box
            const inputBoxId = `comment-input-${annotation.id}`;
            const inputBox = document.createElement('div');
            inputBox.id = inputBoxId;
            inputBox.className = 'bg-white border border-primary rounded p-2 shadow';
            inputBox.style.cssText = `
                position: absolute;
                left: ${commentX}px;
                top: ${commentY}px;
                transform: translateX(-50%);
                z-index: 1001;
                min-width: 200px;
            `;

            inputBox.innerHTML = `
                <textarea class="form-control form-control-sm mb-2" id="new-comment-${annotation.id}" placeholder="Add a comment..."
                          onkeydown="handleNewCommentKeydown(event, '${annotation.id}')" rows="3" autofocus></textarea>
                <div class="d-flex gap-1 justify-content-end">
                    <button class="btn btn-success btn-sm" onclick="saveNewComment('${annotation.id}')">Save</button>
                    <button class="btn btn-secondary btn-sm" onclick="cancelNewComment('${annotation.id}')">Cancel</button>
                </div>
            `;

            imageContainer.appendChild(inputBox);

            // Focus the textarea
            setTimeout(() => {
                const textarea = document.getElementById(`new-comment-${annotation.id}`);
                if (textarea) {
                    textarea.focus();
                }
            }, 100);

            // Store reference to current editing annotation
            window.currentEditingAnnotation = annotation;
        }

        // Function to remove annotation comment
        function removeAnnotationComment(annotationId) {
            const commentId = `comment-${annotationId}`;
            const commentElement = document.getElementById(commentId);
            if (commentElement) {
                commentElement.remove();
            }
        }

        // Function to show comment
        function showComment(annotationId) {

            const commentElement = document.getElementById(`comment-${annotationId}`);
            if (commentElement) {
                commentElement.style.display = 'block';
            }
        }

        // Function to hide comment
        function hideComment(annotationId) {
            const commentElement = document.getElementById(`comment-${annotationId}`);
            if (commentElement) {
                commentElement.style.display = 'none';
            }
        }

        // Function to hide all comments
        function hideAllComments() {
            const commentElements = document.querySelectorAll('.annotation-comment');
            commentElements.forEach(element => {
                element.style.display = 'none';
            });
        }

        // Function to edit annotation comment
        function editAnnotationComment(annotationId) {
            const commentElement = document.getElementById(`comment-${annotationId}`);
            const editArea = document.getElementById(`edit-area-${annotationId}`);
            const editInput = document.getElementById(`edit-input-${annotationId}`);

            if (commentElement && editArea && editInput) {
                // Ensure comment is visible during editing
                commentElement.style.display = 'block';

                // Get current comment from storage
                const commentData = getCommentFromStorage(annotationId);
                const currentText = commentData ? commentData.comment : '';
                editInput.value = currentText;

                editArea.style.display = 'block';
                editInput.focus();
                editInput.select();
            }
        }

        // Function to cancel editing annotation comment
        function cancelEditAnnotationComment(annotationId) {
            const editArea = document.getElementById(`edit-area-${annotationId}`);
            const editInput = document.getElementById(`edit-input-${annotationId}`);

            if (editArea && editInput) {
                editArea.style.display = 'none';
                // Reset input to original value from storage
                const commentData = getCommentFromStorage(annotationId);
                const originalText = commentData ? commentData.comment : '';
                editInput.value = originalText;
            }
        }

        // Function to save annotation comment
        function saveAnnotationComment(annotationId) {
            const editInput = document.getElementById(`edit-input-${annotationId}`);
            const editArea = document.getElementById(`edit-area-${annotationId}`);
            const commentTextElement = document.querySelector(`#comment-${annotationId} .comment-text`);

            if (!editInput || !editArea || !commentTextElement) return;

            const newText = editInput.value.trim();
            const existingComment = getCommentFromStorage(annotationId);
            const userName = existingComment ? existingComment.user : 'sourabh';

            const annotation = window.annotoriusInstance.getAnnotations().find(a => a.id === annotationId);
            if (annotation) {
                saveAnnotationToStorage(annotation, newText, userName);
            }

            commentTextElement.textContent = newText || 'No comment';
            editArea.style.display = 'none';

            if (annotation) {
                addAnnotationComment(annotation);
            }
        }

        // Function to delete annotation comment (and annotation)
        function deleteAnnotationComment(annotationId) {
            if (confirm('Are you sure you want to delete this annotation and its comment?')) {
                // Remove from Annotorious
                if (window.annotoriusInstance) {
                    const annotation = window.annotoriusInstance.getAnnotations().find(a => a.id === annotationId);
                    if (annotation) {
                        window.annotoriusInstance.removeAnnotation(annotation);
                    }
                }

                // Remove from localStorage (integrated structure)
                removeAnnotationFromStorage(annotationId);

                // Remove the comment display
                removeAnnotationComment(annotationId);
            }
        }

        // Function to save new comment
        function saveNewComment(annotationId) {
            const textarea = document.getElementById(`new-comment-${annotationId}`);
            const inputBox = document.getElementById(`comment-input-${annotationId}`);

            if (textarea && inputBox) {
                const commentText = textarea.value.trim();

                if (commentText) {
                    // Use default user name
                    const currentUser =
                        '{{ auth()->user()->name ?? DemoUser }}';

                    // Save annotation with integrated comment to storage
                    if (window.currentEditingAnnotation) {
                        saveAnnotationToStorage(window.currentEditingAnnotation, commentText, currentUser);

                        // Create integrated annotation object for display
                        const integratedAnnotation = {
                            annotation_id: window.currentEditingAnnotation.id,
                            comment: commentText,
                            user_name: currentUser,
                            user_id: '{{ auth()->id() }}',
                            item_id: '{{ $orderItem->id }}',
                            timestamp: new Date().toISOString(),
                            annotation: window.currentEditingAnnotation
                        };

                        // Remove input box
                        inputBox.remove();

                        // Add comment display with integrated structure
                        addAnnotationComment(integratedAnnotation);
                    }
                } else {
                    // No comment text, delete the annotation
                    cancelNewComment(annotationId);
                }

                window.currentEditingAnnotation = null;
            }
        }

        // Function to cancel new comment (and delete annotation)
        function cancelNewComment(annotationId) {
            const inputBox = document.getElementById(`comment-input-${annotationId}`);

            if (inputBox) {
                inputBox.remove();
            }

            // Delete the annotation since no comment was added
            if (window.annotoriusInstance && window.currentEditingAnnotation) {
                window.annotoriusInstance.removeAnnotation(window.currentEditingAnnotation);
            }

            window.currentEditingAnnotation = null;
        }

        // Function to handle keyboard events for new comment
        function handleNewCommentKeydown(event, annotationId) {
            if (event.key === 'Enter' && event.ctrlKey) {
                // Ctrl+Enter to save
                event.preventDefault();
                saveNewComment(annotationId);
            } else if (event.key === 'Escape') {
                // Escape to cancel
                event.preventDefault();
                cancelNewComment(annotationId);
            }
        }

        // Function to handle keyboard events in edit mode
        function handleEditKeydown(event, annotationId) {
            if (event.key === 'Enter' && event.ctrlKey) {
                // Ctrl+Enter to save
                event.preventDefault();
                saveAnnotationComment(annotationId);
            } else if (event.key === 'Escape') {
                // Escape to cancel
                event.preventDefault();
                cancelEditAnnotationComment(annotationId);
            }
        }

        function clearAllComments() {
            if (confirm('Are you sure you want to clear all annotations? This action cannot be undone.')) {

                fetch('/annotations/clear/1', {
                        method: 'DELETE',
                        headers: {
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                        }
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.status) {
                            localStorage.removeItem('annotorious-annotations');

                            if (window.annotoriusInstance) {
                                const annotations = window.annotoriusInstance.getAnnotations();
                                annotations.forEach(annotation => {
                                    window.annotoriusInstance.removeAnnotation(annotation);
                                });
                            }

                            const commentElements = document.querySelectorAll(
                                '.annotation-comment, .comment-input-box');
                            commentElements.forEach(element => {
                                element.remove();
                            });
                        } else {
                            alert('unable to delete')
                        }
                    })
                    .catch(error => {
                        console.error('Error clearing all data:', error);
                        // Fallback to localStorage on error
                    });
            }
        }




        function loadExistingAnnotations() {
            if (!window.annotoriusInstance) return;

            Promise.all([
                    fetch('/annotations/1').then(response => response.json()),
                ])
                .then(([annotationsData]) => {
                    let fetchedAnnotation = annotationsData.annotations
                    localStorage.setItem('annotorious-annotations', JSON.stringify(fetchedAnnotation));

                    fetchedAnnotation.forEach(annotation => {
                        try {
                            window.annotoriusInstance.addAnnotation(annotation.annotationJson);
                            setTimeout(() => {
                                addAnnotationComment(annotation.annotationJson);
                            }, 500);
                        } catch (error) {
                            console.error('Error restoring annotation:', error, annotation.annotationJson);
                        }
                    });
                })
                .catch(error => {
                    console.error('Error loading data from API:', error);
                    const savedAnnotations = loadAnnotationsFromStorage();
                    savedAnnotations.forEach(annotation => {
                        try {
                            window.annotoriusInstance.addAnnotation(annotation);
                            setTimeout(() => {
                                addAnnotationComment(annotation);
                            }, 500);
                        } catch (error) {
                            console.error('Error restoring annotation:', error, annotation);
                        }
                    });
                });
        }

        // Add click outside detection
        document.addEventListener('click', function(event) {
            if (window.currentEditingAnnotation) {
                const inputBox = document.getElementById(`comment-input-${window.currentEditingAnnotation.id}`);
                if (inputBox && !inputBox.contains(event.target)) {
                    // Check if there's text in the input
                    const textarea = document.getElementById(`new-comment-${window.currentEditingAnnotation.id}`);
                    if (textarea) {
                        const commentText = textarea.value.trim();
                        if (commentText) {
                            // Save the comment
                            saveNewComment(window.currentEditingAnnotation.id);
                        } else {
                            // Cancel and delete annotation
                            cancelNewComment(window.currentEditingAnnotation.id);
                        }
                    }
                }
            }
        });

        // jQuery document ready function
        $(document).ready(function() {
            window.selectedAnnotations = [];

            const anno = Annotorious.createImageAnnotator('my-image', {
                widgets: [{
                    widget: 'COMMENT'
                }],
                drawingEnabled: true
            });

            window.annotoriusInstance = anno;

            anno.setUser({
                id: 'user-123',
                name: 'sourabh'
            });

            setTimeout(() => {
                loadExistingAnnotations();
            }, 100);

            anno.on('createAnnotation', annotation => {
                showCommentInputBox(annotation);
            });

            anno.on('updateAnnotation', (annotation, previous) => {
                const existingComment = getCommentFromStorage(annotation.id);
                const commentText = existingComment ? existingComment.comment : '';
                const userName = existingComment ? existingComment.user : 'DemoUser';

                saveAnnotationToStorage(annotation, commentText, userName);
                addAnnotationComment(annotation);
            });

            anno.on('deleteAnnotation', annotation => {
                removeAnnotationFromStorage(annotation.id);
                removeAnnotationComment(annotation.id);
            });

            anno.on('selectionChanged', annotations => {
                window.selectedAnnotations = annotations || [];
                hideAllComments();

                if (annotations && annotations.length > 0) {
                    annotations.forEach(annotation => {
                        showComment(annotation.id);
                    });
                }
            });

            anno.on('clickAnnotation', (annotation, event) => {
                showComment(annotation.id);
            });

            anno.on('mouseEnterAnnotation', (annotation, event) => {
                showComment(annotation.id);
            });

            anno.on('mouseLeaveAnnotation', (annotation, event) => {
                const isSelected = window.selectedAnnotations &&
                    window.selectedAnnotations.some(selected => selected.id === annotation.id);

                if (!isSelected) {
                    hideComment(annotation.id);
                }
            });
        });
    </script>
</body>

</html>
