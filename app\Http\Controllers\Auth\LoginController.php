<?php

namespace App\Http\Controllers\Auth;

use Exception;
use Carbon\Carbon;
use App\Models\Lead;
use App\Models\Leadfor;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Validator;

class LoginController extends Controller
{

    public function loginForm()
    {

        $reqFrom = parse_url(request()->header('referer'));
        if(request()->has('goto')){
            session()->put('gotoAfterLogin', request()->goto);
        }


        if ($reqFrom['path'] && $reqFrom['host'] == request()->getHost()) {
            session()->put('referFrom', request()->header('referer'));
        }

        return view('auth5.login');
    }
    public function SendOTP(Request $request)
    {
        session()->put('userData', null);
        $validator = Validator::make($request->all(), [
            'mobile' => "bail|required_without:email",
            "email" => "bail|required_without:mobile|email"
        ], [
            "mobile.required_without" => "Field cannot be empty",
            "email.required_without" => "Field cannot be empty"
        ]);

        if ($validator->fails()) {
            return redirect()->back()->withErrors($validator)->withInput();
        }

        if ($request->has('mobile') && $request->has('code')) {
            $request['mobile'] = $request->code . $request->mobile;
        }
        if ($request->email) {
            $lead = User::where('email', $request->email)->first();
        } else {
            $lead = User::where('mobile', $request->mobile)->first();
        }
        if ($request->has('email')) {
            $passData["login_email"] = $request->email;
        } else {
            $passData["login_mobile"] = $request->mobile;
            $passData["country_code"] = $request->code;
        }
        session()->put('userData', $passData);
        $currentData = ['session' => session()->get('userData'), 'request' => $request->all()];
        if ($lead) {
            if ($lead->isPasswordGenerated) {
                return redirect()->route('loginPassword');
            } elseif ($lead->isIntroFormFilled) {
                $passData = [
                    "login_email" => $lead->email,
                    "login_mobile" => null,
                    "country_code" => null
                ];
                session()->put('userData', $passData);
                $emailOtp = sendLoginEmailOTP($lead->id, $lead->email);
                if ($emailOtp["status"]) {
                    session()->put('userData.otpLength', $emailOtp["otpLength"]);
                    session()->put('userData.email_sent_to', $lead->email);
                    session_log(session()->getId(), $lead->id, $request->header('referer'), json_encode($currentData), 'EmailOtpSend');
                    return redirect()->route('loginOtp');
                } else {
                    return redirect()->route('passwordGenerateView');
                }
            } else {
                return redirect()->route('registration');
            }
        } else {
            try {
                DB::beginTransaction();
                $leadNew = $request->has('email') ? Lead::create(["email" => $request->email]) : Lead::create(["mobile" => $request->mobile]);
                session_log(session()->getId(), $leadNew->id, $request->header('referer'), json_encode($currentData), "New Lead Created.");
                DB::commit();
                return redirect()->route('registration');
            } catch (Exception $e) {
                session_log(session()->getId(), $lead->id ?? null, $request->header('referer'), json_encode($currentData), 'Unable to fill intro ', $e);
                Log::error('Unable to fill intro: ' . $e->getMessage(), ['session_id' => session()->getId(), 'lead_id' => $lead->id ?? null, 'referer' => $request->header('referer'), 'data' => $currentData]);
                return redirect(route('login'))->withErrors("error", ErrMsg());
            }
        }
    }
    public function registrationForm()
    {
        $lead_for = Leadfor::pluck('name', 'id');
        if (session()->has('userData.login_mobile') || session()->has('userData.login_email')) {
            return view('auth5.reg', compact("lead_for"));
        } else {
            return redirect()->route('login')->with('error', 'Invalid Session.');
        }
    }
    public function registrationSubmit(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'mobile' => "required_without:email",
            "email" => "required_without:mobile|email",
            "company" => "required",
            "name" => "required",
            'interestedIn' => 'required'
        ], [
            "mobile.required_without" => "Mobile field is required",
            "email.required_without" => "Email field is required"
        ]);
        if ($validator->fails()) {
            return redirect()->route('registration')->withErrors($validator)->withInput();
        }
        $userData = session()->get('userData');
        $currentData = ['session' => $userData, 'request' => $request->all()];
        $mergeFound = 0;
        try {

            if (isset($userData['login_mobile'])) {
                $lead = Lead::where("mobile", $userData['login_mobile'])->first();
                $leadExist = Lead::where("email", $request->email)->first();

                if ($leadExist && ($leadExist->mobile != $lead->mobile)) {
                    $mergeFound = 1;
                } else {
                    $lead->email = $request->email;
                }
            } elseif (isset($userData['login_email'])) {
                $lead = Lead::where("email", $userData['login_email'])->first();
                $leadExist = Lead::where("mobile", $request->mobile)->first();
                if ($leadExist && ($leadExist->email != $lead->email)) {
                    $mergeFound = 1;
                } else {

                    $lead->mobile = $request->mobile;
                }
                $currentData = ['session' => $userData, 'request' => $request->all()];
            } else {
                session_log(session()->getId(), null, $request->header('referer'), json_encode($currentData), 'IntroForm', "Unable to Find Lead.");
                return redirect(route('login'))->with("error", ErrMsg());
            }
            if ($lead) {
                $lead->name = $request->name;
                $lead_for = implode(",", $request->interestedIn);
                $lead->isIntroFormFilled = 1;
                $lead->lead_for_ids = $lead_for;
                $lead->update();

                session_log(session()->getId(), $lead->id, $request->header('referer'), json_encode($currentData), 'Lead Intro updated.');

                if ($mergeFound) {
                    if (isset($userData['login_mobile'])) {
                        $mobile = substr($leadExist->mobile, -4);
                        return redirect()->back()->withInput()->with('error', "Entered Email is already associated with numbers ends with $mobile.");
                    } elseif (isset($userData['login_email'])) {
                        $email = maskEmail($leadExist->email);
                        return redirect()->back()->withInput()->with('error', "Entered mobile number is already associated with ($email).");
                    }
                }
                // $passData["login_email"] = $lead->email;
                // session()->put('userData', $passData);

                $passData["login_email"] = $lead->email;
                $passData["login_mobile"] = null;
                $passData["country_code"] = null;
                session()->put('userData', $passData);

                $emailOtp = sendLoginEmailOTP($lead->id, $lead->email);
                if ($emailOtp["status"]) {
                    session()->put('userData.otpLength', $emailOtp["otpLength"]);
                    session()->put('userData.email_sent_to', $lead->email);
                    session_log(session()->getId(), $lead->id, $request->header('referer'), json_encode($currentData), 'EmailOtpSend');
                    return redirect()->route('loginOtp');
                } else {
                    return redirect()->route('passwordGenerateView');
                }
            } else {
                session_log(session()->getId(), $lead->id ?? null, $request->header('referer'), json_encode($currentData), 'IntroForm', "Unable to Find Lead.");
                return redirect('/')->with("error", 'User not Found.');
            }
        } catch (Exception $e) {
            if (session_log(session()->getId(), $lead->id ?? null, $request->header('referer'), json_encode($currentData), 'Unable to fill intro ', $e)['status'] != true) {
                Log::info(json_encode([session()->getId(), $lead->id ?? null, $request->header('referer'), json_encode($currentData), 'Unable to fill intro ', $e->getMessage()]));
            }
            return redirect(route('login'))->with("error", ErrMsg());
        }
    }
    public function loginOtp()
    {


        if (session()->has('userData.login_mobile') || session()->has('userData.login_email')) {
            return view('auth5.otp');
        } else {
            return redirect()->route('login')->with("error", "Session timeout");
        }
    }
    public function otpVerify(Request $request)
    {

        $validator = Validator::make($request->all(), [
            'otp' => "required",
        ]);
        if ($validator->fails()) {
            return redirect()->back()->withErrors($validator)->withInput();
        }
        $userData = session()->get('userData');
        $currentData = ['session' => $userData, 'request' => $request->all()];
        try {
            if (isset($userData['login_email'])) {
                $lead = Lead::where("email", $userData['login_email'])->first();
            } else {
                session_log(session()->getId(), null, $request->header('referer'), json_encode($currentData), 'IntroForm', "Unable to Find Lead.");
                return redirect(route('login'))->with("error", ErrMsg());
            }

            if (isset($userData['login_email']) || $lead->email) {
                $currentDateTime = Carbon::now()->setTimezone("Asia/Calcutta");
                $diffInMinutes = $currentDateTime->diffInMinutes($lead->emailOtpExpiry);
                if ($diffInMinutes <= "10") {
                    if ($lead->emailOtp == $request->otp) {
                        $lead->emailOtpVerified = 1;
                        $lead->update();
                        session_log(session()->getId(), $lead->id, $request->header('referer'), json_encode($currentData), 'Email Verified & login Successfully.');

                        return redirect()->route('passwordGenerateView');
                    } else {
                        session_log(session()->getId(), $lead->id, $request->header('referer'), json_encode($currentData), "emailVerify", "Invalid OTP");

                        return redirect()->back()->with("error", "The OTP you typed is wrong. Kindly enter the accurate OTP that was sent to you.");
                    }
                } else {
                    session_log(session()->getId(), $lead->id, $request->header('referer'), json_encode($currentData), "emailVerify", "Email Otp Expired");

                    return redirect()->back()->with("error", "OTP Expired !!");
                }
            } else {
                session_log(session()->getId(), $lead->id, $request->header('referer'), json_encode($currentData), "emailVerify", "User email not found");
                return redirect()->route('login')->with("error", "User not Found");
            }
        } catch (Exception $e) {
            return redirect()->route('login')->with("error", ErrMsg());
        }
    }

    public function passwordGenerateView()
    {
        if (session()->has('userData.login_mobile') || session()->has('userData.login_email')) {
            return view('auth5.generatePassword');
        } else {
            return redirect()->route('login')->with('error', 'Session timeout !!');
        }
    }
    public function generatePasswordSave(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'password' => 'required|confirmed',
            'password_confirmation' => 'required'
        ], [
            "password.confirmed" => "Password must match with confirm password field",
            'password_confirmation.required' => "password confirm field is required"
        ]);
        if ($validator->fails()) {

            return redirect()->back()->withErrors($validator)->withInput();
        }


        $userData = session()->get('userData');
        $currentData = ['session' => $userData, 'request' => $request->all()];
        try {
            if (isset($userData['login_mobile'])) {
                $lead = Lead::where("mobile", $userData['login_mobile'])->first();
            } elseif (isset($userData['login_email'])) {
                $lead = Lead::where("email", $userData['login_email'])->first();
            } else {
                session_log(session()->getId(), null, $request->header('referer'), json_encode($currentData), 'IntroForm', "Unable to Find Lead.");
                return redirect(route('login'))->with("error", ErrMsg());
            }

            if ($lead) {
                $lead->password = $request->password;
                $lead->isPasswordGenerated = 1;
                $lead->update();
                session_log(session()->getId(), $lead->id, $request->header('referer'), json_encode($currentData), 'Password Generate', "Password Generate success");
                return redirect()->route('loginPassword');
            } else {
                session_log(session()->getId(), $lead->id, $request->header('referer'), json_encode($currentData), 'Password Generate', "Password Generate failed User not found");
                return redirect()->route('login')->with("error", "User Not Found");
            }
        } catch (Exception $e) {
            return redirect()->route('login')->with("error", ErrMsg());
        }
    }
    public function loginPassword()
    {
        if (session()->has('userData.login_mobile') || session()->has('userData.login_email')) {
            return view('auth5.password');
        } else {
            return redirect()->route('login')->with('error', 'Session timeout !!');
        }
    }
    public function verifyPassword(Request $request)
    {

        $validator = Validator::make($request->all(), [
            'password' => "required",
        ]);
        if ($validator->fails()) {

            return redirect()->back()->withErrors($validator)->withInput();
        }
        $currentData = ['session' => session()->get('userData'), 'request' => $request->all()];
        try {
            if (session()->has('userData.login_mobile')) {
                $lead = User::where("mobile", session()->get('userData.login_mobile'))->first();
            } elseif (session()->has('userData.login_email')) {
                $lead = User::where("email", session()->get('userData.login_email'))->first();
            } else {
                return redirect('/')->with('error', 'Session Invalidate.');
            }

            if ($lead) {
                if ($request->password == $lead->password) {

                    Auth::loginUsingId($lead->id);
                    session()->put('userData', null);
                    session_log(session()->getId(), $lead->id, $request->header('referer'), json_encode($currentData), "login Success");
                  
                    return redirect(session()->get('gotoAfterLogin')??session()->get('referFrom') ?? '/');
                } else {
                    session_log(session()->getId(), $request->lead, $request->header('referer'), json_encode($currentData), "Password verify Fail", "Invalid Password");

                    return redirect()->back()->withErrors(['password' => 'The password you entered is incorrect.']);
                }
            } else {
                return redirect('/')->with('error', 'User not Found.');
            }
        } catch (Exception $e) {

            session_log(session()->getId(), $lead->id ?? null, $request->header('referer'), json_encode($currentData), "password verification Fail with Exception.", $e->getMessage());

            return redirect('/')->with("error", ErrMsg());
        }
    }

    public function noPasswordSendOtp(Request $request)
    {
        if ($request->email) {
            $lead = User::where('email', $request->email)->first();
        } elseif ($request->mobile) {
            $lead = User::where('mobile', $request->mobile)->first();
        } else {
            return redirect()->route('login');
        }
        $currentData = ['session' => session()->get('userData'), 'request' => $request->all()];


        $passData["login_email"] = $lead->email;
        $passData["login_mobile"] = null;
        $passData["country_code"] = null;
        session()->put('userData', $passData);


        if ($lead->email) {
            session_log(session()->getId(), $lead->id, $request->header('referer'), json_encode($currentData), 'Mobile OTP send fail trying to send Email OTP.');
            $emailOtp = sendLoginEmailOTP($lead->id, $lead->email);
            if ($emailOtp["status"]) {
                session()->put('userData.otpLength', $emailOtp["otpLength"]);
                session()->put('userData.email_sent_to', $lead->email);
                session_log(session()->getId(), $lead->id, $request->header('referer'), json_encode($currentData), 'Email OTP send.');

                return redirect()->route('loginOtp');
            } else {
                session_log(session()->getId(), $lead->id, $request->header('referer'), json_encode($currentData), 'Email OTP send Fail redirect to password create.');
                return redirect()->route('passwordGenerateView');
            }
        }

        return redirect('/')->with("error", 'Unable to process request currently.');
    }
    public function logout()
    {
        Auth::logout();
        Session::flush();
        return redirect('/');
    }
}
