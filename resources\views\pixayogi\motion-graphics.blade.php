@extends('components.pixayogi.main')

@section('pixayogi-css')
    <link rel="stylesheet" href="{{ asset('assets/pixayogi/css/motion-graphics.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/pixayogi/css/faq.css') }}">
@endsection

@section('pixayogi-js')
    <script src="{{ asset('assets/pixayogi/js/comman.js') }}"></script>
@endsection
@section('webpage')
    {{-- ---------first section-------- --}}
    <section>
        <div
            class="container-fluid pt-5"style="background-image: url({{ asset('assets/pixayogi/images/newhomebg.png') }}); background-repeat: no-repeat; background-size: cover;">
            <div class=" container-lg">
                <div class="row align-items-center">
                    <div class="col-lg-6">
                        <div class="row">
                            <h2 class="mb-4 lh-1">Motion Graphics Services at a flat rate</h2>
                        </div>
                        <div class="row">
                            <p class="fs-5" style="color: #7E7887;">Do you wish to use animation to advance your
                                marketing? Use the
                                most
                                economical motion graphics solution available to elevate your creative work.<img
                                    src="{{ asset('assets/pixayogi/images/tick-1.png') }}" alt="tick" id="tick-1" />
                            </p>
                        </div>
                        <div class="row" style="width:fit-content;">
                            <a aria-label="link" class="text-decoration-none" href="{{ route('pixayogi.contact') }}">
                                <button style="background: #44C9F5"
                                    class="rounded-pill border-0 d-flex align-items-center pt-2 pb-2 pe-4 ps-4 d-flex gap-3 pink-btn-shadow pink-btn text-white fs-5">
                                    <div class="d-flex f-s-18">Talk to a human</div>
                                    <div class="">
                                        <img style="height: 35px;"src="{{ asset('assets/global/images/button.webp') }}"
                                            alt="">
                                    </div>
                                </button>
                            </a>
                        </div>
                    </div>
                    <div class="col-lg-6 text-center">
                        <img class="img-fluid" src="{{ asset('assets/pixayogi/images/motion-sec1.png') }}" alt="img" />
                    </div>
                </div>
            </div>
        </div>
    </section>
    {{-- ---------first section End-------- --}}
    {{-- ---------second section start-------- --}}
    <section>
        <div class="container-fluid  second-container">
            <div class="container-lg pb-5">
                <div class="row align-items-center">
                    <div class="col-lg-6 pt-5 ">
                        <div class="row">
                            <h3 class="lh-sm mb-4">Your media should have motion.</h3>

                        </div>
                        <div class="row">
                            <p id="first-p" class="fs-5 ">Your static graphics become dynamic showstoppers thanks to
                                motion graphics. We can create anything from start or work with your current assets. Let us
                                carry out your imaginative concept without bothering you.
                            </p>
                        </div>
                        <div class="row" style="width:fit-content;">
                            <a aria-label="link" class="text-decoration-none" href="{{ route('pixayogi.plans') }}">
                                <button style="background: #44C9F5"
                                    class="fs-5 text-white rounded-pill border-0 d-flex align-items-center pt-2 pb-2 pe-4 ps-4 gap-2">
                                    Pick your plans
                                    <i class="fa-brands fa-telegram fa-2x" style="color: #FFF;"></i>
                                </button></a>
                        </div>

                    </div>
                    <div class="col-lg-6 pt-4 mt-5">
                        <div class="d-flex gap-3">
                            <div class="pt-2">
                                <img width="80px" height="80px" src="{{ asset('assets/pixayogi/images/yoo4.png') }}"
                                    alt="img" />
                            </div>
                            <div class="ps-lg-4">
                                <div class="mb-2"><strong class="sec2-heading">Improve your media.</strong>
                                </div>
                                <div>
                                    <p class="section2-p">
                                        By converting static images into compelling animated <a aria-label="link"
                                            target="_blank" href="https://stickyfirst.com/paid-ads/"
                                            class="fw-normal text-decoration-none text-dark">
                                            <span>social media advertising</span></a> or by including animated text in
                                        already existing video material, you may increase paid conversion rates.
                                    </p>
                                </div>
                            </div>
                        </div>
                        <div class="d-flex mt-3">
                            <div class="pt-2">
                                <img width="80px" height="80px" src="{{ asset('assets/pixayogi/images/yoo5.png') }}"
                                    alt="img" />
                            </div>
                            <div class="ps-lg-4">
                                <div class="mb-2"><strong class="sec2-heading">Create animated presentations</strong>
                                </div>
                                <div>
                                    <p class="section2-p">
                                        By incorporating motion into your slides, you can convert your still visuals into
                                        moving graphics and give your presentation designs more heft.
                                    </p>
                                </div>
                            </div>
                        </div>
                        <div class="d-flex mt-3">
                            <div class="pt-2">
                                <img width="80px" height="80px" src="{{ asset('assets/pixayogi/images/yoo6.png') }}"
                                    alt="img" />
                            </div>
                            <div class="ps-lg-4">
                                <div class="mb-2"><strong class="sec2-heading">Make product explanation videos</strong>
                                </div>
                                <div>
                                    <p class="section2-p">
                                        Animated product explainers can help you reach your conversion and retention goals.
                                        This is excellent for producing on-demand product demos or overviews.
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    {{-- ---------second section End-------- --}}
    {{-- ---------third section End-------- --}}
    <div class="container-fluid  third-container pb-lg-5 bg-img-fixed"
        style="background-image: url({{ asset('assets/pixayogi/images/bgf4copy.jpg') }});;">
        <div class="container-lg">
            <div class="pt-5 pb-3 pb-lg-5 row d-flex flex-column" id="third-upper-column">
                <h3 class=" pb-lg-3 ">
                    Why choose PixaYogi?
                </h3>
                <p class="dmsans third-sec-h4 mt-3 mt-lg-0">
                    PixaYogi is a flat-rate and all-in-one creative enablement
                    platform and the only subscription creative services company
                    that offers real-time communication with your designer via Slack.
                </p>

            </div>

            <div class="d-flex gap-4 ">
                <div class="row cards-row-container">
                    <div class="col-lg-4 p-2">
                        <div class="card card-pad shadow-lg h-100">
                            <h4 class=" card-heading mb-4 fw-bold">Unmatched skills</h4>
                            <p class="card-p dmsans lh-sm">
                                Work together with PixaYogi’s skilled in-house Motion Graphics artists during business
                                hours. They have extensive training in all phases of animation.
                            </p>
                        </div>
                    </div>
                    <div class="col-lg-4 p-2">
                        <div class="card card-pad shadow-lg h-100">
                            <h4 class="card-heading mb-4 fw-bold">Time savings</h4>
                            <p class="card-p dmsans lh-sm ">
                                With a streamlined creative process, ready-to-use animation files, quick turnarounds, and
                                end-to-end workflow management, you can increase your productivity.
                            </p>
                        </div>
                    </div>
                    <div class="col-lg-4 p-2">
                        <div class="card card-pad shadow-lg h-100">
                            <h4 class="card-heading mb-4 fw-bold">Cost efficiency</h4>
                            <p class="card-p dmsans lh-sm">
                                With a flat-rate subscription that offers you the freedom to scale up or down based on your
                                business needs, you can elevate your creativity on a budget. No extra costs. No agreements.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {{-- ---------third section End-------- --}}
    {{-- ---------forth section start-------- --}}
    <div class="container-fluid pb-lg-5 pe-0 ps-0 bg-body-tertiary faq-section-margin">
        <div class="container-lg pt-5">
            <h3 class="text-center fw-bold">Frequently Asked Questions</h3>
            @php
                $faqs = [
                    [
                        'question' => 'What are Motion Graphics?',
                        'answer' =>
                            'Motion graphics are animated images or designs that give the impression of movement and help you connect with your audience more effectively.',
                    ],
                    [
                        'question' => 'What type of files can you deliver?',
                        'answer' =>
                            'The main format will be MP4. We will also deliver the After Effects source file (AEP) and MOV file format if you need it for transparent Motion Graphics assets.',
                    ],
                    [
                        'question' => 'Can I send you a video and have the Motion Graphics added to that?',
                        'answer' =>
                            'Yes, but the video needs to be the final version – Motion Graphics does not include video editing. We also cannot work on videos longer than 5 minutes, as that is the maximum length of output for Motion Graphics videos we create for you.',
                    ],
                    [
                        'question' => 'What software is used to create Motion Graphics?',
                        'answer' => 'We use Adobe After Effects.',
                    ],
                    [
                        'question' => 'Do you provide voice-overs?',
                        'answer' => 'No, but if you have a voice-over ready we can add it!',
                    ],
                    [
                        'question' => 'Do you provide audio?',
                        'answer' =>
                            'No, but you can send your music or sound effects to be added. Just tell us where in the video you want specific sound effects or music to play. You must send us licensed audio files that you are allowed to use. You can find royalty-free music to use at Pixabay Music, Shutterstock, Adobe, or even YouTube Studio (some are free, others are paid for).',
                    ],
                ];
            @endphp
            <div class="container-lg">
                <x-faq-accordion :faqs="$faqs" collapsed-color="#ffffff" active-color="#3cd3fd" />
            </div>
        </div>
    </div>
    {{-- ---------forth section End-------- --}}
    {{-- ---------fifth section start-------- --}}
    <div class="container-fluid">
        <div class="row ">
            <div id="main" class="col-lg-6 p-0">
                <div class="video-wrapper">
                    <video class="img-fluid video" src="{{ asset('assets/pixayogi/images/Motion graphic.mp4') }}" muted
                        autoplay loop></video>
                    <div class="overlay"></div>
                </div>


            </div>
            <div class="col-lg-6 second-part d-flex align-items-center pb-5 pb-lg-0">
                <div class="fifth-section-padding" style="width: 625px;">
                    {{-- <div class="container-lg "> --}}
                    <div class="fifth-sec-heading lh-sm mb-4"><span class="fifth-sec-color-heading">Motion Graphics is
                            included in</span> Premium include Presentation <span class="fifth-sec-color-heading">
                            Graphics Premium.</span></div>
                    <div class="fifth-sec-p">All plans include Adobe source files, unlimited requests, and unlimited
                        changes.</div>
                    <div class="mt-3" style="width:fit-content;">
                        <a aria-label="link" class="text-decoration-none" href="{{ route('pixayogi.plans') }}">
                            <button style="background: #44C9F5"
                                class="mt-3 fs-5 text-white rounded-pill border-0 d-flex align-items-center pt-2 pb-2 pe-4 ps-4 gap-2">
                                View our plans
                                <i class="fa-brands fa-telegram fa-2x" style="color: #FFF;"></i>
                            </button></a>
                    </div>
                    {{-- </div> --}}

                </div>
            </div>
        </div>
    </div>
    {{-- ---------fifth section End-------- --}}
    {{-- -----------sixth section start-------------- --}}
    <div class="container-fluid pt-5">
        <div class="container-lg">
            <div class="row">
                <div class="col-lg-6 contact-content-container">
                    <div class="row">
                        <p class="heading lh-1">
                        <h3>Do you want to test PixaYogi ?</h3>
                        </p>
                    </div>
                    <div class="row">
                        <p class="last-sec-p ">
                            Get your designs tomorrow after creating your project today. It’s that easy. Select your design
                            from our collections.
                        </p>
                    </div>
                    <div class="d-flex flex-column flex-lg-row flex-md-row gap-md-5 gap-lg-0">
                        <div class="col-lg-6 contact-number-container">
                            <p>We’re In Business</p>
                            <div class="row number-div">
                                <div class="col p-0 d-flex align-items-center justify-content-start">
                                    <span class="last-number fw-bolder">11+</span>
                                    <h3 class="mb-0 ps-2 fs-2 fw-bolder">Years</h3>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-lg-6">
                                    <div class="separator"></div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6 contact-number-container">
                            <p>Completed Successfully</p>
                            <div class="row number-div">
                                <div class="col p-0 d-flex align-items-center justify-content-start">
                                    <span class="last-number fw-bolder">350+</span>
                                    <h3 class="mb-0 ps-2 fs-2 fw-bolder">Projects</h3>
                                </div>
                            </div>
                            <div class="row">

                                <div class="col-lg-6">
                                    <div class="separator"></div>
                                </div>
                            </div>
                        </div>

                    </div>
                    <div class="" style="width:fit-content;">
                        <a aria-label="link" class="text-decoration-none" href="{{ route('pixayogi.contact') }}">
                            <button style=" background: #44C9F5"
                                class="mt-5 rounded-pill border-0 d-flex align-items-center pt-2 pb-2 pe-4 ps-4 d-flex gap-3 pink-btn-shadow pink-btn text-white fs-5">
                                <div class="d-flex f-s-18">Talk to a human</div>
                                <div class="">
                                    <img style="height: 35px;"src="{{ asset('assets/global/images/button.webp') }}"
                                        alt="">
                                </div>
                            </button>
                        </a>
                    </div>
                </div>
                <div class="col-lg-6  p-0 contact-img-container">
                    <img class="img-fluid mt-4 mt-lg-0" src="{{ asset('assets/pixayogi/images/girlLaptop.png') }}"
                        alt="img" class="img-fluid" />
                </div>
            </div>
        </div>
    </div>

    {{-- -----------sixth section end-------------- --}}
@endsection
