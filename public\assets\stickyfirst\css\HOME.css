body {
    /* background: #000; */
    /* color: #FFF; */
    margin: 0;
    padding: 0;
}

.gradient-color {
    background: linear-gradient(90deg, transparent 0%, #000 100%);
}

#main {
    width: 100%;
    /* height: 100vh; */
    position: relative;
}

.main-text {
    position: absolute;
    left: 13%;
    top: 42%;
    transform: translate(-13%, -42%);
    line-height: 0;
}

.main-text h2 {
    font-weight: 500;
    font-size: 30px;
    color: #E0FFFF;
}

.main-text h1 {
    padding-top: 50px;
    font-size: 70px;
    font-weight: 500;
    color: #fff;
}

.main-text p {
    font-size: 20px;
    color: #E0FFFF;
    margin: 25px 0px 25px;
    line-height: 50px;
}

.main-text a {
    width: 150px;
    height: 40px;
    outline: none;
    border-radius: 195px;
    font-size: 15px;
    font-weight: 200;
    padding: 1rem;
}

/* .btn {
    border: 1px solid rgba(255, 255, 255, 0.1);
    color: #E0FFFF;
} */

video {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.BG-img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    opacity: 0.15;
}

.text-wrapper {
    position: relative;
}

.overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    background-image: linear-gradient(to bottom, rgba(255, 255, 255, 0) 50%, rgba(0, 0, 0));
}

.MT-3 {
    margin-top: 3rem;
}

.MT-5 {
    margin-top: 5rem;
}

.MT-10 {
    margin-top: 7rem;
}

.MT-15 {
    margin-top: 7rem;
}

/* .PB-5 {
    padding-bottom: 5rem;
} */

.PB-4 {
    padding-bottom: 4rem;
}

.MB-3 {
    margin-bottom: 3rem;
}

@media only screen and (max-width: 991px) {
    .MT-10 {
        margin-top: 5rem;
    }

    .MT-15 {
        margin-top: 5rem;
    }

    .PB-4 {
        padding-bottom: 0rem;
    }
}

#next {
    background-color: #22232E;
    height: 100vh;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    padding: 30px;
}

.btn-grad {
    background-image: linear-gradient(to right, #DC2424 0%, #4A569D 51%, #DC2424 100%);
    margin: 10px;
    padding: 15px 45px;
    text-align: center;
    text-transform: uppercase;
    transition: 0.5s;
    background-size: 200% auto;
    color: white;
    border-radius: 10px;
    display: block;
}

.btn-grad:hover {
    background-position: right center;
    color: #fff;
    text-decoration: none;
}

.content {
    transform: translateX(0%);
    transform: translateY(60%);
    color: #f1f1f1;
    width: 900px;
    padding: 20px;
}

@media only screen and (max-width: 991px) {
    .content {
        padding-top: 10rem !important;
    }
}

@media only screen and (max-width: 767px) {
    .content {
        padding-top: 7rem !important;
    }
}

@media only screen and (max-width: 425px) {
    .content {
        padding-top: 4rem !important;
    }
}

.heading-color {
    width: max-content;
    background: -webkit-linear-gradient(180deg, #3E5AFA 0%, #e94526 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.icon-s-320 {
    display: block;
}

.icon-h-320 {
    display: none;
}

@media only screen and (max-width: 768px) {

    .icon-h-320 {
        display: block;
    }

}

@media only screen and (max-width: 768px) {

    .icon-s-320 {
        display: none;
    }

}

.home-heading-size {
    font-size: 50px;
}

@media only screen and (max-width: 991px) {
    .home-heading-size {
        font-size: 45px;
    }
}

@media only screen and (max-width: 991px) {
    .home-heading-size {
        font-size: 35px;
    }
}

.VS-heading {
    font-size: 4rem;
    line-height: 65px;
    color: #000;
    font-weight: 600;
}

@media only screen and (max-width: 768px) {
    .VS-heading {
        font-size: 2em;
        line-height: 35px;
    }

    .FS-light-heading {
        font-size: 2em !important;
    }

    .FS-heading {
        font-size: 2em !important;
    }
}

.VS-para {
    color: #000;
    font-size: 1.3em;
    margin-bottom: 20px;
}

.TYC-para {
    color: #FFF;
    font-size: 1.3em;
    margin-bottom: 20px;
}

.max-width-btn {
    width: max-content !important;
}

.hero-btn {
    align-items: center;
    gap: 1rem;
}

@media only screen and (max-width: 1200px) {
    .hero-btn {
        flex-direction: column;
        align-items: flex-start;
    }
}

@media only screen and (max-width: 991px) {
    .hero-btn {
        flex-direction: row;
        align-items: center;
    }
}

@media only screen and (max-width: 567px) {
    .hero-btn {
        flex-direction: column;
        align-items: flex-start;
    }
}

.color-btn {
    color: #FFF;
    background-color: #000;
    /* border: 1px solid black; */
}

.color-white-btn {
    color: #000;
    background-color: #FFF;
    /* border: 1px white solid; */
}

.HP-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    row-gap: 1rem;
    column-gap: 1rem;
}

.HP-grid-item {
    /* background-color: #FFF; */
    padding: 2rem;
    border-radius: 15px;
    display: flex;
    align-items: center;
    gap: 2rem;
    font-size: 1.4em;
    box-shadow: 5px 4px 28.5px -15px rgba(0, 0, 0, 0.25);
}

.btn-CB {
    background-color: #17a8e3;
}

.btn-CB:hover {
    background-color: #17a8e3;
}

@media only screen and (max-width: 991px) {
    .HP-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .content {
        transform: translateY(0%);
        padding: 0px;
        width: auto;
    }

    .heading-color {
        width: auto;
    }
}

@media only screen and (max-width: 768px) {
    .HP-grid {
        grid-template-columns: repeat(1, 1fr);
    }
}

@media only screen and (max-width: 400px) {
    .FS-heading {
        font-size: 1.6em !important;
    }

    .FS-light-heading {
        font-size: 1.6em !important;
    }

    .TYC-heading {
        font-size: 1.6em !important;
    }

    .big-heading {
        font-size: 2em !important;
    }
}

.HP-first-grid-item {
    background-color: #0f0f11;
    padding: 2rem;
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 1.4em;
    color: #fff;
}

.FS-heading {
    line-height: 1.3em;
    font-size: 3rem;
}

.FS-light-heading {
    line-height: 1.3em;
    font-size: 3em;
    color: #FFF;
}

.TYC-heading {
    line-height: 1.3em;
    font-size: 2.5em;
    color: #FFF;
}

.big-heading {
    line-height: 1.3em;
    font-size: 3.5em;
}

.color-para {
    font-size: 1.3em;
}

.color-light-para {
    font-size: 1.3em;
    color: #FFF;
}

.testi-para {
    color: #65676b;
    font-size: 16px;
}

.OS-grid {
    display: grid;
    align-items: center;
    grid-template-columns: repeat(3, 1fr);
    column-gap: 3rem;
    row-gap: 1rem;
}

.OS-grid-item {
    /* background-color: #0f0f11; */
    /* width: 350px; */
    background-color: #fff;
    color: #000;
    padding: 1rem 2rem 1rem 2rem;
    border-radius: 22px 0 22px 0;
    box-shadow: 0px 2px 14.300000190734863px -5px rgba(0, 0, 0, 0.07);
}

@media only screen and (max-width: 991px) {
    .OS-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media only screen and (max-width: 768px) {
    .OS-grid {
        grid-template-columns: repeat(1, 1fr);
    }
}

.OS-link {
    text-decoration: none;
}

.OS-link:hover {
    color: #cc9ee5 !important;
}

.testi-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    column-gap: 3rem;
    row-gap: 1rem;
}

.testi-grid-item {
    background-color: #0f0f11;
    padding: 3rem;
    border-radius: 30px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-direction: column;
}

@media only screen and (max-width: 991px) {
    .testi-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media only screen and (max-width: 768px) {
    .testi-grid {
        grid-template-columns: repeat(1, 1fr);
    }
}

.testi-img {
    height: 100px;
    width: 100px;
    position: relative;
    margin: 0 auto;
    border-radius: 50%;
    margin-bottom: 30px;
}

.process-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    column-gap: 3rem;
    row-gap: 3rem;
}

.process-grid-item {
    background-color: #0f0f11;
    padding: 3rem;
    border-radius: 30px;
}

@media only screen and (max-width: 991px) {
    .process-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media only screen and (max-width: 768px) {
    .process-grid {
        grid-template-columns: repeat(1, 1fr);
    }
}

.process-grid-item p {
    color: #65676b;
}

.contact-heading {
    font-size: 60px;
    color: #65676b;
    line-height: 1.3em;
}

.contact-flex {
    display: flex;
}

@media only screen and (max-width: 768px) {
    .contact-flex {
        flex-direction: column;
    }

    .contact-flex .form-floating {
        padding: 0rem !important;
    }
}

.img-elipse {
    position: absolute;
    z-index: -1;
    margin-top: -55px;
}

@media only screen and (max-width: 360px) {
    .img-elipse {
        max-width: 92%;
    }
}

.bg-color {
    background-color: #0f0f11;
    padding: 3rem;
    border-radius: 30px;
}

@media only screen and (max-width: 425px) {
    .bg-color {
        padding: 1rem;
    }
}

.form-control:focus {
    border-color: transparent;
    outline: 0;
    box-shadow: 0 0 0 0.25rem #FFF;
}

.form-control {
    color: #000 !important;
}

/* MARQUEE-Section Start */

.mobile-container {
    padding: 0 !important;
}

.marquee-w {
    /* position: relative; */
    display: block;
    /* width: 750px; */
    height: 100%;
    top: 50%;
    left: 50%;
    /* transform: translate(-50%, -50%); */
    overflow: hidden;
}

.marquee-w img {
    position: relative;
    z-index: 1;
}

.marquee-first {
    position: absolute;
    display: block;
    margin: auto auto;
    white-space: nowrap;
    overflow: hidden;
    max-width: 100%;
    height: 25%;
    margin-top: 120px;
    z-index: 0;
}

.marquee-second {
    position: absolute;
    display: block;
    margin: auto auto;
    white-space: nowrap;
    overflow: hidden;
    max-width: 100%;
    height: 25%;
    margin-top: -365px;
    z-index: 2;
}

.marquee-first span {
    display: inline-block;
    /* padding-left: 100%; */
    text-align: center;
    -webkit-text-stroke: 1px #fff;
    white-space: nowrap;
    min-width: 100%;
    height: 100%;
    font-size: 130px;
    font-weight: 600;
    color: #fff;
    line-height: 200px;
    animation: marquee-first 15s linear infinite;
    overflow: hidden;
}

@keyframes marquee-first {
    0% {
        transform: translate(0, 0);
    }

    100% {
        transform: translate(-100%, 0);
    }
}

.marquee-second span {
    display: inline-block;
    /* padding-left: 100%; */
    text-align: center;
    -webkit-text-stroke: 1px #fff;
    white-space: nowrap;
    /* min-width: 100%; */
    height: 100%;
    font-size: 130px;
    font-weight: 600;
    color: #fff;
    line-height: 200px;
    animation: marquee-second 15s linear infinite;
    overflow: hidden;
}

@keyframes marquee-second {
    0% {
        transform: translate(-100%, 0);
    }

    100% {
        transform: translate(0, 0);
    }
}

@media only screen and (max-width: 1175px) {
    .marquee-first {
        margin-top: 90px;
    }

    .marquee-second {
        margin-top: -330px;
    }

    .marquee-first span {
        font-size: 115px;
    }

    .marquee-second span {
        font-size: 115px;
    }
}

@media only screen and (max-width: 1075px) {
    .marquee-first {
        margin-top: 70px;
    }

    .marquee-second {
        margin-top: -300px;
    }
}

@media only screen and (max-width: 991px) {
    .marquee-first {
        margin-top: 50px;
    }

    .marquee-second {
        margin-top: -295px;
    }

    .marquee-first span {
        font-size: 100px;
    }

    .marquee-second span {
        font-size: 100px;
    }
}

@media only screen and (max-width: 870px) {
    .marquee-first {
        margin-top: 40px;
    }

    .marquee-second {
        margin-top: -260px;
    }

    .marquee-first span {
        font-size: 90px;
    }

    .marquee-second span {
        font-size: 90px;
    }
}

@media only screen and (max-width: 767px) {
    .marquee-first {
        margin-top: 30px;
    }

    .marquee-second {
        margin-top: -250px;
    }

    .marquee-first span {
        font-size: 80px;
    }

    .marquee-second span {
        font-size: 80px;
    }
}

@media only screen and (max-width: 700px) {
    .marquee-first {
        margin-top: 5px;
    }

    .marquee-second {
        margin-top: -230px;
    }

    .marquee-first span {
        font-size: 80px;
    }

    .marquee-second span {
        font-size: 80px;
    }
}

@media only screen and (max-width: 615px) {
    .marquee-first {
        margin-top: -10px;
    }

    .marquee-second {
        margin-top: -210px;
    }

    .marquee-first span {
        font-size: 65px;
    }

    .marquee-second span {
        font-size: 65px;
    }
}

@media only screen and (max-width: 521px) {
    .marquee-first {
        margin-top: -20px;
    }

    .marquee-second {
        margin-top: -190px;
    }

    .marquee-first span {
        font-size: 55px;
    }

    .marquee-second span {
        font-size: 55px;
    }
}

@media only screen and (max-width: 450px) {
    .marquee-first {
        margin-top: -30px;
    }

    .marquee-second {
        margin-top: -170px;
    }

    .marquee-first span {
        font-size: 40px;
    }

    .marquee-second span {
        font-size: 40px;
    }
}

@media only screen and (max-width: 367px) {
    .marquee-first {
        margin-top: -40px;
    }

    .marquee-second {
        margin-top: -160px;
    }

    .marquee-first span {
        font-size: 30px;
    }

    .marquee-second span {
        font-size: 30px;
    }
}

/* MARQUEE-Section End */
/* PAID ADS Section Start */

/* .padding-SF {
    padding-left: 190px;
    padding-right: 190px;
} */

@media only screen and (max-width: 991px) {
    .padding-SF {
        padding-left: 0px;
        padding-right: 0px;
    }
}

.PA-bg {
    position: relative;
    height: 70vh;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.PA-color {
    width: max-content;
    background: -webkit-linear-gradient(180deg, #3E5AFA 0%, #e94526 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    margin: 0 auto;
    font-weight: bold;
}

@media only screen and (max-width: 768px) {
    .PA-color {
        width: auto;
    }
}

.PA-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    row-gap: 2rem;
    column-gap: 2rem;
    margin-top: 5rem;
}

.PA-grid a {
    cursor: pointer;
    text-decoration: none;
}

.PA-grid h3 {
    color: #000;
}

.PA-grid h5 {
    color: #000;
}

.PA-grid-item {
    background-color: transparent;
    padding: 2rem;
    border-radius: 30px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-direction: column;
    height: 100%;
    box-shadow: 5px 4px 28.5px 0 rgba(0, 0, 0, 0.25);
}

.hover-first:hover {
    background-color: #F9F8FF;
}

.hover-second:hover {
    background-color: #FFFCF6;
}

.hover-third:hover {
    background-color: #F0FFFC;
}

.PA-para {
    color: #000;
    font-size: 16px;
}

.PA-light-para {
    color: #FFF;
    font-size: 16px;
}

@media only screen and (max-width: 991px) {
    .PA-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media only screen and (max-width: 768px) {
    .PA-grid {
        grid-template-columns: repeat(1, 1fr);
    }
}


/* PAID ADS Section End */
/* Social Media Ads Start */
.SMA-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    row-gap: 1rem;
    column-gap: 1rem;
    margin-top: 5rem;
}

.SMA-grid-item {
    background-color: #FFF;
    padding: 2rem;
    border-radius: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    box-shadow: 5px 4px 28.5px -15px rgba(0, 0, 0, 0.25);
}

@media only screen and (max-width: 991px) {
    .SMA-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .Empty-grid-item {
        display: none;
    }
}

@media only screen and (max-width: 768px) {
    .SMA-grid {
        grid-template-columns: repeat(1, 1fr);
    }
}

.SMads-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    row-gap: 2rem;
    column-gap: 2rem;
    margin-top: 5rem;
}

.SMads-grid-item {
    /* background-color: #FFF; */
    padding: 2rem;
    border-radius: 30px;
    display: flex;
    align-items: center;
    flex-direction: column;
    box-shadow: 5px 4px 28.5px 0 rgba(0, 0, 0, 0.25);
}

.hover-first:hover {
    background-color: #F9F8FF;
}

.hover-second:hover {
    background-color: #FFFCF6;
}

.hover-third:hover {
    background-color: #F0FFFC;
}

@media only screen and (max-width: 991px) {
    .SMads-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media only screen and (max-width: 768px) {
    .SMads-grid {
        grid-template-columns: repeat(1, 1fr);
    }
}

/* Social Media Ads End */
/* YouTube Ads Start */

.carousel-indicators [data-bs-target] {
    background-color: #FFF !important;
    width: 10px;
    height: 10px;
    border-radius: 50%;
}

.carousel-indicators {
    position: absolute;
    right: 30px;
    bottom: 0;
    left: 0;
    top: 300px;
    z-index: 2;
    display: flex;
    justify-content: center;
    padding: 0;
    margin-right: 15%;
    margin-bottom: 1rem;
    margin-left: 15%;
    margin-top: 10rem;
}

@media only screen and (max-width: 1399px) {
    .carousel-indicators {
        margin-top: 7rem;
    }
}

@media only screen and (max-width: 1199px) {
    .carousel-indicators {
        margin-top: 2rem;
    }
}

@media only screen and (max-width: 991px) {
    .carousel-indicators {
        margin-top: -3rem;
    }
}

@media only screen and (max-width: 767px) {
    .carousel-indicators {
        margin-top: -7rem;
    }
}

@media only screen and (max-width: 575px) {
    .carousel-indicators {
        margin-top: 4rem;
    }
}

@media only screen and (max-width: 500px) {
    .carousel-indicators {
        margin-top: 0rem;
    }
}

@media only screen and (max-width: 430px) {
    .carousel-indicators {
        margin-top: -2rem;
    }
}

@media only screen and (max-width: 380px) {
    .carousel-indicators {
        margin-top: -4rem;
    }
}

@media only screen and (max-width: 340px) {
    .carousel-indicators {
        margin-top: -5rem;
    }
}

@media only screen and (max-width: 575px) {
    .YT-ads-carousel {
        display: none;
    }
}

/* YouTube Ads End */
/* Social Media Management Start */

.SMM-color {
    width: max-content;
    background: -webkit-linear-gradient(180deg, #3E5AFA 0%, #e94526 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

/* Social Media Management End */
/* Search Engine Start */

.SEO-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    row-gap: 2rem;
    column-gap: 2rem;
}

.SEO-grid-item {
    padding: 1rem;
    border-radius: 30px;
    display: flex;
    align-items: flex-start;
    flex-direction: column;
}

.SEOads-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    margin-top: 5rem;
}

.SEOads-grid-item {
    padding: 1rem;
    display: flex;
    align-items: flex-start;
    flex-direction: column;
}

.SEO-color {
    background: -webkit-linear-gradient(180deg, #3E5AFA 0%, #e94526 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

@media only screen and (max-width: 991px) {
    .SEO-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .SEOads-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media only screen and (max-width: 768px) {
    .SEO-grid {
        grid-template-columns: repeat(1, 1fr);
    }

    .SEOads-grid {
        grid-template-columns: repeat(1, 1fr);
    }

    .row-flex-grid {
        flex-direction: column-reverse;
    }
}

/* Search Engine End */
/* Blog Content Writing Start */

.BCW-heading {
    font-size: 3rem;
}

.BCW-flex {
    background-color: #FFF;
    padding: 2rem 4rem;
    border-radius: 30px;
    display: flex;
    align-items: center;
    gap: 3rem;
    box-shadow: 5px 4px 28.5px 0 rgba(0, 0, 0, 0.25);
}

.BCW-flex img {
    width: 120px;
    height: 120px;
}

@media only screen and (max-width: 991px) {
    .BCW-flex {
        flex-direction: column;
    }
}

@media only screen and (max-width: 991px) {
    .BCW-flex {
        padding: 2rem 0.5rem;
    }
}

/* Blog Content Writing End */
/* Development Start */

.DEV-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    row-gap: 2rem;
    column-gap: 2rem;
    margin-top: 5rem;
    margin-left: 15%;
    margin-right: 15%;
}

.DEV-grid a {
    cursor: pointer;
    text-decoration: none;
}

.DEV-grid h3 {
    color: #000;
}

.DEV-grid h5 {
    color: #000;
}

.circle-icon-pink {
    display: flex;
    width: 80px;
    height: 80px;
    background-color: #FAD1DD;
    border-radius: 50%;
    margin: 0 auto;
    align-items: center;
    justify-content: center;
}

.circle-icon-yellow {
    display: flex;
    width: 80px;
    height: 80px;
    background-color: #FFE8B5;
    border-radius: 50%;
    margin: 0 auto;
    align-items: center;
    justify-content: center;
}

.circle-icon-blue {
    display: flex;
    width: 80px;
    height: 80px;
    background-color: #BBE5F2;
    border-radius: 50%;
    margin: 0 auto;
    align-items: center;
    justify-content: center;
}

.circle-icon-green {
    display: flex;
    width: 80px;
    height: 80px;
    background-color: #c9fff4;
    border-radius: 50%;
    margin: 0 auto;
    align-items: center;
    justify-content: center;
}

.circle-icon-purple {
    display: flex;
    width: 80px;
    height: 80px;
    background-color: #E7C0DA;
    border-radius: 50%;
    margin: 0 auto;
    align-items: center;
    justify-content: center;
}

@media only screen and (max-width: 991px) {
    .DEV-grid {
        grid-template-columns: repeat(2, 1fr);
        margin-left: 0%;
        margin-right: 0%;
    }
}

@media only screen and (max-width: 768px) {
    .DEV-grid {
        grid-template-columns: repeat(1, 1fr);
    }
}

/* Development End */
/* Development Web Start */
.WD-right-para {
    background-color: #FFF;
    padding: 2rem;
    border-radius: 30px;
    margin-left: -80px;
    box-shadow: 3px 5px 14px 0px rgba(0, 0, 0, 0.1);
}

.WD-left-para {
    background-color: #FFF;
    padding: 2rem;
    border-radius: 30px;
    margin-right: -80px;
    box-shadow: 3px 5px 14px 0px rgba(0, 0, 0, 0.1);
}

.WD-color-para {
    color: #65676b;
    font-size: 1em;
}

@media only screen and (max-width: 991px) {
    .WD-right-left {
        flex-direction: column;
        gap: 1.5rem;
    }

    .WD-right-para {
        margin-left: 0;
    }

    .WD-left-para {
        margin-right: 0;
    }

    .WD-gap {
        gap: 1.5rem;
    }
}

/* Development Web End */
/* Mobile App Development Start */

.MD-img {
    width: 100px;
    height: 100px;
}

.MD-heading {
    line-height: 1.3em;
    font-size: 2em;
}

.MD-light-heading {
    font-size: 36px;
    line-height: 1.6;
    color: #FFF;
}

.HP-grid-item:hover {
    background-color: #f4f5fa;
}

/* .MD-LS-heading {
    line-height: 1.3em;
    font-size: 2em;
} */

.MD-img-size {
    width: 60px;
    height: 60px;
}

.MAD-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    row-gap: 2rem;
    column-gap: 2rem;
    margin-top: 5rem;
}

.MAD-grid-item {
    background-color: #ffffff;
    padding: 2rem;
    border-radius: 30px;
    display: flex;
    align-items: center;
    flex-direction: column;
    margin-bottom: 8px;
    box-shadow: 3px 5px 14px 0px rgba(0, 0, 0, 0.1);
}

.MAD-grid-item:hover {
    margin-bottom: 0px;
    margin-top: 8px;
    transition: ease-in-out 0.4s;
}

@media only screen and (max-width: 991px) {
    .MAD-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media only screen and (max-width: 768px) {
    .MAD-grid {
        grid-template-columns: repeat(1, 1fr);
    }
}

.MD-grid {
    display: grid;
    grid-template-columns: repeat(6, 1fr);
    row-gap: 2rem;
    column-gap: 2rem;
    margin-top: 5rem;
}

.MD-grid-item {
    background-color: #0f0f11;
    padding: 2rem;
    border-radius: 30px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-direction: column;
    height: 100%;
    gap: 0.3rem;
    margin-bottom: 10px;
}

.MD-grid-item:hover {
    margin-bottom: 0px;
    margin-top: 10px;
    transition: ease-in-out 0.3s;
}

@media only screen and (max-width: 991px) {
    .MD-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media only screen and (max-width: 768px) {
    .MD-grid {
        grid-template-columns: repeat(1, 1fr);
    }
}

.MD-tech-grid {
    display: grid;
    grid-template-columns: repeat(1, 1fr);
    row-gap: 2rem;
    margin-top: 5rem;
}

.MD-tech {
    background-color: #FFF;
    padding: 2rem;
    border-radius: 30px;
    justify-content: space-between;
    height: 100%;
    margin-bottom: 10px;
    box-shadow: 3px 5px 14px 0px rgba(0, 0, 0, 0.1);
}

@media only screen and (max-width: 450px) {
    .MD-tech {
        padding: 2rem 0rem;
    }
}

.MD-tech:hover {
    margin-bottom: 0px;
    margin-top: 10px;
    transition: ease-in-out 0.3s;
}

.MD-arrow-content {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    color: #FFF;
    gap: 2.5rem;
}

.MD-LS-img {
    width: 125px;
}

.arrow-content ul li {
    color: #65676b;
}

/* Mobile App Development End */
/* About Us Page Start */
.AU-bg {
    position: relative;
    height: 50vh;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0.5;
}

.AU-bg::before {
    content: "";
    background-repeat: no-repeat;
    background-size: cover;
    position: absolute;
    top: 0px;
    right: 0px;
    bottom: 0px;
    left: 0px;
}

.AU-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    row-gap: 2rem;
    column-gap: 2rem;
}

.AU-grid-item {
    background-color: #FFF;
    padding: 2rem;
    border-radius: 30px;
    display: flex;
    align-items: flex-start;
    flex-direction: column;
    height: 100%;
    gap: 0.3rem;
    box-shadow: 3px 5px 14px 0px rgba(0, 0, 0, 0.1);
}

.AU-grid-item:hover {
    background-color: #f4f5fa;
}

.AU-OP-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    row-gap: 2rem;
    column-gap: 2rem;
}

.AU-OP-grid-item {
    padding: 2rem;
    border-radius: 30px;
    display: flex;
    align-items: center;
    flex-direction: column;
    height: 100%;
    gap: 0.3rem;
}

@media only screen and (max-width: 991px) {
    .AU-OP-grid {
        grid-template-columns: repeat(1, 1fr);
        row-gap: 0rem;
    }

    .AU-grid {
        grid-template-columns: repeat(1, 1fr);
    }
}

.AU-btn {
    color: #FFF;
    border-radius: 10px;
    border: none;
    background: #3d3d3d;
}

.AU-padding {
    padding-left: 190px;
    padding-right: 190px;
}

@media only screen and (max-width: 991px) {
    .AU-padding {
        padding-left: 0px;
        padding-right: 0px;
    }
}

/* About Us Page End */
/* Blog Page Start */

.blog-color-heading {
    width: auto;
    background: -webkit-linear-gradient(180deg, #3E5AFA 0%, #e94526 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    font-weight: bold;
}

@media only screen and (max-width: 1024px) {
    .px-small-pad {
        padding-left: 0.5rem !important;
        padding-right: 0.5rem !important;
    }
}

.bg-blog {
    background: #0F0F11;
}

/* Blog Page End */
/* Contact Page Start */

.CU-color {
    font-size: 1.5rem;
    width: max-content;
    background: -webkit-linear-gradient(180deg, #3E5AFA 0%, #e94526 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    margin: 0 auto;
    font-weight: bold;
}

.CU-heading {
    line-height: 1.3em;
    font-size: 3em;
}

/* Contact Page End */
/* privacypolicy Page Start */

.heading-pp {
    font-size: 30px;
    line-height: 42px;
    font-weight: bolder;
}

/* privacypolicy Page End */
/* Help Page Start */

/* .Help-para {
    font-size: 19px;
    color: #fff;
} */

.HELP_first-container {
    box-shadow: 0 10px 100px 0 rgba(40, 47, 98, 0.08);
}

.horizontal {
    width: 100%;
    height: 0px;
    border: 1px solid #2571BE;
}

.HELP-links a {
    text-decoration: none !important;
    color: #FFF;
}

.HELP-create {
    border: solid 1px rgb(102, 102, 251);
}

.HELP-buy {
    border: solid 1px rgb(102, 102, 251);
}

.HELP-links a:hover {
    color: #FFF;
}

.HELP-btn {
    border: solid 1px rgb(102, 102, 251);
}

.HELP-right-head {
    border-radius: 50%;
    background-color: #2571BE;
    width: 40px;
    height: 40px;
    color: #FFF;
}

.HELP-choose-file {
    font-size: 15px;
}

.model-h-w {
    min-width: 575px;
    height: 700px;
    margin-left: -45px;
}

.radio-w {
    margin-left: 88px;
}

.HELP-container-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    row-gap: 2rem;
    column-gap: 2rem;
}

@media only screen and (max-width: 991px) {
    .HELP-container-grid {
        grid-template-columns: repeat(1, 1fr);
    }
}

@media only screen and (min-width: 351px) {
    .Help-top-btn .margin-left {
        margin-left: 1rem;
    }
}

@media only screen and (max-width: 767px) {
    .model-h-w {
        margin-left: -40px;
        min-width: 585px;
        height: 700px;
    }
}

@media only screen and (max-width: 575px) {
    .model-h-w {
        min-width: 200px;
        height: 900px;
        margin: 2px;
    }

    .f-col {
        flex-direction: column;
    }

}

@media only screen and (max-width: 350px) {
    .Help-top-btn {
        flex-direction: column;
        gap: 1rem;
    }

    .Help-top-btn button {
        margin: auto;
    }
}

.TYC-pad {
    padding: 0px;
    border-radius: 3rem 0 3rem 0;
}

.TYC-bg {
    /* background-color: #0f0f11; */
    padding: 2rem;
    /* border-radius: 30px; */
    display: flex;
    align-items: flex-start;
    justify-content: center;
}

@media only screen and (max-width: 360px) {
    .TYC-bg {
        padding: 0.8rem;
    }
}

/* Help Page End */
/* Price Section Start */
.price-item1 {
    padding-top: 27px;
    margin-top: 20px !important;
    padding-bottom: 27px;
    padding-left: 23px;
    padding-right: 23px;
    border-radius: 16px;
    background: #F9F8FF;
}

.price-item2 {
    padding-top: 27px;
    margin-top: 20px !important;
    padding-bottom: 27px;
    padding-left: 23px;
    padding-right: 23px;
    border-radius: 16px;
    background: #FFFCF6;
}

.price-item3 {
    padding-top: 27px;
    margin-top: 20px !important;
    padding-bottom: 27px;
    padding-left: 23px;
    padding-right: 23px;
    border-radius: 16px;
    background: #F0FFFC;
}

.price-btn {
    border-radius: 5px;
    background: blue;
    color: #FFF;
    font-weight: 600;
}

.meta-btn {
    border-radius: 5px;
    background-image: linear-gradient(90deg, #9289f1 0%, #6254e7 100%);
    color: #FFF;
    font-weight: 600;
}

.meta-btn:hover {
    border-radius: 5px;
    border: 1px solid #6254e7;
    background: #FFF;
    color: #6254e7;
    transition: all .3s;
}

.linkedin-btn {
    border-radius: 5px;
    background-image: linear-gradient(90deg, #f0ac0e 0%, #f47015 100%);
    color: #FFF;
    font-weight: 600;
}

.linkedin-btn:hover {
    border-radius: 5px;
    border: 1px solid #f47015;
    background: #FFF;
    color: #f47015;
    transition: all .6s;
}

.twitter-btn {
    border-radius: 5px;
    background-image: linear-gradient(90deg, #88fdb9 0%, #34c478 100%);
    color: #FFF;
    font-weight: 600;
}

.twitter-btn:hover {
    border-radius: 5px;
    border: 1px solid #34c478;
    background: #FFF;
    color: #34c478;
    transition: all .6s;
}

.basic-btn {
    border-radius: 5px;
    /* background: #9582ff; */
    background-image: linear-gradient(90deg, #9289f1 0%, #6254e7 100%);
    color: #FFF;
    font-weight: 600;
}

.basic-btn:hover {
    border-radius: 5px;
    border: 1px solid #6254e7;
    background: #FFF;
    color: #6254e7;
    transition: all .6s;
}

.advanced-btn {
    border-radius: 5px;
    /* background: #fbb528; */
    background-image: linear-gradient(90deg, #f0ac0e 0%, #f47015 100%);
    color: #FFF;
    font-weight: 600;
}

.advanced-btn:hover {
    border-radius: 5px;
    border: 1px solid #f47015;
    background: #FFF;
    color: #f47015;
    transition: all .3s;
}

.premium-btn {
    border-radius: 5px;
    background-image: linear-gradient(90deg, #88fdb9 0%, #34c478 100%);
    color: #FFF;
    font-weight: 600;
}

.premium-btn:hover {
    border-radius: 5px;
    border: 1px solid #34c478;
    background: #FFF;
    color: #34c478;
    transition: all .3s;
}

.color-li {
    color: #65676b;
    font-size: 1.3em;
}

.price-list {
    font-size: 14px;
}

.price-listing {
    list-style-type: none;
    padding-left: 0;
}

.most-popular-btn {
    border-radius: 14px 14px 14px 0px;
    background: linear-gradient(90deg, #e94526 0%, #3E5AFA 100%);
    color: white;
    border: none;
    position: absolute;
    z-index: -1;
    margin-top: -67px;
    margin-left: -23px;
    font-size: larger;
}

.price-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
}

@media only screen and (max-width: 991px) {
    .price-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media only screen and (max-width: 768px) {
    .price-grid {
        grid-template-columns: repeat(2, 1fr);
        row-gap: 2.5rem;
    }
}

@media only screen and (max-width: 566px) {
    .price-grid {
        grid-template-columns: repeat(1, 1fr);
        row-gap: 2.5rem;
    }
}

/* Price Section End */
/* FAQ's STARTS CSS  */

/* FAQ'S SECTION  */

.faq-accordion .faq-heading {
    color: #000;
    font-weight: 400;
    font-size: 30px;
}

.faq-heading {
    line-height: 30px;
    color: #3c3c3c;
    align-items: start;
}

.faq-body p {
    line-height: 24px;
    padding: 10px 20px 10px 20px;
    margin-top: 2px;
}

.faq-body div {
    color: #5f6880;
    line-height: 24px;
    /* padding: 20px; */
    margin-top: 2px;
}

.faq-accordion {
    display: flex;
    flex-direction: column;
}

.faq-accordion button {
    text-decoration: none !important;
}

.faq-btn:first-child:active,
:not(.faq-btn-check)+.faq-btn:active {
    background-color: rgba(15, 17, 15, 1) !important;
}

.faq-accordion a {
    text-decoration: none !important;
}

.faq-card-header {
    display: flex;
    color: #717171;
    cursor: pointer;
    align-items: self-start;
    padding: 20px;
}

/* .card-body {
    padding: 0 !important;
} */

.faq-para {
    color: #777777;
    box-shadow: 3px 5px 14px 0px rgba(0, 0, 0, 0.1);
}

/* .text-dark-color {
    color: #777777;
} */

.btn[aria-expanded="true"] {
    background-color: rgba(15, 17, 15, 1);
}

.btn[aria-expanded="true"] .faq-heading {
    color: #fff;
}

.btn[aria-expanded="true"]:hover {
    background-color: rgba(15, 17, 15, 1);
}

.btn[aria-expanded="true"] .faq-card-header {
    background-color: transparent;
}

/* .collapse [aria-expanded="true"] {
    animation: fade-in 1.8s;
  } */
@keyframes fade-in {
    from {
        opacity: 0;
    }

    to {
        opacity: 1;
    }
}

.bg-faq {
    background-color: #000;
}

.btn-check:focus+.btn,
.btn:focus {
    outline: 0;
    box-shadow: none !important;
}

.navbar-toggler:focus {
    text-decoration: none;
    outline: 0;
    box-shadow: none !important;
}

@media only screen and (max-width: 320px) {
    .faq-card-header {
        display: flex;
        color: #717171;
        border-radius: 12px;
        cursor: pointer;
        align-items: self-start;
        padding: 10px;
    }

    .grid-item {
        padding: 0px;
    }

    .container1 {
        padding: 15px;
        width: 290px !important;
    }
}

/* FAQ's ENDS CSS  */

/* contact form  */
.email-div {
    background: #ffffff;
    border-radius: 20px;
    width: 35vw;
}

.email-center {
    align-items: center;
}

.bg-light-blue {
    background: #e0efff3b;
}

.img-h-500 {
    height: 500px;
}

/* contact text-flelds  */


.input-form-style {
    width: 100%;
    padding: 12px;
    border: 1px solid #ccc;
    border-radius: 8px;
    border-style: none none solid none;
    background: #f0f0f000;
    box-sizing: border-box;
    margin-top: 6px;
    margin-bottom: 16px;
    resize: vertical;
    outline: none !important;

}

.container1 {
    border-radius: 20px;
    /* background-color: #0F0F11; */
    padding: 60px;
    border: 1px solid #CACACA;
    width: 645px;
}

@media screen and (max-width: 768px) {
    .container1 {
        padding: 36px;
        width: 450px;
    }
}

@media only screen and (max-width: 320px) {
    .container1 {
        padding: 15px;
        width: 290px !important;
    }
}

@media only screen and (max-width: 425px) {
    .container1 {
        padding: 15px;
        width: 330px;
    }
}

.fly-img {
    background-repeat: no-repeat;
    background-size: 200px;
    background-position: 88% 50%;
}

@media only screen and (max-width: 1399px) {
    .fly-img {
        background-position: 95% 50%;
    }
}

@media only screen and (max-width: 1199px) {
    .fly-img {
        background-position: 105% 50%;
    }
}

@media only screen and (max-width: 991px) {
    .fly-img {
        background-size: 0px;
        background-position: 109% 50%;
    }
}

/* .text-warning {
    color: red !important;
} */

input::placeholder {
    color: #938d8d;
    opacity: 1;
}

:focus {
    outline: 0px solid transparent !important;
}

textarea {
    border: none;
    border-bottom: 1px solid #938d8d;
    outline: none;
    transition: border-bottom 0.3s;
}

textarea::placeholder {
    color: #938d8d;
}

select:focus {
    outline: none;
}

.contact-info {
    font-size: 18px;
}

@media only screen and (max-width: 1399px) {
    .contact-info {
        font-size: 18px;
    }
}

@media only screen and (max-width: 1199px) {
    .contact-info {
        font-size: 18px;
    }
}

@media only screen and (max-width: 991px) {

    .contact-info {
        font-size: 18px;
    }
}

/* contact text-flelds  */
/* divider-circle CSS START */


.divider-circle {
    background-color: #ff7426;
    width: 6px;
    height: 6px;
    display: inline-block;
    vertical-align: middle;
    margin-left: 10px;
    margin-right: 10px;
    border-radius: 50%;
}

/* divider-circle CSS END */