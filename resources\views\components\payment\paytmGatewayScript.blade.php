@if ($hostdata->paytm_merchant_mid && $hostdata->paytm_merchant_key)
<script>
    $('#paytmPopUpBtn').on('click', function() {
        $('#paymentLoader').show();
        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        });
        $.ajax({
            url: "{{ route('paytmPopUp') }}",
            method: "post",
            data: {
                paymentKey: "{{ $str }}"
            },
            success: function(response) {

                // console.log(response);
                if (response.status == true) {
                    let src = 'https://secure.paytmpayments.com/merchantpgpui/checkoutjs/merchants/' +
                        response.paytm_merchant_mid + '.js';

                    function onScriptLoad() {
                        var config = {
                            "root": "",
                            "flow": "DEFAULT",
                            "data": {
                                "orderId": response.orderId,
                                "token": response.paytmTxnToken,
                                "tokenType": "TXN_TOKEN",
                                "amount": response.amount,
                            },
                            "handler": {
                                "notifyMerchant": function(eventName, data) {
                                    console.log("notifyMerchant handler function called");
                                    console.log("eventName => ", eventName);
                                    console.log("data => ", data);
                                }
                            }
                        };
                        if (window.Paytm && window.Paytm.CheckoutJS) {
                            window.Paytm.CheckoutJS.onLoad(function excecuteAfterCompleteLoad() {
                                window.Paytm.CheckoutJS.init(config).then(
                                    function onSuccess() {
                                        window.Paytm.CheckoutJS.invoke();
                                    }).catch(function onError(error) {
                                    console.log("error => ", error);
                                });
                            });
                        }
                    }
                    $.getScript(src)
                        .done(function(script, textStatus) {
                            onScriptLoad();
                        })
                        .fail(function(jqxhr, settings, exception) {
                            console.log("Script load failed: " + exception);
                        });
                } else {
                    $('#paymentLoader').hide();
                    alert('{{ ErrMsg() }}')
                }
                $('#paymentLoader').hide();
            },
            error: function(res) {
                $('#paymentLoader').hide();
                alert('{{ ErrMsg() }}')
            }
        });
    });
</script>
@endif