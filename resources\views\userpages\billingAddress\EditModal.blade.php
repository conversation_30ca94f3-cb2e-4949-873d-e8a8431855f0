<div class="modal fade" id="billingProfileEdit" tabindex="-1" aria-labelledby="billingProfileEdit" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <div class="">
                    <h4 class="heading-font">
                        Edit Billing Profile
                    </h4>
                </div>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form action="{{ route('billingAddress.modal.update') }}" method="post">
                    @csrf
                    <input type="hidden" name="address_id" id="id" value="{{ old('address_id') }}" />
                    <div class="row mb-3">
                        <div class="col-lg-6">
                            <label class="form-label support-label">Name<span style="color: red;">*</span> </label>
                            <div class="mb-3">
                                <input type="text" name="name" class="form-control fieldInput " id="name"
                                    placeholder="Name" value="{{ old('name') }}">
                                @error('name')
                                    <div class="text-danger">{{ $message }}</div>
                                @enderror
                            </div>

                            <label class="form-label support-label">GST No.</label>
                            <div class="mb-3">
                                <input type="text" name="gst" class="form-control fieldInput" id="gst"
                                    placeholder="Ex: 08AAACH0000R0Z0" value="{{ old('gst') }}">
                                @error('gst')
                                    <div class="text-danger">{{ $message }}</div>
                                @enderror
                            </div>

                            <label class="form-label support-label">Business Name</label>
                            <div class="mb-3">
                                <input type="text" name="company" class="form-control fieldInput"
                                    placeholder="Business Name" id="company" value="{{ old('company') }}">
                                @error('company')
                                    <div class="text-danger">{{ $message }}</div>
                                @enderror
                            </div>

                            <label class="form-label support-label">Country</label>
                            <div class="mb-3">
                                <input type="text" name="country" class="form-control fieldInput" id="country"
                                    placeholder="Country" value="{{ old('country') }}">
                                @error('country')
                                    <div class="text-danger">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="d-flex gap-4 mt-2">

                                <label class="form-label support-label">Make This Address Default </label>
                                <div class="">
                                    <input class="form-check-label form-check-input form-select-lg" name="isPrimary"
                                        type="checkbox" value="" id="addressPrimaryCheck">

                                    @error('isPrimary')
                                        <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6">
                            <label class="form-label support-label">PIN/ZIP Code</label>
                            <div class="mb-3">
                                <input type="text" name="pinCode" class="form-control fieldInput" placeholder="Pin"
                                    id="pincode" value="{{ old('pincode') }}">
                                @error('pinCode')
                                    <div class="text-danger">{{ $message }}</div>
                                @enderror
                            </div>

                            <label class="form-label support-label">City</label>
                            <div class="mb-3">
                                <input type="text" name="city" class="form-control fieldInput" placeholder="City"
                                    id="city" value="{{ old('city') }}">
                                @error('city')
                                    <div class="text-danger">{{ $message }}</div>
                                @enderror
                            </div>

                            <label class="form-label support-label">State</label>
                            <div class="mb-3">
                                <input type="text" name="state" class="form-control fieldInput" placeholder="State"
                                    id="state" value="{{ old('pincode') }}">
                                @error('pincode')
                                    <div class="text-danger">{{ $message }}</div>
                                @enderror
                            </div>



                            <label class="form-label support-label">Billing Address</label>
                            <div class="mb-3">
                                <textarea class="form-control fieldInput" name="address" placeholder="Dunes Factory Pvt. Ltd., Bikaner"
                                    rows="3" id="address"> {{ old('address') }}" </textarea>
                                @error('address')
                                    <div class="text-danger">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="align-items-center justify-content-end gap-3 mt-5 d-flex">
                                <button type="button" class="rounded-pill ps-4 pe-4 pt-1 pb-1 text-white"
                                    data-bs-dismiss="modal" aria-label="Close"
                                    style="background-color: #DE0505;border: 2px solid #DE0505;">
                                    Cancel
                                </button>
                                <button
                                    class="rounded-pill text-white ps-4 pe-4 pt-1 pb-1 d-flex align-items-center gap-3 btn-bg-blue">
                                    Update
                                    <i class="fa-solid fa-arrow-right text-white"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                    @include('components.core.modal-alerts')
                </form>
            </div>
        </div>
    </div>
</div>



@if (session('isEditModalShow') == true)
    <script>
        $(document).ready(function() {
            new bootstrap.Modal($('#billingProfileEdit')).show()
        });
    </script>
@endif


<script>
    $('.billingProfileEditBtn').on("click", function() {
        let btn = $(this).val();
        $.ajax({
            url: window.location.origin + "/billingAddress/" + btn + "/edit",
            method: "GET",
            data: {
                id: btn
            },
            dataType: 'json',
            success: function(response) {
                console.log("response: ", response.address);
                $('#name').val(response.address.name);
                $('#gst').val(response.address.gst);
                $('#company').val(response.address.company);
                $('#pincode').val(response.address.pincode);
                $('#city').val(response.address.city);
                $('#state').val(response.address.state);
                $('#country').val(response.address.country);
                $('#address').text(response.address.address);
                $('#id').val(response.id);

                $('#addressPrimaryCheck').prop('checked', response.address.isPrimary == 1);

                $('#billingProfileEdit').modal('show');
            },
        });
    })
</script>
