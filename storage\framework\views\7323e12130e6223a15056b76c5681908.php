<!-- Modal -->
<div class="modal fade" id="getquoteModal" tabindex="-1" aria-labelledby="getquoteModal" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h1 class="modal-title fs-5" id="getquoteModalHead">Get Quotation</h1>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form action="<?php echo e(route('quotation.store.modal')); ?>" method="POST" enctype="multipart/form-data">
                    <?php echo csrf_field(); ?>
                    <!--begin::Row-->
                    <div class="row p-4">
                        <h2 class="text-center text-gray-800 ">What is your budget? <span
                                class="fs-1 text-danger"><sup>*</sup></span></h2>
                    </div>
                    <!--end::Row-->
                    <?php
                        $budget = [
                            ['value' => 50000, 'name' => '0 - 50k '],
                            ['value' => 500000, 'name' => '50k - 5L'],
                            ['value' => 100000, 'name' => '5L - 10L'],
                            ['value' => 1, 'name' => 'Not Sure'],
                        ];
                    ?>
                    <!--begin::Row-->
                    <div class="row mb-3 w-100 justify-content-lg-between m-0">
                        <?php $__currentLoopData = $budget; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $list): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <!--begin::Col-->
                            <div class="col-lg-6 col-md-6  p-2">
                                <label
                                    class="radio-container  d-flex justify-content-center rounded-2  bg-light-gray  w-100 h-100 <?php if($loop->first): ?> <?php echo e('active-radio'); ?> <?php endif; ?>">
                                    <div class="text-center d-flex gap-3">
                                        <div class="d-flex align-self-center">
                                            <input class="form-check-input budget-radio m-0" type="radio"
                                                <?php if($loop->first): ?> <?php echo e('checked'); ?> <?php endif; ?>
                                                value="<?php echo e($list['value']); ?>" name="budget" required />
                                        </div>
                                        <div class=" ">
                                            <h4 class=" m-1">₹
                                                <?php echo e($list['name']); ?>

                                            </h4>

                                        </div>
                                    </div>
                                </label>
                            </div>
                            <!--end::Col-->
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                    </div>
                    <!--end::Row-->
                    <!--begin::Row-->
                    <div class="row mb-3 w-100 justify-content-lg-between m-0">
                        <div class=" ">

                            <div class="p-3 ">
                                <!--begin::Row-->
                                <div class="row mb-3">
                                    <div class="col-lg-6 col-md-12 col-sm-12 mb-3">
                                        <!--begin::Label-->
                                        <?php
                                            $modeOfCCommunications = [
                                                [
                                                    'name' => 'Call',
                                                    'value' => 0,
                                                ],
                                                [
                                                    'name' => 'E-Mail',
                                                    'value' => 1,
                                                ],
                                                [
                                                    'name' => 'Whatsapp',
                                                    'value' => 2,
                                                ],
                                            ];
                                        ?>
                                        <label class="form-label">Preferred Mode of Contact <span
                                                class="text-danger">*</span></label>
                                        <div class="row">
                                            <?php $__currentLoopData = $modeOfCCommunications; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $mode): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <div class="col">

                                                <div class="form-check  d-flex align-items-center">
                                                    <input class="form-check-input" type="radio"
                                                    name="preferredModeOfContact"
                                                        <?php if(old('preferredModeOfContact') == $mode['value'] || $loop->first): ?> <?php echo e('checked'); ?> <?php endif; ?>
                                                        id="<?php echo e($mode['name']); ?>" value="<?php echo e($mode['value']); ?>">
                                                    <label class="form-check-label ps-1 pt-1" for="<?php echo e($mode['name']); ?>">
                                                        <?php echo e($mode['name']); ?>

                                                    </label>
                                                </div>
                                                </div>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </div>
                                        <!--end::Row-->
                                    </div>

                                    <div class="col-lg-6 col-md-12 col-sm-12 mb-3">
                                        <!--begin::Label-->
                                        <?php
                                            $preferredTimes = [
                                                ['name' => 'Any Time', 'value' => 0],
                                                ['name' => 'Morning', 'value' => 1],
                                                ['name' => 'Afternoon', 'value' => 2],
                                                ['name' => 'Evening', 'value' => 3],
                                            ];
                                        ?>

                                        <label class="form-label">Preferred Time <span
                                                class="text-danger">*</span></label>
                                        <div class="row p-1">
                                            <?php $__currentLoopData = $preferredTimes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $time): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <div class="col">

                                                <div class="form-check d-flex align-items-center">
                                                    <input class="form-check-input" type="radio" name="preferredTime"
                                                    <?php if(old('preferredTime') == 0 || $loop->first): ?> <?php echo e('checked'); ?> <?php endif; ?>
                                                    id="<?php echo e($time['name']); ?>" value="<?php echo e($time['value']); ?>">
                                                    <label class="form-check-label ps-1 pt-1 text-nowrap"
                                                    for="<?php echo e($time['name']); ?>">
                                                    <?php echo e($time['name']); ?>

                                                </label>
                                            </div>
                                                </div>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </div>
                                        <!--end::Row-->
                                    </div>
                                </div>
                                <div class="row mb-3">
                                    <!--begin::solid autosize textarea-->
                                    <div class="col">

                                        <label>
                                            <h4 class="">
                                                Details of
                                                Requirement <span class="text-danger"><sup>*</sup></span>
                                            </h4>
                                        </label>
                                        <textarea cols="8" placeholder="Details ..." rows="5" name="details" required
                                            class="form-control form-control form-control-solid " data-kt-autosize="true"></textarea>
                                    </div>
                                    <!--end::solid autosize textarea-->

                                </div>
                                <div class="row mb-3">
                                    <div class="col">
                                        <div class="form-group">
                                            <label for="" class="form-label fw-bolder ">
                                                <h4> Attachment</h4>
                                            </label>
                                            <input id="my-input"
                                                class="form-control form-control form-control-solid  mb-2"
                                                type="file" name="attachment">
                                            <small class="text-secondary pt-3">IMAGE,ZIP,PDF.etc (Less then
                                                20
                                                MB)</small>
                                        </div>
                                    </div>
                                </div>


                                <!--end::Row-->

                            </div>
                        </div>
                    </div>
                    <!--begin::Row-->
                    <div class="row text-end">
                        <div class="col">
                            <button class="btn btn-primary px-5 py-2" type="submit">
                                Send
                            </button>
                        </div>
                    </div>
                </form>
            </div>

        </div>
    </div>
</div>
<?php /**PATH C:\Users\<USER>\Desktop\live\websites_laravel\resources\views/components/core/getQuoteModal.blade.php ENDPATH**/ ?>