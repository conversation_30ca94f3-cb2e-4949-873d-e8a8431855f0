<?php

namespace App\Models;

use App\Models\Invoice;
use App\Models\OrderItems;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Order extends Model
{
    // use Searchable;
    use HasFactory;
    protected $table = 'order';
    public $timestamps = false;

    public function orderItems()
    {
        return $this->hasMany(OrderItems::class, 'order_id', 'id')->with('product');
    }
    public function invoice()
    {
        return $this->belongsTo(Invoice::class, 'invoice_id', 'id');
    }
    // public function product()
    // {
    //     return $this->belongsTo(Invoice::class, 'invoice_id', 'id');
    // }
}
