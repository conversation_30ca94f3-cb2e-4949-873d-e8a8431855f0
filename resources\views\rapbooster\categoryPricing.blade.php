@extends('global.main')
@section('GlobalPages')
    @php
        $host = request()->getHttpHost();
    @endphp

    @if ($host == 'localhost:8001' || $host == 'wabhai.com' || $host == 'wabhai.orkia.in')
        <div class="container-fluid bg-faq pt-3 pb-3 pt-lg-0 pb-lg-0" style="margin-top: 155px;">

        </div>
    @elseif ($host == 'localhost:8002' || $host == 'primailer.com' || $host == 'primailer.orkia.in')
        <div class="container-fluid" style="padding-top: 100px">

        </div>
    @elseif ($host == 'localhost:8003' || $host == 'stickyfirst.com' || $host == 'stickyfirst.orkia.in')
        <div class="container-fluid" style="padding-top: 12px">

        </div>
    @elseif ($host == 'localhost:8004' || $host == 'ringcaster.com' || $host == 'ringcaster.orkia.in')
        <div class="container-fluid" style="background: #FFEEE7; padding-top: 5px">

        </div>
    @elseif ($host == 'localhost:8005' || $host == 'pixayogi.com' || $host == 'pixayogi.orkia.in')
        <div class="container-fluid " style="background: #282B30;padding-top: 30px">

        </div>
    @elseif ($host == 'localhost:8006' || $host == 'rokdi.com' || $host == 'rokdi.orkia.in')
        <div class="container-fluid" style="padding-top: 30px">

        </div>
    @elseif ($host == 'localhost:8007' || $host == 'androsms.com' || $host == 'androsms.orkia.in')
        <div class="container-fluid bg-color rounded-4 w-90" style="padding-top: 10px">

        </div>
    @elseif ($host == 'localhost:8008' || $host == 'clatos.com' || $host == 'clatos.orkia.in')
        <div class="container-fluid" style="padding-top: 107px">

        </div>
    @elseif ($host == 'localhost:8009' || $host == 'rapbooster.com' || $host == 'rapbooster.orkia.in')

    @elseif ($host == 'localhost:8010' || $host == 'dunesfactory.com' || $host == 'dunesfactory.orkia.in')
        <div class="container-fluid" style="padding-top: 1px;margin-top:90px">

        </div>
    @endif

    <div class="">
        {{-- @if ($category->headerImage || $category->headerText)
            <div class="container-fluid p-0">
                <div class="pt-5 pb-5"
                    style="background-image: url({{ $category->headerImage ? s3_fileShow($category->headerImage, 'product_category', '') : asset('assets/rapbooster/images/pricing_bg.png') }}); background-repeat: no-repeat; background-size: cover;">
                    <h1 class="text-center text-white h2-custom">
                        {{ $category->headerText ?? '' }}
                    </h1>
                </div>
            </div>
        @endif --}}
        @if ($category->pricingTableLayout == 1)
            <div class="my-3">
                @include('rapbooster.plans.showPlans')
            </div>
        @endif
        @if ($category->ecommerceLayout)
            <div class="container-lg container-fluid my-5">
                <div class="row row-cols-lg-3 row-cols-md-3 row-cols-1">
                    @foreach ($category->products as $product)
                        <x-product-view :product="$product" />
                    @endforeach
                </div>
            </div>
        @endif
    </div>

    @include('components.rapbooster.SEWU')
@endsection
