/*------------------------------------------------- navbar css Starts --------------------------------------------------------- */

h1 {
    font-weight: bold;
    font-size: 65px;
    line-height: 1.2;
}

@media only screen and (max-width: 425px) {
    h1 {
        font-size: 50px;
    }

    h2 {
        font-size: 45px;
    }

    h3 {
        font-size: 35px;
    }
}

h2 {
    font-weight: bold;
    font-size: 60px;
    line-height: 1.2;
}

h3 {
    font-weight: 700;
    margin: 0;
    font-size: 50px;
}

#main {
    position: relative;
}

.overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    background-image: linear-gradient(to bottom, #52eae61f 50%, #52eae6);
}

@media only screen and (max-width: 991px) {
    .horzontal-line {
        border: 1px solid #999999;
    }
}

.item-center {
    align-items: center;
}

@media only screen and (max-width: 991px) {

    .item-center {
        align-items: flex-start;
    }

}

.sticky-navbar {
    position: fixed;
    top: 30px;
    width: 100%;
    z-index: 9999;
    background-color: #ffffff;
    transition: background-color 0.3s ease-in-out;
    box-sizing: border-box;
}

.navbar-brand {
    display: flex;
    align-items: center;
}

.navbar-brand img {
    max-height: 100%;
    margin-right: 10px;
}

#navbar {
    width: 100%;
    position: relative;
    display: flex;
    align-items: center;
    position: sticky;
    top: 0;
    background-color: white;
    z-index: 1000;
    justify-content: space-between;
}

#logo img {
    height: 60px;
}


.links {
    text-decoration: none;
    font-weight: 700;
    color: #525260;
    font-size: 16px;
}

.links:hover {
    color: #168cd1;
    text-decoration: none;
}

.hover-underline-animation {
    display: inline-block;
    position: relative;
    text-decoration: none;
}

.hover-underline-animation:after {
    content: "";
    position: absolute;
    width: 100%;
    transform: scaleX(0);
    height: 2px;
    bottom: 0;
    left: 0;
    background-color: #0087ca;
    transform-origin: bottom right;
    transition: transform 0.25s ease-out;
    text-decoration: none;
}

.hover-underline-animation:hover:after {
    transform: scaleX(1);
    transform-origin: bottom left;
}

.hover-underline-animation:active {
    color: #0087ca;
}

.dropdown-content {
    display: none;
    top: 36px;
    left: -93px;
    position: absolute;
    background-color: white;
    right: 0;
    min-width: 250px;
    box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.2);
    z-index: 1;
    padding: 15px 10px;
    border-radius: 15px;
}

@media only screen and (max-width: 991px) {
    .dropdown-content {
        left: 0px;
        width: fit-content;
    }
}

.dropdown-content a {
    color: #525260;
    font-weight: 500;
    padding: 5px 16px;
    text-decoration: none;
    display: block;
}

.dropdown-content a:hover {
    color: #168cd1;
}

.dropdown:hover .dropdown-content {
    display: block;
}

.nav_button {
    background-color: #44c9f5;
    padding: 11px 23px;
    /* font-size: 13px; */
    font-weight: 600;

}

.nav_button:hover {
    background: linear-gradient(to bottom, #44c9f5, #78ebff);
}

.header-div>.header-ul {
    gap: 1.5rem !important;
}

@media only screen and (max-width: 1200px) {
    .header-div>.header-ul {
        gap: 0.8rem !important;
    }
}

.header-nav>.header-nav-text {
    font-size: 1.3rem !important;
}

.schedule-nav>.schedule-text {
    font-size: 1rem !important;
}

@media only screen and (max-width: 991px) {
    .navbar-contents {
        flex-direction: column;
        padding: 10px;
    }

    .navbar-collapse {
        padding-left: 0rem !important;
    }

    .links-container {
        flex-direction: column;
    }
}

/*------------------------------------------------- NAVBAR CSS Ends --------------------------------------------------------- */

/*------------------------------------------------- FOOTER CSS STARTS --------------------------------------------------------- */

ul {
    list-style-type: none;
    margin: 0;
    padding: 0
}

ul>li {
    padding: 4px;
    color: black;
    width: fit-content;
}

ul>li:hover {
    color: #286fb4;
    cursor: pointer
}

hr {
    border-width: 3px
}

/* .card {
    padding: 2% 7%
} */

.social>i {
    padding: 1%;
    font-size: 15px
}

.social>i:hover {
    color: #286fb4;
    cursor: pointer
}

.policy>div {
    padding: 4px
}

.footer-heading {
    color: black;
    font-weight: 700;
}

.divider {
    border-top: 1px solid #e0e0e2
}

.logo {
    height: 50px;
}

.icon-height {
    height: 30px;
}

.flex {
    display: flex;
    padding-top: 15px;
}

.padding-left {
    padding: 0px 0px 0px 11px;
}

.height {
    height: 30px;

}

.font-size20 {
    font-size: 20px;


}

.footer-JC {
    justify-content: flex-end;
}

@media only screen and (max-width: 991px) {
    .footer-flex {
        display: flex;
        flex-direction: column;
    }

    .footer-JC {
        justify-content: flex-start;
    }
}

/*------------------------------------------------- FOOTER CSS ENDS --------------------------------------------------------- */

/* Price Section Start */
.price-para {
    font-size: 13px;
}

.price-listing {
    list-style-type: none;
    padding-left: 0;
}

.price-grid {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 1rem;
}

.price-EM-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 1rem;
}

.price-WMS-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
}

.price-items-1 {
    padding-top: 27px;
    margin-top: 20px !important;
    padding-bottom: 27px;
    padding-left: 23px;
    padding-right: 23px;
    border-radius: 16px;
    background: #F8FAFB;
}

.price-items-1-btn {
    border-radius: 5px;
    border: 1px solid #D6D6D6;
    background: #EBEBEB;
}

.price-items-2 {
    padding-top: 27px;
    margin-top: 20px !important;
    padding-bottom: 27px;
    padding-left: 23px;
    padding-right: 23px;
    border-radius: 16px;
    background: #ECF4FE;
}

.price-items-2-btn {
    border-radius: 5px;
    color: #FFF;
    background: #2C8CF4;
    border: 1px solid #2C8CF4;
}

.price-items-3 {
    padding-top: 27px;
    margin-top: 20px !important;
    padding-bottom: 27px;
    padding-left: 23px;
    padding-right: 23px;
    border-radius: 16px;
    background: #F1F1FD;
}

.price-items-3-btn {
    border-radius: 5px;
    color: #FFF;
    background: #2D2DB0;
    border: 1px solid #2D2DB0;
}

.price-list {
    font-size: 14px;
}

.most-popular-blue {
    border-radius: 10px 10px 10px 0px;
    background: #1273EB;
    color: white;
    border: 1px solid #1273EB;
    position: absolute;
    z-index: -1;
    margin-top: -60px;
    margin-left: -23px;
}

.most-popular-purple {
    border-radius: 10px 10px 10px 0px;
    background: #2D2DB0;
    color: white;
    border: 1px solid #2D2DB0;
    position: absolute;
    z-index: -1;
    margin-top: -60px;
    margin-left: -23px;
}

.most-popular-grey {
    border-radius: 10px 10px 10px 0px;
    background: #675F72;
    color: white;
    border: 1px solid #675F72;
    position: absolute;
    z-index: -1;
    margin-top: -60px;
    margin-left: -23px;
}

@media only screen and (max-width: 1199px) {
    .price-EM-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .price-EM-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .price-EM-div {
        display: none;
    }
}

@media only screen and (max-width: 991px) {
    .price-grid {
        grid-template-columns: repeat(3, 1fr);
    }

    .price-WMS-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media only screen and (max-width: 768px) {
    .price-grid {
        grid-template-columns: repeat(2, 1fr);
        row-gap: 2.5rem;
    }

    .price-WMS-grid {
        grid-template-columns: repeat(2, 1fr);
        row-gap: 2.5rem;
    }
}

@media only screen and (max-width: 566px) {
    .price-grid {
        grid-template-columns: repeat(1, 1fr);
        row-gap: 2.5rem;
    }

    .price-WMS-grid {
        grid-template-columns: repeat(1, 1fr);
        row-gap: 2.5rem;
    }

    .price-EM-grid {
        grid-template-columns: repeat(1, 1fr);
        row-gap: 2.5rem;
    }
}

/* Price Section End */


/* Privacy Policy  */
.heading-pp {
    font-size: 30px;
    line-height: 42px;
    font-weight: bolder;
}

.hover-none {
    li:hover {
        color: black;
        cursor: auto;
    }
}