<?php

namespace App\Http\Controllers;

use Exception;
use Illuminate\Http\Request;
use App\Models\BillingAddressModel;
use App\Http\Controllers\Controller;
use App\Models\LeadAddress;
use Illuminate\Database\QueryException;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Session;

class BillingAddressController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $data = BillingAddressModel::paginate(6);
        return view("userpages.billingAddress.Show", compact("data"));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view("userpages.billingAddress.Add");
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validate = validator($request->all(), [
            'name' => ['required'],
            // "email" => "nullable|email",
            // "pinCode" => 'required|numeric',
            'address' => 'required',
            "gst" => 'nullable |regex:/^([0-9]){2}([a-zA-Z]){5}([0-9]){4}([a-zA-Z]){1}([a-zA-Z0-9]){3}$/',

        ], [], [
            "name" => 'Name',
            "gst" => 'Gst',
            "company" => 'Company',
            "mobile" => 'Mobile',
            "email" => 'E-Mail',
            "isPrimary" => 'Primary',
            "pinCode" => 'PinCode',
            "city" => 'City',
            "state" => 'State',
            "country" => 'Country',
            "address" => 'Address',
        ]);

        if ($validate->fails()) {
            Session::put('isAddModalShow', true);
            return redirect()->back()->withErrors($validate)->withInput();
        }
        $primary = 0;
        if ($request->isPrimary) {
            if ($request->isPrimary == "on") {
                $primary = 1;
                BillingAddressModel::where('lead_id', Auth::id())->update(["isPrimary" => 0]);
            }
        }

        try {
            $newAddr = new BillingAddressModel;
            $newAddr->name = $request->name;
            $newAddr->company = $request->company;
            $newAddr->gst = $request->gst;
            $newAddr->address = $request->address;
            $newAddr->city = $request->city;
            $newAddr->pincode = $request->pincode;
            $newAddr->state = $request->state;
            $newAddr->country = $request->country;
            $newAddr->isPrimary = $primary;
            $newAddr->lead_id = auth()->id();
            $newAddr->save();
            // $newAddr->mobile = $request->mobile;
            // $newAddr->email = $request->email;
            Session::put('isAddModalShow', false);

            return redirect()->back()->with('success', 'Billing address added successfully.');
        } catch (Exception $e) {
            Session::put('isAddModalShow', true);
            if (app()->environment('local')) {
                return redirect()->back()->withInput()->with("modal-error", $e->getMessage());
            } else {
                return redirect()->back()->withInput()->with("modal-error", ErrMsg());
            }
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $data["address"] = BillingAddressModel::find($id);
        $data["id"] = $id;
        $data["event"] = "edit";
        return json_encode($data);
        // return view("userpages.billingAddress.Add", compact("data"));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        $request->validate([
            "name" => "required|string",
            "company" => "required|string",
            "gst" => 'regex:/^[0-9]{2}[A-Z]{1}[0-9]{5}[A-Z]{1}[0-9]{4}$/|nullable',
            "mobile" => "required|numeric|digits:10",
            "email" => "required|email",
            "address" => "string|nullable",
            "city" => 'nullable|string',
            "pincode" => 'nullable|numeric',
            "state" => 'nullable|string',
            "placeOfSupply" => 'string|nullable',
            "country" => 'string|nullable',
        ]);
        $formdata = $request->all();
        if (!array_key_exists("isPrimary", $formdata)) {
            $formdata["isPrimary"] = "0";
        }
        $resultdata = BillingAddressModel::find($id);
        $result = $resultdata->update($formdata);
        if ($result) {
            return redirect()->back()->with("success", "data updated");
        } else {
            return redirect()->back()->with("error", "update failed");
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        // BillingAddressModel delete foreign key error
        $result = BillingAddressModel::find($id);
        $result->delete();
        return redirect()->route('user.address');
    }
    public function setasDefault(Request $request)
    {
        Session::put('isEditModalShow', false);
        $id = $request->id;
        $lead_id = Auth::id();
        try {
            BillingAddressModel::where('lead_id', $lead_id)->update(["isPrimary" => 0]);
            $newPrimary = BillingAddressModel::find($id);
            $newPrimary->update(['isPrimary' => 1]);
            return redirect()->back()->with('success', 'Default Address updated.');
        } catch (Exception $e) {
            return redirect()->back()->with('error', ErrMsg());
        }
    }

    public function updateModel(Request $request)
    {
        if (empty($request->address_id)) {
            return redirect()->back()->with('error', 'Invlaid request.');
        }

        $validate = validator($request->all(), [
            'name' => ['required'],
            "email" => "nullable|email",
            "pinCode" => 'required|numeric',
            'address' => 'required',
            "gst" => 'nullable |regex:/^([0-9]){2}([a-zA-Z]){5}([0-9]){4}([a-zA-Z]){1}([a-zA-Z0-9]){3}$/',

        ], [], [
            "name" => 'Name',
            "gst" => 'Gst',
            "company" => 'Company',
            "mobile" => 'Mobile',
            "email" => 'E-Mail',
            "isPrimary" => 'Primary',
            "pinCode" => 'PinCode',
            "city" => 'City',
            "state" => 'State',
            "country" => 'Country',
            "address" => 'Address',
        ]);

        if ($validate->fails()) {

            Session::put('isEditModalShow', true);

            return redirect()->back()->withErrors($validate)->withInput();
        }

        try {
            DB::beginTransaction();
            $addr = LeadAddress::find($request->address_id);

            if ($addr) {
                if ($request->isPrimary != $addr->isPrimary) {

                    if ($request->isPrimary && $request->isPrimary == 1) {
                        $addr->isPrimary = 1;
                        (BillingAddressModel::where('lead_id', Auth::id())->update(["isPrimary" => 0]));
                    } else {
                        $addr->isPrimary = 0;
                    }
                }
                $addr->name = $request->name;
                $addr->company = $request->company;
                $addr->gst = $request->gst;
                $addr->address = $request->address;
                $addr->city = $request->city;
                $addr->pincode = $request->pinCode;
                $addr->state = $request->state;
                $addr->country = $request->country;
                $addr->update();
                Session::put('isEditModalShow', false);
                DB::commit();
                return redirect()->back()->with('success', 'Billing address updated successfully.');
            } else {
                Session::put('isEditModalShow', false);
                DB::rollBack();
                return redirect()->back()->with("error", 'Invalid Request.');
            }
        } catch (QueryException $e) {
            DB::rollBack();
            Session::put('isEditModalShow', true);
            return redirect()->back()->withInput()->with("modal-error", "Invalid data manipulation");
        } catch (Exception $e) {
            DB::rollBack();
            Session::put('isEditModalShow', true);
            return redirect()->back()->withInput()->with("modal-error", "Invalid request or system error occurred.");
        }
    }
}
