@extends('userpages.main')
@section('title', 'Graphics')
@section('child-PAGE-CSS')
    <link rel="stylesheet" href="{{ asset('assets/global/css/cart.css') }}">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@recogito/annotorious@2.7.8/dist/annotorious.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@annotorious/annotorious@3.4.0/dist/annotorious.min.css">
    <style>
        /* === RESPONSIVE LAYOUT === */
        @media (max-width: 991.98px) {
            .category-sidebar {
                display: none !important;
            }


        }


        /* === CATEGORY STYLES === */
        .category-item,
        .subcategory-item {
            cursor: pointer;
            transition: all 0.3s ease;
            color: black;
        }

        .category-item:hover,
        .subcategory-item:hover {
            background-color: #e9ecef;
        }

        .category-item.active {
            background-color: #6e6e6eaf;
            color: white;
        }

        .category-item.active>a {
            color: white;
        }

        .category-item>a {
            color: black;
        }

        .subcategory-item.active {
            background-color: #194DAB;
            color: white;
        }

        .subcategory-item {
            padding-left: 2rem;
            font-size: 0.9rem;
        }

        /* === IMAGE CARD STYLES === */
        .image-card {
            transition: transform 0.2s ease, box-shadow 0.2s ease;
            border: 1.5px solid white;
        }

        .image-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .image-card.selected {
            border-color: #007bff71;
            box-shadow: 3px 3px 7px rgba(0, 123, 255, 0.3);
        }

        .image-placeholder {
            max-width: 250px;
            max-height: auto;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            font-size: 3rem;
            overflow: hidden;
            cursor: pointer;
            position: relative;
            margin: auto
        }

        .image-card:hover .demo-image {
            transform: scale(1.05);
        }

        .image-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .image-placeholder:hover .image-overlay {
            opacity: 1;
        }

        /* === COLLAPSE STYLES === */
        .collapse-icon {
            transition: transform 0.3s ease;
            transform: rotate(0deg);
        }

        .collapse-toggle:not(.collapsed) .collapse-icon,
        .fa-chevron-down.rotate-180 {
            transform: rotate(180deg);
        }

        .collapse-toggle {
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .collapse-toggle:hover {
            opacity: 0.7;
        }

        .card-body-checkbox {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
            justify-content: space-between
        }

        .card-body-checkbox label {
            margin: 0;
            cursor: pointer;
            font-weight: 500;
            flex: 1;
            user-select: none;
        }

        /* === MODAL STYLES === */
        .image-modal .modal-dialog {
            max-width: 90vw;
            max-height: 90vh;
        }



        /* === GLOBAL STYLES === */
        body {
            margin: 0;
            padding: 0;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            background-color: #F8F7FC !important;
        }

        .rounded {
            border-radius: 7px !important;
        }

        .rounded-pill {
            border-radius: 50rem !important;
        }

        .r6o-comment-dropdown-menu>li {
            padding: 0 15px !important;
        }
    </style>
@endsection
@section('userpagesection')
    <div class="container-fluid container-lg">
        <div class="">

            <!-- Header Bar -->
            <div class="bg-white p-3 rounded">
                <div class="row align-items-center justify-content-between">
                    <div class="col-md-1 col-lg-1 col-2 ">
                        <a class="btn rounded-circle shadow" href="{{ url()->previous() }}">
                            <i class="bi bi-arrow-left"></i>
                        </a>
                    </div>
                    <div class="col-md-7 col-lg-7 col-10 ">
                        <div class="d-flex justify-content-end  flex-column">
                            <h4 class="mb-0 ">{{ $order->orderItem->name ?? 'N/A' }}</h4>
                            <div class="text-secondary ">Pending Designs:
                                {{ $order->noOfDesignsPending }}
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 col-lg-4 col-12 d-flex justify-content-end align-items-center">
                        <div class="error-box text-danger small d-none"></div>
                        @if ($order->noOfDesignsPending > 0)
                            <a href="{{ route('graphics.orders.edit', ['order' => $order->id]) }}"
                                style="background-color: #194DAB;" class="btn text-white btn-sm rounded-pill">Select
                                Design</a>
                        @else
                            <button data-bs-toggle="tooltip" data-bs-title="You have no pending Designs."
                                style="background-color: #84a1d5;" class="btn text-white btn-sm rounded-pill">Select
                                Design</button>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Image Grid -->
            @if ($items->count() != 0)
                <div class=" pt-3">


                    <div class="table-responsive ">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Preview</th>
                                    <th>Name</th>
                                    <th>Category</th>
                                    <th>Format</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach ($items as $item)
                                    @php
                                        if ($item->isDesigned != 0) {
                                            $image = $item->design;
                                        } else {
                                            $image = $item;
                                        }
                                        $followDesign = $item->design;
                                        $previewFile = s3_fileShow(
                                            $image->image,
                                            'graphic_design',
                                            $followDesign->name,
                                        );
                                        $parsedUrl = parse_url($previewFile);
                                        $path = isset($parsedUrl['path']) ? $parsedUrl['path'] : '';

                                        $extension = pathinfo($path, PATHINFO_EXTENSION);

                                        if (str_contains($extension, '&')) {
                                            $extension = explode('&', $extension)[0];
                                        }
                                    @endphp
                                    <tr>
                                        <td>
                                            <div class="image-placeholder" data-image-index="{{ $image->id }}">
                                                <img src="{{ $previewFile }}" alt="{{ $image->image }}"
                                                    data-name="{{ $followDesign->name }}"
                                                    data-category="{{ $followDesign->category->name }}"
                                                    data-image-id="{{ $item->id }}" id="image-{{ $image->id }}"
                                                    class="demo-image image-preview img-fluid rounded-1"
                                                    title="{{ $image->image }}" loading="lazy" style="max-height: 100px;">
                                                <div class="image-overlay">
                                                    <i class="fas fa-eye"></i>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="align-middle">{{ $followDesign->name }}</td>
                                        <td class="align-middle">{{ $followDesign->category->name }}</td>
                                        <td class="align-middle">{{ $extension }}</td>
                                        <td class="align-middle">
                                            @if ($item->isDesigned)
                                                <div class="small bg-success-subtle px-2 rounded-2 border border-success text-success py-1 fw-bold"
                                                    style="font-size: 12px; width: fit-content">
                                                    Done
                                                </div>
                                            @else
                                                <div class="small bg-warning-subtle px-2 rounded-2 border border-warning text-warning py-1 fw-bold"
                                                    style="font-size: 12px; width: fit-content">
                                                    Pending
                                                </div>
                                            @endif
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                    @if ($items->lastPage() > 1)
                        <div class="mt-3 p-1 bg-white" style="border-radius: 7px;">
                            @include('components.showPagination', ['data' => $items])
                        </div>
                    @endif

                </div>
            @else
                <div class="mt-3 p-1 bg-white" style="border-radius: 7px;">
                    <div class="d-flex align-items-center flex-column bg-white justify-content-center ">
                        <img src="{{ asset('assets/userpages/images/billing.png') }}" alt="blank_page_img"
                            class="img-fluid">
                    </div>
                </div>
            @endif

        </div>
    </div>

    <!-- Image View Modal -->
    <div class="modal fade image-modal" id="imageModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog     modal-dialog-centered m-auto">
            <div class="modal-content border-0 rounded-4 shadow-lg m-auto">
                <div class="modal-header border-2">
                    <h5 class="modal-title" id="modalImageTitle">Image Title</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body p-0">
                    <div class="row w-100 m-0">
                        <div class=" col-12 cols-md-7 col-lg-8  p-3   bg-body-tertiary ">
                            <div class="flex-column d-flex ">
                                <img src="" alt="" class="modal-image img-fluid rounded-1 m-auto"
                                    id="modalImage" data-image-id="">
                                <div class="text-secondary text-start w-50 m-auto">
                                    <small>
                                        Select image area and add Comment
                                    </small>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-4  col-12 cols-md-5 p-0">
                            <div class="card w-100 h-100 border-0  bg-wite ">
                                <h5 class="card-header border-0 bg-white">
                                    Comments
                                </h5>
                                <div class="card-body  overflow-auto" id="modelCommentBox" style="max-height: 400px;">
                                    <div class="fs-5 text-secondary">
                                        Add your comment here...
                                    </div>
                                </div>
                                <div class="card-footer bg-white border-0">
                                    <div class="d-flex flex-start w-100">
                                        {{-- <div class="p-1">
                                        <div class="bg-body-secondary fw-semibold px-3 py-2 rounded-circle text-primary"
                                            data-bs-toggle="tooltip"
                                            data-bs-title="{{ auth()->user()->name ?? 'User' }}">
                                            {{ auth()->user()->name[0] ?? 'U' }}
                                        </div>
                                    </div> --}}
                                        <div class="w-100 form-floating">
                                            <textarea class="form-control" id="modeltextarea" rows="3" placeholder="Add a comment..."></textarea>
                                            <label for="floatingInputInvalid">Add a comment...</label>
                                        </div>
                                    </div>
                                    <div class="float-end mt-2 pt-1 d-flex">
                                        <div id="storeCommentErr"></div>
                                        <button type="button" id="storeComment" class="btn btn-primary btn-sm">Post
                                            comment</button>
                                    </div>
                                </div>

                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer border-2">
                    <div class="d-flex justify-content-between align-items-center w-100">
                        <small class="text-muted" id="modalImageInfo"> landscapes</small>
                        <div>
                            <button type="button" class="btn btn-secondary btn-sm"
                                data-bs-dismiss="modal">Close</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
@section('PAGE-script')
    {{-- comment store --}}

    <script>
        const textarea = document.getElementById("modeltextarea");
        const button = document.getElementById("storeComment");

        textarea.addEventListener("keydown", function(e) {
            if (e.ctrlKey && e.key === "Enter") {
                e.preventDefault(); // Optional: prevent new line
                button.click(); // Trigger the button click
            }
        });
    </script>

    <script src="https://cdn.jsdelivr.net/npm/@recogito/annotorious@2.7.8/dist/annotorious.min.js"></script>

    {{-- annotorious --}}
    <script>
        $(document)
            .on('change', '.custom-checkbox', function() {
                const imageId = parseInt($(this).closest('.image-card').data('image-id'));
                toggleSelection(imageId);
            })
            .on('click', '.image-placeholder', function(e) {
                e.stopPropagation();
                const imageIndex = parseInt($(this).data('image-index'));
                showImageModal(imageIndex);
            });

        function showImageModal(imageIndex) {
            const $image = $(`#image-${imageIndex}`);
            const imageData = $image.data();
            $('#modalImageTitle').text(imageData.name);
            $('#modalImage').attr('src', $image.attr('src')).attr('alt', imageData.name);
            $('#modalImageInfo').text(imageData.category);
            $('#modalImage').data('image-id', imageData.imageId);
            createAnnotation(imageData.imageId)
            $('#imageModal').modal('show');
        }

        const anno = Annotorious.init({
            image: 'modalImage',
            widgets: [{
                widget: 'COMMENT'
            }],
            tools: ['rect', 'polygon']
        });

        function createAnnotation(id) {
            // $('#modelCommentBox').empty();
            anno.clearAnnotations();
            fetch(`/annotations/${id}`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': '{{ csrf_token() }}'
                    }
                })
                .then(res => res.json())
                .then(data => {
                    let newAnnotations = Object.values(data.annotations);

                    if (newAnnotations.length) {
                        anno.setAnnotations(newAnnotations);
                    }
                    if (data.comments.length) {
                        $('#modelCommentBox').empty();
                        data.comments.forEach(comment => {
                            let commentBy = '';
                            if (comment.by_lead) {
                                commentBy = comment.by_lead.name;
                            } else if (comment.by_user) {
                                commentBy = comment.by_user.name;
                            }
                            let commentStr = commentPreviewBox(comment.id, commentBy, comment.datetime,
                                comment.comment)
                            $('#modelCommentBox').append(commentStr);
                        });
                        $("#modelCommentBox").animate({
                            scrollTop: $('#modelCommentBox').get(0).scrollHeight
                        }, 1000);
                    } else {
                        $('#modelCommentBox').empty();
                        let commentStr = `<div class="fs-5 text-secondary">Add your comment here... </div>`
                        $('#modelCommentBox').append(commentStr);
                    }
                });
        }



        function commentPreviewBox(id, commentBy, datetime, comment) {
            return `<div class="p-1 border-top" id="commentId${id}">
                <div class="p-2"> <div class="d-flex flex-start align-items-center justify-content-between">                                                    
                                    <h6 class="fw-semibold  mb-0 "> ${commentBy}</h6>
                                    <p class="text-muted small mb-0 "  style="font-size: 12px;">
                                        <i class="bi bi-clock me-1"></i>${datetime}
                                    </p>                                                    
                                    </div>
                                <p class=" m-0 text-start" style=" font-size: 14px;">${comment}</p>
                </div>
                </div>`
        }
        anno.on('createAnnotation', annotation => {
            commentStore({
                annotation: annotation
            })
        });

        anno.on('updateAnnotation', annotation => {
            commentStore({
                annotation: annotation
            })
        });

        anno.on('deleteAnnotation', annotation => {
            let delID = annotation.id.replace('#', '')
            fetch(`/annotations/${delID}`, {
                // fetch(`/annotations/bb17ee36-3919-4f25-abcb-d9df5726c366`, {
                method: 'DELETE',
                headers: {
                    'X-CSRF-TOKEN': '{{ csrf_token() }}'
                }
            });
        });

        $('#storeComment').on('click', function() {
            const modelTextareaValue = $('#modeltextarea').val();
            if (modelTextareaValue.length > 0) {
                commentStore({
                    text: modelTextareaValue
                })
            } else {
                $('#storeCommentErr').text('Empty Comment.')
            }
        });

        function deleteComment(id) {
            fetch(`/annotations/comment/${id}`, {
                    method: 'DELETE',
                    headers: {
                        'X-CSRF-TOKEN': '{{ csrf_token() }}'
                    }
                })

                .then(res => res.json())
                .then(data => {
                    $(`#commentId${id}`).remove()
                });
        }

        function commentStore(data) {
            const imageData = $('#modalImage').data();
            data.item_id = imageData.imageId;

            fetch('/annotations', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': '{{ csrf_token() }}'
                    },
                    body: JSON.stringify(data)
                })
                .then(res => res.json())
                .then(data => {
                    let commentObj = data.comment
                    if (data.status && commentObj.comment) {

                        let commentStr = commentPreviewBox(commentObj.id, "{{ auth()->user()->name }}", data.datetime,
                            commentObj.comment)
                        $('#modelCommentBox').append(commentStr);
                        $("#modelCommentBox").animate({
                            scrollTop: $('#modelCommentBox').get(0).scrollHeight
                        }, 1000);
                        $('#modeltextarea').val('')
                    }
                });
        }
    </script>


@endsection
