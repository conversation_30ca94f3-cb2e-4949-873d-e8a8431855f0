<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="csrf-token" content="{{ csrf_token() }}" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    @yield('metaData')
    <title>
        @yield('title')
    </title>
    <link rel="icon" type="image/x-icon" href="@yield('siteIcon')">

    @if(app()->environment()=='local' )
    <meta name="robots" content="noindex">
    @endif

    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.1/dist/css/bootstrap.min.css" rel="stylesheet">


    {{-- AOS CSS --}}
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">

    {{-- Font Awesome CSS --}}
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
        integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw=="
        crossorigin="anonymous" referrerpolicy="no-referrer" />
    {{-- bootstrap icons --}}

    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">

    {{-- JQUERY --}}
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.7.0/jquery.min.js"
        integrity="sha512-3gJwYpMe3QewGELv8k/BX9vcqhryRdzRMxVfq6ngyWXwo03GFEzjsUm8Q7RZcHPHksttq7/GFoxjCVUjkjvPdw=="
        crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    <style>
        .iti {
            width: 100% !important;
        }
    </style>
    <style>
        .modal-backdrop {
            display: none !important;
        }
    </style>
    @yield('css')
    @yield('PAGE-CSS')



</head>

<body>
    @yield('breadcrumb')
    @yield('main')

    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <script>
        AOS.init();
    </script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"
        integrity="sha384-geWF76RCwLtnZ8qwWowPQNguL3RmwHVBC9FhGdlKrxdiJJigb/j/68SIy3Te4Bkz" crossorigin="anonymous">
    </script>



    @yield('script')
    @yield('PAGE-script')
    @include('components.core.getQuoteModal')
    @include('components.core.message')
    <script>
        $("button").prop("disabled", true);
        $(document).ready(function() {
            $("button").prop("disabled", false);
        });
    </script>
    <script>
       
        function conCountry() {
            var phoneNo = $('#number').val();
            var countryCodenumber = $('.iti__selected-dial-code').text();
            $('#number').val(countryCodenumber + phoneNo);
        }
    </script>

</body>

</html>