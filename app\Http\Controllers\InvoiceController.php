<?php

namespace App\Http\Controllers;

use DateTime;
use Exception;
use App\Models\Cart;
use App\Models\Invoice;
use App\Models\LeadAddress;
use App\Models\InvoiceItems;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Models\CartView;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Database\QueryException;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Validator;

class InvoiceController extends Controller
{

    function payment(Request $req, $invoice)
    {
        $data['inv'] = Invoice::where('id', $invoice)->where('lead_id', Auth::user()->id)->first();
        $data['items'] = $data['inv']->items;
        return view('userpages.invoice.payment', $data);
    }
    public function addressUpdate(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'address_id' => 'required'
        ], [
            'address_id.required' => 'Select billing address before proceeding for payment OR Create One. '
        ]);
        if ($validator->fails()) {
            return back()->with('error', 'Select billing address before proceeding for payment OR Create One.')->withInput();
        }

        try {
            Session::put('address_id', $request->address_id);
        } catch (Exception $e) {
            return redirect()->back()->with('error', 'Something went wrong. ');
        }
        return redirect()->route('inv.payment');
    }


    function alreadyPaid()
    {
        return view('userpages.invoice.alreadyPaid');
    }
}
