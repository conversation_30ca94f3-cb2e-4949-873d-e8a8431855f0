<div class="modal fade" id="addAddress" tabindex="-1" aria-labelledby="addAddressLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title heading-font" id="addAddressLabel">Create Billing Profile</h4>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <?php echo $__env->make('components.core.billingAddressForm', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            </div>

        </div>
    </div>
</div>
<script>
    $('#addpincode').change(async function() {
        var pincode = $(this).val();

        try {
            let res = await fetch("https://api.postalpincode.in/pincode/" + pincode);
            let response = await res.json();
            var postData = response[0]['PostOffice'][0];

            $('#addcountry').val(postData.Country);
            $('#addstate').val(postData.State);
            $('#addcity').val(postData.District);
        } catch (error) {
            console.log(error);
        }
    });
</script>
<?php if(session('isAddModalShow') == true): ?>
    <script>
        $(document).ready(function() {
            new bootstrap.Modal($('#addAddress')).show()
        });
    </script>
<?php endif; ?>
<?php /**PATH C:\Users\<USER>\Desktop\live\websites_laravel\resources\views/userpages/billingAddress/addModal.blade.php ENDPATH**/ ?>