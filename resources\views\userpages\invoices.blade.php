@extends('userpages.main')

@section('title', 'Invoices')
@section('userpagesection')

<div class=" ms-lg-2 m-0">
    <div class="bg-white pt-2 pb-2 pe-3 ps-3 mb-3" style="border-radius: 7px;">
        <h4 class="heading-font">Invoices</h4>
    </div>
    
    @if ($data->count() != 0)
    <div class="table-responsive">
        <table class="table mb-2" style="--bs-table-bg: transparent;">
            <thead>
                <tr>
                    <th scope="col" class="ps-5 fw-bold text-nowrap text-start">Invoice No</th>
                    <th scope="col" class="fw-bold text-nowrap text-center">Date</th>
                    <th scope="col" class="fw-bold text-nowrap text-center">Qty</th>
                    <th scope="col" class="fw-bold text-nowrap text-end">Total Amount</th>
                </tr>
            </thead>
            <tbody class="bg-white">
                @foreach ($data as $invoice)
                <tr class="">
                    <td class="gray-dark-text text-nowrap ">
                        <button class="border-0 bg-transparent btn" onclick="showItems('sub-table-{{ $invoice->invoiceNumber }}')">
                            <i class="fa-solid fa-chevron-down"></i></button>
                        {{ $invoice->invoiceNumber }}
                    </td>
                    <td class="gray-dark-text text-nowrap text-center">
                        {{ humanDate($invoice->date) }}
                    </td>
                    <td class="gray-dark-text text-nowrap  text-center">{{ count($invoice->items) }}</td>
                    <td class="gray-dark-text text-nowrap ">
                        <div class="d-flex gap-3 align-items-center  justify-content-end">

                            ₹ {{ number_format($invoice->items->sum('total')) }}
                            {{-- <a href="" class="btn gray-dark-text btn-sm" style="background-color: #194DAB;">
                                <i class="fa-solid fa-download text-white"></i>
                            </a> --}}
                        </div>
                    </td>
                </tr>
                <tr class="sub-table" id="sub-table-{{ $invoice->invoiceNumber }}" style="display: none;">
                    <td colspan="5">
                        <div class="table-responsive">
                            <table class="table mb-0 table-striped table-borderless">
                                <thead>
                                    <tr>
                                        <th class="text-start">Product Name</th>
                                        <th class="text-center">Qty</th>
                                        <th class="text-center">Rate</th>
                                        <th class="text-center">Tax</th>
                                        <th class="text-end">Total</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach ($invoice->items as $items)
                                    <tr>
                                        <td class="gray-dark-text text-nowrap">{{ $items->name }}
                                        </td>
                                        <td class="text-center gray-dark-text text-nowrap">
                                            {{ $items->quantity }}
                                        </td>
                                        <td class="text-center gray-dark-text text-nowrap">
                                            ₹ {{ number_format($items->rate) }}
                                        </td>
                                        <td class="text-center gray-dark-text text-nowrap">
                                            ({{ $items->gstPercent }}%)
                                        </td>
                                        <td class="text-end gray-dark-text text-nowrap">
                                            ₹ {{ number_format($items->total) }}
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </td>
                </tr>
                @endforeach

            </tbody>
        </table>
    </div>
    @else
    <div class="d-flex align-items-center flex-column bg-white justify-content-center  ">
        <img src="{{ asset('assets/userpages/images/billing.png') }}" alt="blank_page_img" class="img-fluid">
        <div class="bg-transparent fs-4 fw-medium mb-4 border border-0 border-bottom text" style="color: #194dab;">
            Currently No Invoice Available.
        </div>

    </div>
    @endif

    <x-showPagination :data=$data />



</div>

@endsection
@section('PAGE-script')
<script>
    function showItems(id) {
        $('.sub-table').not('#' + id).hide();
        $('#' + id).toggle();
    }
</script>
@endsection
