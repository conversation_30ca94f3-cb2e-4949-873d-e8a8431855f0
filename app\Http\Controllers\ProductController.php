<?php

namespace App\Http\Controllers;

use Exception;
use App\Models\Product;
use App\Models\ProductVariant;

class ProductController extends Controller
{
    public function product($category, $categoryId, $productVariant)
    {
        try {
            // $cacheKey = "product_{$productVariant}";
            // $data = cache()->remember($cacheKey, now()->addMinutes(2), function() use ($productVariant) {
            $rangeVariant = ProductVariant::with([
                'product:id,name,product_category_id',
                'ranges' => fn($q) => $q->orderBy('minQty'),
                'product.category:id,name',
                'product.features',
                'product.addonsProducts',
                'product.variants' => fn($q) => $q->whereHas('ranges')->with(['ranges' => fn($q) => $q->orderBy('minQty')])
            ])->findOrFail($productVariant);
            $data = [
                'productVariant' => $rangeVariant,
                'productVariants' => $rangeVariant->product->variants->filter(fn($variant) => $variant->ranges->isNotEmpty()),
                'bulkDiscounts' => $rangeVariant->ranges,
                'product' => $rangeVariant->product,
                'mainProduct' => [
                    'first' => $rangeVariant->ranges->first(),
                    'last' => $rangeVariant->ranges->last()
                ],
                'hidePrice' => auth()->check() && auth()->user()->hidePrice == 0,
                'addons' => collect()
            ];

            // Eager load addons with non-empty variants and ranges
            $addons = $data['product']->addonsProducts()
                ->select([
                    'product.id',
                    'name',
                    'tagline',
                    'product_category_id',
                    'detailOnInvoice',
                    'taxPercent',
                    'version',
                    'showPriceAnonymous',
                    'showPriceAfterLogin',
                    'getQuoteAnonymous',
                    'getQuoteAfterLogin',
                    'isInInterestForm',
                    'isOnWebsite',
                    'isActive'
                ])
                ->whereHas('variants.ranges')
                ->with(['variants' => function ($query) {
                    $query->whereHas('ranges')->with(['ranges' => function ($q) {
                        $q->orderBy('minQty');
                    }]);
                }])
                ->get();

            $data['addons'] = $addons;
            $data['addonsRanges'] = $addons->pluck('variants.*.ranges')->flatten(2)->values()->toArray();

            //     return $data;
            // });

            return view('global.ProductView', $data);
        } catch (Exception $e) {
            return redirect('/')->with('error', 'Invalid Request.');
        }
    }
}
