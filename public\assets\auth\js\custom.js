// <!-- on backspace goto last input -->

$(':input').keydown(function (e) {
            if ((e.which == 8 || e.which == 46) && $(this).val() == '') {
                        $(this).prev('input').focus();
            }
});
// <!-- jquery for focus on next input -->

$("input").keyup(function () {
            if (this.value.length == this.maxLength) {
                        $(this).next('input').focus();
            }
});

var count = 0;

// on enter key press  find .btn class and click it where diable is not set
$(document).ready(function () {
            $('#number').keypress(function (e) {
                        if (e.which == 13) {
                                    e.preventDefault();
                                    $('#kt_sign_in_submit').click();
                                    $('#kt_sign_in_submit').text('Processing..');
                        }
            });
            $('#text6').keypress(function (e) {
                        if (e.keyCode == 13) {
                                    $('#verifPhNum').click();
                                    $('#verifPhNum').text('Processing..');
                        }
            });
});
var input = document.querySelector("#number");
window.intlTelInput(input, {
            dropdownContainer: document.body,
            localizedCountries: {
                        'IN': 'india'
            },
            placeholderNumberType: "MOBILE",
            preferredCountries: ['in'],
            separateDialCode: true,
});