<?php

namespace App\Http\Controllers\Graphic;

use App\Http\Controllers\Controller;
use App\Models\Graphic\DesignOrder;
use App\Models\GraphicProject;
use App\Models\LeadGraphicPlan;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class ProjectController extends Controller
{
    public function index($id = null)
    {

        $data['sort']  = request('sort') == 'DESC' ? 'ASC' : 'DESC'; // Default sort order

        try {

            $data['graphicOrders'] = DesignOrder::where('lead_id', Auth::id())->with('orderItem')->withCount('items')->paginate(10);

            return view('userpages.projects.graphic.order', $data);
        } catch (Exception $e) {

            return redirect()->back()->with('error', ErrMsg());
        }
    }
}
