<div class="modal fade text-dark" id="createTicket" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content ">
            <div class="modal-header">
                <h4 class="modal-title " id="exampleModalLab">Support</h4>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form action="<?php echo e(route('support.store')); ?>" id="support_ticket_form" class="form"
                    enctype="multipart/form-data" method="POST">
                    <?php echo csrf_field(); ?>
                    <div class="row mb-3 ">
                        <label class="col-lg-4 col-form-label fw-bold fs-6 ">Subject <span
                                class="text-danger">*</span></label>
                        <div class="col-lg-8 ">
                            <input style="border: solid 1px #dee2e6" type="text" name="subject"
                                class="form-control text-dark form-control-lg form-control-solid fs-6 m-auto" required
                                placeholder="Subject of Ticket" value="<?php echo e(old('subject')); ?>" />
                        </div>
                    </div>
                    <div class="row mb-3 ">
                        <label class="col-lg-4 col-form-label fw-bold fs-6 ">Mobile <span
                                class="text-danger">*</span></label>
                        <div class="col-lg-8 ">
                            <input style="border: solid 1px #dee2e6" type="text" name="mobile"
                                pattern="[0-9]{2}[0-9]{10}" value="<?php echo e(old('mobile')); ?>"
                                title="Kindly enter a valid mobile number with the appropriate country code.For example, '919876543210'."
                                class="form-control form-control-lg form-control-solid fs-6 m-auto  text-dark" required
                                placeholder="Enter Mobile" value="<?php echo e(old('subject')); ?>" />
                        </div>
                    </div>
                    <div class="row mb-3 ">
                        <label class="col-lg-4 col-form-label fw-bold fs-6">Product</label>
                        <div class="col-lg-8 ">
                            <select name="product_id" class="form-control  form-select m-auto" required>
                                <option value="">
                                    Select Product...
                                </option>
                                <?php $__currentLoopData = products(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $prd): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($prd->id); ?>" <?php if(old('product_id') == $prd->id): echo 'selected'; endif; ?>>
                                        <?php echo e($prd->name); ?>

                                    </option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>


                            </select>
                        </div>
                    </div>
                    <div class="row mb-3 ">
                        <label class="col-lg-4 col-form-label fw-bold fs-6 ">Message <span
                                class="text-danger">*</span></label>
                        <div class="col-lg-8 ">
                            <textarea name="comment" class="form-control form-control-lg form-control-solid fs-6" id=""
                                placeholder="Write Your Message "><?php echo e(old('comment')); ?></textarea>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <label class="col-lg-4 col-form-label fw-bold fs-6">Attachment</label>
                        <div class="col-lg-8 ">
                            <input type="file" name="attachment" id="file" class="form-control m-auto"
                                placeholder="Image" />

                            <small class="">IMAGE,ZIP,PDF.etc (Less then
                                20 MB)</small>

                        </div>
                    </div>
                    <div class="row mb-3 align-items-baseline">
                        <label class="col-lg-4 col-form-label fw-bold fs-6 ">Preferred Mode of
                            Contact <span class="text-danger">*</span></label>
                        <?php
                            $modeOfCCommunications = [
                                [
                                    'name' => 'Call',
                                    'value' => 0,
                                ],
                                [
                                    'name' => 'E-Mail',
                                    'value' => 1,
                                ],
                                [
                                    'name' => 'Whatsapp',
                                    'value' => 2,
                                ],
                            ];
                        ?>
                        <div class="col-lg-8 row">
                            <?php $__currentLoopData = $modeOfCCommunications; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $mode): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="form-check form-check-custom col">
                                    <input class="form-check-input" type="radio" name="mode"
                                        id=" <?php echo e($mode['name']); ?>" value="<?php echo e($mode['value']); ?>"
                                        <?php if(old('mode') == $mode['value'] || $loop->first): echo 'checked'; endif; ?> />
                                    <label class="form-check-label text-nowrap ps-1" for=" <?php echo e($mode['name']); ?>">
                                        <?php echo e($mode['name']); ?>

                                    </label>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                        </div>
                    </div>
                    <div class="row mb-3 ">
                        <label class="col-lg-4 col-form-label  fw-bold fs-6">Preferred Time <span
                                class="text-danger">*</span></label>
                        <?php
                            $preferredTimes = [
                                ['name' => 'Any Time', 'value' => 0],
                                ['name' => 'Morning', 'value' => 1],
                                ['name' => 'Afternoon', 'value' => 2],
                                ['name' => 'Evening', 'value' => 3],
                            ];
                        ?>
                        <div class="col-lg-8 row row-cols-2">
                            <?php $__currentLoopData = $preferredTimes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $time): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="form-check form-check-custom col">
                                    <input class="form-check-input" type="radio" name="preferredTime"
                                        id="<?php echo e($time['name']); ?>" value="<?php echo e($time['value']); ?>"
                                        <?php if(old('preferredTime') == $time['value'] || $loop->first): echo 'checked'; endif; ?> />
                                    <label class="form-check-label text-nowrap ps-1" for="<?php echo e($time['name']); ?>">
                                        <?php echo e($time['name']); ?>

                                    </label>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="submit" class="btn btn-primary"
                    onclick="$('#support_ticket_form').submit()">Generate</button>
            </div>
        </div>
    </div>
</div>
<?php if(isset($content)): ?>
    <div class="modal fade text-dark" id="planModal" tabindex="-1" aria-labelledby="exampleModalLabel"
        aria-hidden="true">
        <div class="modal-dialog <?php echo e($width ?? 'modal-xl'); ?>">
            <div class="modal-content ">

                <div class="modal-body">
                    <div class="text-end">

                        <button type="button" class="btn-close" data-bs-dismiss="modal"
                            aria-label="Close"></button>
                    </div>
                    <div class="">
                        <?php echo $__env->make($content, \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                    </div>

                </div>

            </div>
        </div>
    </div>
<?php endif; ?>
<?php /**PATH C:\Users\<USER>\Desktop\live\websites_laravel\resources\views/components/core/supportForm.blade.php ENDPATH**/ ?>