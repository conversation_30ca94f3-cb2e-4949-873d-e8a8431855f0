/* Common CSS */
.heading-font {
    font-size: 24px !important;
    font-weight: 600;
    margin: 0;
}

.gray-text {
    color: #505050;
}

.gray-dark-text {
    color: #626262 !important;
}

.text-weight-400 {
    font-weight: 400;
}

.text-weight-500 {
    font-weight: 500;
}

.support-label {
       font-weight: 500;
    line-height: 1;
    font-size: 16px;
}

.header-btn-flex {
    align-items: center;
}

@media only screen and (max-width: 460px) {
    .header-btn-flex {
        flex-direction: column;
        align-items: baseline;
        gap: 1rem;
    }
}

.footer-btn-flex {
    align-items: center;
}

@media only screen and (max-width: 460px) {
    .footer-btn-flex {
        flex-direction: column;
        align-items: baseline;
    }
}

/* side Menu */

.res-side-menu {
    padding: 1rem;
}

.res-mid-gap {
    gap: 0.7rem;
}

.fs-sign-out {
    font-size: 1.5em;
}

.res-pad-start {
    padding-left: 0.5rem;
}

.font-side-menu {
    font-size: 1.25rem;
}

/* responsive on 1199 */
@media screen and (max-width: 1199px) {
    .res-side-menu {
        padding: 0.25rem;
    }

    .res-mid-gap {
        gap: 0.1rem;
    }

    .res-sign-out {
        padding-left: 0rem !important;
        gap: 0rem !important;
    }

    .fs-sign-out {
        font-size: calc(1.3rem + .6vw) !important;
    }

    .res-pad-start {
        padding-left: 0rem;
    }

    .font-side-menu {
        font-size: 1.1rem;
    }
}


.btn-bg-blue {
    background-color: #194DAB;
    border: 2px solid #194DAB;
}

/* Profile Page CSS */
.profile-btns {
    display: flex;
    align-items: center;
    justify-content: end;
    gap: 10px;
}

@media only screen and (max-width: 340px) {
    .profile-btns {
        flex-direction: column;
    }
}


.profile-card {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
}

@media only screen and (max-width: 991px) {
    .profile-card {
        grid-template-columns: repeat(1, 1fr);
    }
}

.profile-grid-items {
    background-color: #FFF;
    border-radius: 7px;
    box-shadow: 0px 4px 7.199999809265137px 0px #00000008;
    position: relative;
}

.profile-grid {
    grid-template-columns: repeat(2, 1fr);
}


@media only screen and (max-width: 1199px) {
    .profile-grid {
        grid-template-columns: repeat(1, 1fr);
    }
}

@media only screen and (max-width: 992px) {
    .profile-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media only screen and (max-width: 768px) {
    .profile-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media only screen and (max-width: 396px) {
    .profile-grid {
        grid-template-columns: repeat(1, 1fr);
    }
}

.c-menu>.nav-item:hover {
    background-color: #EAF1FF;
    border-radius: 0.375rem;
}

.side-menu-item:hover {
    background-color: #EAF1FF !important;
    box-shadow: inset 2px 1px 4px #EAF1FF;
    transition: 0.3s ease;
    color: #6C757D;
}

.c-bg-light-blue {
    background-color: #194dab;
border-radius: 3px;
    color: #FFF !important;
}

.c-bg-light-blue:hover {
    background-color: #194dab !important;
    color: #FFF !important;
}



.card {
    border-radius: 0.625rem;
    border: 1px solid #eff2f5;
}

.card-footer {
    border: 1px solid #eff2f5;
    border-radius: 0 0 0.625rem 0.625rem !important;
}

.card-header {
    border: 1px solid #eff2f5;
    border-radius: 0.625rem 0.625rem 0 0 !important;
}

body {
    background-color: #F8F7FC !important;
    display: flex;
    flex-direction: column;
    /* height: 100vh; */
}

.round-30px {
    border-radius: 30px !important;
}

.badge-light-orange {
    color: #ff7640;
    background-color: #ff76401a;
}

.badge-light-pine {
    color: #57af9f;
    background-color: #57af9f1a;
}

.bg-gred {
    background-color: hsl(195deg 80.03% 38.97%) !important;
}

.bg-dark-gray {
    background: #6C757D !important;
}

.form-control-border-light {
    border: 1.5px #ebf0f6 solid !important;
}


input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

/* Project Page CSS */

.status-bg-purple {
    background-color: #ECE9F4;
    color: #4E3397;
    width: fit-content;
    margin: 0 auto;
}

.status-bg-red {
    background-color: #FFDEDE;
    color: #D72500;
    width: fit-content;
    margin: 0 auto;
}

.status-bg-blue {
    background-color: #EBF3FA;
    color: #407EB6;
    width: fit-content;
    margin: 0 auto;
}

.status-bg-green {
    background-color: #D6EEEC;
    color: #0B8E85;
    width: fit-content;
    margin: 0 auto;
}

/* Billing Profie */

.billing-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
}

@media only screen and (max-width: 991px) {
    .billing-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media only screen and (max-width: 671px) {
    .billing-grid {
        grid-template-columns: repeat(1, 1fr);
    }
}

.billing-grid-items {
    background-color: #FFF;
    border-radius: 7px;
    box-shadow: 0px 4px 7.199999809265137px 0px #00000008;
    position: relative;
    /* width: 439px; */
}

.border-verti {
    border-left: 2px black solid;
    width: 2px;
    height: 17px;
    opacity: 0.4;
}

.ribbon {
    position: absolute;
    top: 76px;
    right: -19px;
    background-color: #EAF1FF;
    color: #194DAB;
    font-size: 14px;
    font-weight: bold;
    padding: 2px 42px;
    transform: rotate(45deg);
    transform-origin: top right;
}

.support-ticket-ribbon {
    position: absolute;
    top: 90px;
    right: -20px;
    /* background-color: #EAF1FF;
    color: #194DAB; */
    font-size: 14px;
    font-weight: bold;
    padding: 4px 50px;
    transform: rotate(45deg);
    transform-origin: top right;
}

.support-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
}

@media only screen and (max-width: 1399px) {
    .support-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media only screen and (max-width: 1199px) {
    .support-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* .support-ticket-width-btn-status {
    width: 97px;
}

.support-ticket-width-btn-action {
    width: 150px;
} */

/* .support-ticket-ribbon {
    position: absolute;
    top: 90px;
    right: -20px;
    background-color: #EAF1FF;
    color: #194DAB;
    font-size: 14px;
    font-weight: bold;
    padding: 4px 50px;
    transform: rotate(45deg);
    transform-origin: top right;
} */

/* transaction */

.transaction-head {
    align-items: center;
}

@media only screen and (max-width: 640px) {
    .transaction-head {
        flex-direction: column;
        align-items: baseline;
    }
}



/* Firefox */
input[type=number] {
    -moz-appearance: textfield;
}


.dropdown-no-radius {
    border-radius: 0 !important;
    width: 220px !important;
}

.no-radius {
    border-radius: 0 !important;
}

.padding-button {
    padding: 0rem;
    font-weight: 700 !important;
    border: none;
    background-color: transparent;
}

.DG-section-tab-content {
    display: none;
}

.DG-section-tab-content.active {
    display: block;
}

.sidemenu-btn {
    background-color: transparent !important;
}

.DG-menu a.active .DG-tab-heading {
    border-bottom: 2px solid #000;
}

.GD-section-tab-content {
    display: none;
}

.GD-section-tab-content.active {
    display: block;
}

.GD-menu {
    flex-wrap: wrap;
}

.GD-menu a.active .GD-tab {
    background-color: #194DAB !important;
}

.GD-menu a.active .GD-tab h4 {
    color: #FFF !important;
}

.GD-menu a.active .GD-tab i {
    color: #FFF !important;
}

.GD-tab-heading {
    font-size: 14px !important;
}

a {
    text-decoration: none;
}

.grid-items {
    grid-template-columns: repeat(5, 1fr);
}

.grid-design-gellery {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    grid-column-gap: 1rem;
    grid-row-gap: 1rem;
}

@media only screen and (max-width: 1400px) {
    .grid-design-gellery {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media only screen and (max-width: 767px) {
    .grid-design-gellery {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media only screen and (max-width: 445px) {
    .grid-design-gellery {
        grid-template-columns: repeat(1, 1fr);
    }
}

.grid-item-design-gellery {
    position: relative;
    width: 100%;
    height: 100%;
}

.grid-item-design-gellery img {
    cursor: pointer;
}

@media only screen and (max-width: 445px) {
    .grid-item-design-gellery {
        display: flex;
        align-items: center;
        justify-content: center;
    }
}

@media only screen and (max-width: 534px) {
    .generate-btn {
        flex-direction: column;
    }
}

.grid-detail {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    grid-column-gap: 4rem;
    grid-row-gap: 2rem;
}

@media only screen and (max-width: 1400px) {
    .grid-detail {
        grid-template-columns: repeat(3, 1fr);
        grid-column-gap: 2rem;
        grid-row-gap: 2rem;
    }
}

@media only screen and (max-width: 767px) {
    .grid-detail {
        grid-template-columns: repeat(2, 1fr);
        grid-column-gap: 1rem;
        grid-row-gap: 1rem;
    }
}

.brand-btn {
    align-items: center;
}

@media only screen and (max-width: 445px) {
    .grid-detail {
        grid-template-columns: repeat(1, 1fr);
    }

    .grid-item-details {
        width: 66%;
    }

    .brand-btn {
        flex-direction: column;
        align-items: baseline;
    }
}

.overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 192px;
    background: rgba(0, 0, 0, 0.6);
    display: none;
}

.grid-item-details {
    position: relative;
    width: 100%;
    height: 192px;
    /* padding-bottom: 100%; */
    /* background-size: cover; */
    background-position: center;
    background-repeat: no-repeat;
}

.image-container {
    position: relative;
    max-width: 100%;
}

.image-container img {
    width: 100%;
    height: auto;
    position: absolute;
}

.view-ellipsis {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    display: flex;
    justify-content: flex-end;
    align-items: flex-start;
    padding: 7px;
    box-sizing: border-box;
}

.view-icon {
    display: flex;
    cursor: pointer;
}

.view-menu {
    display: none;
    position: absolute;
    background-color: #fff;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    z-index: 1;
    right: 8px;
    top: 16%;
}

.view-menu ul {
    list-style: none;
    margin: 0;
    padding: 0;
}

.view-menu li {
    padding: 10px;
    cursor: pointer;
}

.view-menu li:hover {
    background-color: #f0f0f0;
}

/* Carousel */
.carousel {
    overflow: hidden;
    width: 100%;
    position: relative;
}

.carousel-container {
    display: flex;
    /* width: calc(100% * 4); */
    gap: 10px;
    transition: transform 0.5s ease-in-out;
}

.carousel-item {
    flex: 0 0 23%;
    /* width: 25%; */
    display: block !important;
    margin-right: 0 !important;
}

.carousel-btn {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    z-index: 1;
    background-color: #FFF;
}

.carousel-btn.prev {
    left: 0;
}

.carousel-btn.next {
    right: 0;
}

#myImg {
    border-radius: 5px;
    cursor: pointer;
    transition: 0.3s;
}

#myImg:hover {
    opacity: 0.7;
}

/* The Modal (background) */
.modal {

    background-color: rgba(0, 0, 0, 0.5);
}

.design-modal {
    padding-top: 160px;
}

/* Modal Content (image) */
.modal-content {
    margin: auto;
    display: block;
}

.content-width {
    width: 80%;
    max-width: 700px;
}

/* Caption of Modal Image */
#caption {
    margin: auto;
    display: block;
    width: 80%;
    max-width: 700px;
    text-align: center;
    color: #ccc;
    padding: 10px 0;
    height: 150px;
}

/* Add Animation */
.modal-content,
#caption {
    -webkit-animation-name: zoom;
    -webkit-animation-duration: 0.6s;
    animation-name: zoom;
    animation-duration: 0.6s;
}

@-webkit-keyframes zoom {
    from {
        -webkit-transform: scale(0)
    }

    to {
        -webkit-transform: scale(1)
    }
}

@keyframes zoom {
    from {
        transform: scale(0)
    }

    to {
        transform: scale(1)
    }
}

/* The Close Button */
.close {
    position: absolute;
    top: 115px;
    right: 35px;
    color: #f1f1f1;
    font-size: 40px !important;
    font-weight: bold;
    transition: 0.3s;
}

.close:hover,
.close:focus {
    color: #bbb;
    text-decoration: none;
    cursor: pointer;
}

.preview-image {
    cursor: pointer;
}

@media only screen and (max-width: 1200px) {
    .responsive-btn {
        flex-direction: column;
    }
}

/* License */

.licence-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    column-gap: 1rem;
    row-gap: 2rem;
}

/* @media only screen and (max-width: 991px) {
    .licence-grid {
        grid-template-columns: repeat(1, 1fr);
    }
} */

.licence-grid-items {
    background-color: #FFF;
    border-radius: 7px;
    box-shadow: 0px 4px 7.199999809265137px 0px #00000008;
}

.version-control {
    display: flex;
    align-items: center;
    gap: 3rem;
}

.licence-label {
    margin-bottom: 0.5rem;
}

@media only screen and (max-width: 1199px) {
    .licence-label {
        flex-direction: column;
    }

    .version-control {
        flex-direction: column;
        align-items: baseline;
        gap: 0.5rem;
    }
}

@media only screen and (max-width: 991px) {
    .licence-label {
        flex-direction: row;
    }

    .version-control {
        flex-direction: row;
        align-items: center;
        gap: 3rem;
    }
}

@media only screen and (max-width: 810px) {
    .licence-label {
        flex-direction: column;
    }

    /* .version-control {
        flex-direction: column;
        align-items: baseline;
        gap: 0.3rem;
    } */

    .licence-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media only screen and (max-width: 740px) {
    .version-control {
        flex-direction: column;
        align-items: baseline;
        gap: 0.3rem;
    }
}

@media only screen and (max-width: 620px) {
    .licence-grid {
        grid-template-columns: repeat(1, 1fr);
    }

    .version-control {
        flex-direction: row;
        align-items: center;
        gap: 3rem;
    }
}

@media only screen and (max-width: 385px) {
    .version-control {
        flex-direction: column;
        align-items: baseline;
        gap: 0.3rem;
    }
}

.active-bg:active {
    background-color: transparent !important;
}

.focus-bg:focus {
    box-shadow: none !important;
}

/* @media only screen and (max-width: 1199px) {
    .version-control {
        flex-direction: column;
        align-items: baseline;
        gap: 0.5rem;
    }
} */

/* Refer & Earn CSS */

.refer-btn-flex {
    align-items: center;
}

.refer-header-flex {
    align-items: center;
}

@media only screen and (max-width: 1199px) {
    .refer-btn-flex {
        align-items: baseline;
        flex-direction: column;
        gap: 1rem;
    }
}

@media only screen and (max-width: 991px) {
    .refer-btn-flex {
        align-items: center;
        flex-direction: row;
        gap: 1rem;
    }
}

@media only screen and (max-width: 767px) {
    .refer-btn-flex {
        align-items: baseline;
        flex-direction: column;
        gap: 1rem;
    }
}

.red-bg-color {
    background-color: #FFDEDE;
    color: #D72500;
}

.purple-bg-color {
    background-color: #ECE9F4;
    color: #4E3397;
}

.blue-bg-color {
    background-color: #EAF1FF;
    color: #194DAB;
}


.unorder-list {
    padding-bottom: 5px !important;
}

.refer-badge {
    align-items: center;
}

@media only screen and (max-width:1400px) {
    .refer-badge {
        flex-direction: column;
        align-items: baseline;
    }
}

@media only screen and (max-width:991px) {
    .refer-badge {
        flex-direction: row;
        align-items: center;
    }
}

@media only screen and (max-width:860px) {
    .refer-badge {
        flex-direction: column;
        align-items: baseline;
    }
}

@media only screen and (max-width:767px) {
    .refer-badge {
        flex-direction: row;
        align-items: center;
    }
}

@media only screen and (max-width:442px) {
    .refer-badge {
        flex-direction: column;
        align-items: baseline;
    }
}

/* .selection ul li {
    background-color: #FFDEDE;
    border-radius: 50%;
    padding-top: 0px;
    padding-bottom: 0px;
} */

/* coupon section CSS */
.coupon-text-color {
    color: #4B4B4B;
}

.text-underline {
    text-decoration: underline;
    color: #626262;
}

.show-content {
    display: block;
}

/* .content {
    display: none;
} */
