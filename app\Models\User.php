<?php

namespace App\Models;

use App\Models\Leadfor;
use App\Models\LeadAddress;
use App\Models\Cart;
use Laravel\Sanctum\HasApiTokens;
use Illuminate\Notifications\Notifiable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;

class User extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable;

    protected $table = 'lead';
    public $timestamps = false;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        "name",
        "designation",
        "company",
        "gst",
        "address",
        "email",
        "city",
        "mobile",
        "state",
        "website",
        "country",
        "pincode",
        "lead_for_ids",
        "parent_id",

    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        // 'password' => 'hashed',
    ];
    protected $appends = [
        'interested_in'
    ];

    public function addresses()
    {
        return $this->hasMany(LeadAddress::class, 'lead_id', 'id')->orderBy('isPrimary', 'desc');
    }

    public function getInterestedInAttribute()
    {
        return  Leadfor::whereIn('id', explode(',', $this->lead_for_ids))->get()->pluck('name')->toArray();
    }
    public function profiles()
    {
        return $this->hasMany(User::class, 'parent_id', 'id');
    }

    // public function AffiliateAccount()
    // {
    //     return $this->hasOne(AffiliateAccount::class, 'lead_id', 'id')->where('isApproved', 1);
    // }
    public function carts()
    {
        return $this->hasMany(Cart::class, 'lead_id', 'id')->where('parentCart_id', null);
    }
    public function CartWithAddons()
    {
        return $this->hasMany(Cart::class, 'lead_id', 'id');
    }

}
