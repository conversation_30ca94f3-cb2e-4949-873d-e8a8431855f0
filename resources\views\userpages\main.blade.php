@php
    $host = request()->getHttpHost();
    $hostdataArray = allDomains()[$host];
    $main_path = $hostdataArray['main_path'];
    $icon = $hostdataArray['icon'];
@endphp
@section('siteIcon', asset($icon))
@extends($main_path)
@section('title', 'Profile')
@section('PAGE-CSS')
    <link rel="stylesheet" href="{{ asset('assets/userpages/customer.css') }}">
    @php
        $marginMap = [
            'wabhai' => ['localhost:8001', 'wabhai.com', 'wabhai.orkia.in', 'responsive'],
            'primailer' => [
                'localhost:8002',
                'primailer.com',
                'primailer.orkia.in',
                '**************:4444',
                'responsive',
            ],
            'stickyfirst' => ['localhost:8003', 'stickyfirst.com', 'stickyfirst.orkia.in', 42],
            'ringcaster' => ['localhost:8004', 'ringcaster.com', 'ringcaster.orkia.in', 38],
            'pixayogi' => ['localhost:8005', 'pixayogi.com', 'pixayogi.orkia.in', 67],
            'rokdi' => ['localhost:8006', 'rokdi.com', 'rokdi.orkia.in', 65],
            'androsms' => ['localhost:8007', 'androsms.com', 'androsms.orkia.in', 37],
            'clatos' => ['localhost:8008', 'clatos.com', 'clatos.orkia.in', 142],
            'rapbooster' => ['localhost:8009', 'rapbooster.com', 'rapbooster.orkia.in', 43],
            'dunesfactory' => ['localhost:8010', 'dunesfactory.com', 'dunesfactory.orkia.in', null],
        ];

        $currentSite = null;
        foreach ($marginMap as $site => $domains) {
            if (in_array($host, $domains)) {
                $currentSite = $site;
                break;
            }
        }
    @endphp

    @if ($currentSite && isset($marginMap[$currentSite][3]))
        @if ($marginMap[$currentSite][3] === 'responsive')
            <style>
                @media only screen and (max-width: 600px) {
                    .c-margin {
                        margin-top: 100px !important;
                    }
                }

                @media only screen and (min-width: 600px) {
                    .c-margin {
                        margin-top: 100px !important;
                    }
                }

                @media only screen and (min-width: 768px) {
                    .c-margin {
                        margin-top: 100px !important;
                    }
                }

                @media only screen and (min-width: 992px) {
                    .c-margin {
                        margin-top: 100px !important;
                    }
                }

                @media only screen and (min-width: 1200px) {
                    .c-margin {
                        margin-top: 120px !important;
                    }
                }
            </style>
        @else
            <style>
                .c-margin {
                    margin-top: {{ $marginMap[$currentSite][3].'px' }};
                }
            </style>
        @endif
    @endif
    <style>
        .modal-backdrop {
            display: none !important;
        }
        
    </style>


    @yield('only-Page-css')
@endsection

@section('webpage')

    <div class="container-lg container-fluid p-0 mb-auto c-margin" id="main-page">

        <div class="d-flex justify-content-around gap-2">
            <div class="col-lg-3 d-none d-lg-inline-block pe-0">
                <div class="overflow-auto mb-5">
                    @include('userpages.sideMenu')
                </div>
            </div>
            <div class="col-lg-9 col-sm-12 col-md-12 p-0">
                @yield('userpagesection')

            </div>
        </div>
    </div>

    @yield('BillingProfileEditModal')


@endsection
