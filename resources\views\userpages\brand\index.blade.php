@extends('userpages.layouts.projBrandMain')
@section('title', 'Brands')

@section('proj-pages')

    <div class="bg-white d-flex align-items-center justify-content-between gap-3 pt-2 pb-2 pe-3 ps-3 mb-3"
        style="border-radius: 7px;">
        <h4 class="heading-font">Graphic Brands</h4>
        <a aria-label="link" href="{{ route('brand.create') }}" class="btn text-white btn-sm rounded-pill"
            style="background-color: #194DAB;">
            <i class="bi bi-plus"></i>
            Add Brand
        </a>
    </div>

    <div class="card">
        {{-- <div class="card-header  border-0 bg-white"> --}}
        {{-- <div class="card-title text-dark"> --}}
        {{-- <h2 class="fs-2">Brands ({{ $brands->count() }})
            </h2>
            <a aria-label="link" href="{{ route('brand.create') }}" class="btn btn-success">
                <span><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor"
                        class="bi bi-plus-lg" viewBox="0 0 16 16">
                        <path fill-rule="evenodd"
                            d="M8 2a.5.5 0 0 1 .5.5v5h5a.5.5 0 0 1 0 1h-5v5a.5.5 0 0 1-1 0v-5h-5a.5.5 0 0 1 0-1h5v-5A.5.5 0 0 1 8 2Z" />
                    </svg></span>
                Add Brand</a> --}}
        {{-- </div> --}}
        {{-- <div class="card-toolbar"> --}}
        {{-- <div class="me-3">
                    @php
                        $sortUrl = '';
                        if (isset($search)) {
                            $sortUrl = request()->url() . '?' . http_build_query(['sort' => $sort, 'search' => $search]);
                        } else {
                            $sortUrl = request()->url() . '?' . http_build_query(['sort' => $sort]);
                        }
                    @endphp
                    <a aria-label="link" href="{{ $sortUrl }}" class="btn btn-sm btn-primary btn-icon h-30px w-30px " data-bs-toggle="tooltip"
                        data-bs-placement="top" data-bs-title="Sort" data-bs-custom-class="tooltip-inverse">
                        <svg xmlns="http://www.w3.org/2000/svg" height="1em" style="fill: #ffffff"
                            viewBox="0 0 320 512"><!--! Font Awesome Free 6.4.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2023 Fonticons, Inc. -->
                            <path
                                d="M137.4 41.4c12.5-12.5 32.8-12.5 45.3 0l128 128c9.2 9.2 11.9 22.9 6.9 34.9s-16.6 19.8-29.6 19.8H32c-12.9 0-24.6-7.8-29.6-19.8s-2.2-25.7 6.9-34.9l128-128zm0 429.3l-128-128c-9.2-9.2-11.9-22.9-6.9-34.9s16.6-19.8 29.6-19.8H288c12.9 0 24.6 7.8 29.6 19.8s2.2 25.7-6.9 34.9l-128 128c-12.5 12.5-32.8 12.5-45.3 0z" />
                        </svg>
                    </a>
                </div> 
                
                <a aria-label="link" href="{{ route('brand.create') }}" class="btn btn-success">
                    <span><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor"
                        class="bi bi-plus-lg" viewBox="0 0 16 16">
                        <path fill-rule="evenodd"
                        d="M8 2a.5.5 0 0 1 .5.5v5h5a.5.5 0 0 1 0 1h-5v5a.5.5 0 0 1-1 0v-5h-5a.5.5 0 0 1 0-1h5v-5A.5.5 0 0 1 8 2Z" />
                    </svg></span>
                    Add Brand</a>
                --}}
        {{-- </div> --}}
        {{-- </div> --}}
        <div class="card-body p-4">


            <div class="row row-gap-2 row-cols-1  row-cols-md-2 row-cols-lg-2 row-cols-xl-3">
                @forelse ($brands as $item)
                    <div class="col    ">

                        <div class="border m-auto rounded-2   p-2  d-flex h-100">
                            <div class="d-flex gap-2">
                                @if ($item->logo)
                                    <div class="  m-auto">
                                        <img src="{{ s3_fileShow($item->logo, 'graphic_lead_brand') }}" alt=""
                                            class="img-fluid rounded-0" style="max-height: 60px">
                                    </div>
                                @endif
                                <div class=" pe-2 text-start">
                                    <div class="fs-6  text-dark  text-nowrap d-block text-truncate">
                                        {{ $item->name ?? '-' }}
                                    </div>
                                    <div class="text-gray-400   ">
                                        {{ humanDate($item->ts) }}
                                    </div>
                                </div>
                            </div>
                            <div class=" m-auto  text-end me-1">
                                <a aria-label="link" href="{{ route('brand.show', ['brand' => $item->id]) }}"
                                    class="btn btn-secondary text-nowrap btn-sm ">
                                    <i class="bi bi-gear"></i>
                                    Details
                                </a>
                            </div>
                        </div>

                    </div>
                @empty
                    <div class="mt-3 p-1 bg-white" style="border-radius: 7px;">
                        <div class="d-flex align-items-center flex-column bg-white justify-content-center ">
                            <img src="{{ asset('assets/userpages/images/billing.png') }}" alt="blank_page_img"
                                class="img-fluid">
                        </div>
                    </div>
                @endforelse

            </div>

        </div>
    </div>
@endsection
