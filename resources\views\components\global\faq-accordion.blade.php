<div class="accordion accordion-flush" id="customAccordion"
    style="--accordion-bg: {{ $collapsedColor }}; --accordion-active-bg: {{ $activeColor }};">
    @foreach ($faqs as $index => $faq)
        @php
            $headingId = 'heading' . $index;
            $collapseId = 'collapse' . $index;
            $isFirst = $index === 0;
        @endphp

        <div class="accordion-item mb-3">
            <h2 class="accordion-header" id="{{ $headingId }}">
                <button class="accordion-button {{ $isFirst ? '' : 'collapsed' }}" type="button" data-bs-toggle="collapse"
                    data-bs-target="#{{ $collapseId }}" aria-expanded="{{ $isFirst ? 'true' : 'false' }}"
                    aria-controls="{{ $collapseId }}">
                    {{ $faq['question'] }}
                </button>
            </h2>
            <div id="{{ $collapseId }}" class="accordion-collapse collapse {{ $isFirst ? 'show' : '' }}"
                aria-labelledby="{{ $headingId }}" data-bs-parent="#customAccordion">
                <div class="accordion-body">
                    {!! $faq['answer'] !!}
                </div>
            </div>
        </div>
    @endforeach
</div>
