:root {
  --bs-tertiary-bg: #FF6F14;
}

/* #customRange1 {
  background-color: #FF6F14;
}

#customRange1:focus,
#customRange1:active {
  background-color: #fd8a42;
} */

@media screen and (-webkit-min-device-pixel-ratio:0) {
  input[type='range'] {
    -webkit-appearance: none;
    background-color: white;
    border: 1px solid #CACACA
  }

  input[type='range']::-webkit-slider-runnable-track {
    -webkit-appearance: none;
  }

  input[type='range']::-webkit-slider-thumb {
    -webkit-appearance: none;
    cursor: ew-resize;
    background: white;
    border: 1px solid #CACACA
  }

}

.primary-color {
  color: #1A3760;
}

body {
  padding-top: 5%;
}

@media only screen and (min-width: 320px) {

  body {
    padding-top: 15%;
  }

}

@media only screen and (min-width: 768px) {

  body {
    padding-top: 10%;
  }

}

@media only screen and (min-width: 991px) {

  body {
    padding-top: 6%;
  }

}

@media only screen and (min-width: 992px) {

  body {
    padding-top: 10%;
  }

}

@media only screen and (min-width: 1199px) {

  body {
    padding-top: 8%;
  }

}

@media only screen and (min-width: 1299px) {

  body {
    padding-top: 6%;
  }

}

@media only screen and (min-width: 2560px) {

  body {
    padding-top: 4%;
  }

}

.header-div>.header-ul {
  gap: 1.5rem !important;
}

.header-nav>.header-nav-text {
  font-size: 1.3rem !important;
}

.schedule-nav>.schedule-text {
  font-size: 1rem !important;
}

.text-blue {
  color: #1a3760;
}

.text-orange {
  color: #ff6f14;
}

p {
  color: #777777;
}

h1 {
  font-size: 50px;
  font-weight: 600;
  line-height: 3.5rem;
}

@media only screen and (max-width: 991px) {
  h1 {
    font-size: 40px;
    font-weight: 600;
    line-height: 3.5rem;
  }
}

@media only screen and (max-width:768px) {
  h1 {
    font-size: 40px;
    font-weight: 600;
    line-height: 2.5rem;
  }
}

h3 {
  /* color: #1A3760; */
  font-size: 40px;
  font-weight: 600;
  line-height: 3.5rem;

}

@media only screen and (max-width: 991px) {
  h3 {
    color: #1A3760;
    font-size: 30px;
    font-weight: 600;
    line-height: 2.5rem;
  }
}

h2 {
  color: #1A3760;
  font-size: 42px;
  font-weight: bolder;
  line-height: 3.1rem;
}

@media only screen and (max-width: 991px) {
  h2 {
    color: #1A3760;
    font-size: 35px;
    font-weight: bolder;
    line-height: 2.8rem;
  }
}

h4 {
  color: #1A3760;
  font-size: 33px;
  font-weight: 600;
  line-height: 3rem;

}

/*------------------------------------------------- NAVBAR css Starts --------------------------------------------------------- */
a.underline-hover-effect {
  text-decoration: none;
  color: inherit;
}

.underline-hover-effect {
  display: inline-block;
  padding-bottom: 0.25rem;
  position: relative;
}

.underline-hover-effect::before {
  content: "";
  position: absolute;
  left: 0;
  bottom: 0;
  width: 0;
  height: 2px;
  background-color: #ff6f14;
  transition: width 0.25s ease-out;
}

.underline-hover-effect:hover::before {
  width: 100%;
}

@media only screen and (max-width: 991px) {
  .horzontal-line {
    border: 1px solid #ff6f14;
  }

  .nav-items-responsive {
    align-items: flex-start !important;
  }
}

.orange-btn {
  background: #ff6f14;
  box-shadow: 0px 4px 4px 0px #FFD4C2;
  width: fit-content;
  cursor: pointer;
  transition: .3s;
}

.orange-btn:hover {
  background: #eb5a00;
}

.mobile-nav {
  display: flex;
  justify-content: space-between;
}

.max-width-fit {
  max-width: fit-content;
}

.navbar-toggler {
  padding: var(--bs-navbar-toggler-padding-y) var(--bs-navbar-toggler-padding-x);
  font-size: var(--bs-navbar-toggler-font-size);
  line-height: 1;
  color: rgb(255 255 255);
  background-color: transparent;
  border: var(--bs-border-width) solid rgb(255 255 255 / 15%);
  border-radius: var(--bs-navbar-toggler-border-radius);
  transition: var(--bs-navbar-toggler-transition);
}

.nav-items-responsive {
  align-items: center;
}

/*------------------------------------------------- NAVBAR CSS Ends --------------------------------------------------------- */


/*------------------------------------------------- FOOTER CSS STARTS --------------------------------------------------------- */

ul {
  list-style-type: none;
  margin: 0;
}

.para-color-list {
  color: #ffffff;
  font-size: 16px;
  font-weight: 400;
  line-height: 40px;
  margin-bottom: 6px;
}

.para-color-list:hover {

  color: white;

}

hr {
  border-width: 3px
}

/* .card {
  padding: 2% 7%
} */

.social>i {
  padding: 1%;
  font-size: 15px
}

.social>i:hover {
  color: #286fb4;
  cursor: pointer
}

.divider {
  border-top: 1px solid #e0e0e2
}

.logo {
  height: 70px;
}

.icon-height {
  height: 30px;
}

.flex {
  display: flex;
  padding-top: 15px;
}

.padding-left {
  padding: 0px 0px 0px 11px;
}

.height {
  height: 30px;

}

.font-size20 {
  font-size: 20px;
}

@media only screen and (max-width: 991px) {
  .footer-flex {
    display: flex;
    flex-direction: column;
  }

  .footer-JC {
    justify-content: flex-start !important;
  }
}

/*------------------------------------------------- FOOTER CSS ENDS --------------------------------------------------------- */

/*------------------------------------------------- AUDIO PLAYER CSS STARTS --------------------------------------------------------- */

*:focus {
  outline: none;
}

.button:hover {
  background-color: #d6d6de00 !important;
}

#app-cover {
  /* position: absolute;
    top: 50%;
    right: 0;
    left: 0; */
  width: 430px;
  height: 100px;
  margin: -4px auto;
}

@media only screen and (max-width: 520px) {
  #app-cover {
    width: 245px;
    height: 100px;
    margin: -4px auto;
  }
}

#player {
  position: relative;
  height: 100%;
  z-index: 3;
}

#player-track {
  position: absolute;
  top: 0;
  right: 15px;
  left: 15px;
  padding: 13px 22px 10px 184px;
  /* background-color: #fff7f7; */
  border-radius: 15px 15px 0 0;
  transition: 0.3s ease top;
  z-index: 1;
}

#player-track.active {
  top: -92px;
}

#album-name {
  color: #54576f;
  font-size: 17px;
  font-weight: bold;
}

#track-name {
  color: #acaebd;
  font-size: 13px;
  margin: 2px 0 13px 0;
}

#track-time {
  height: 12px;
  margin-bottom: 3px;
  overflow: hidden;
}

#current-time {
  float: left;
}

#track-length {
  float: right;
}

#current-time,
#track-length {
  color: transparent;
  font-size: 11px;
  background-color: #ffe8ee;
  border-radius: 10px;
  transition: 0.3s ease all;
}

#track-time.active #current-time,
#track-time.active #track-length {
  color: #f86d92;
  background-color: transparent;
}

#s-area,
#seek-bar {
  position: relative;
  height: 4px;
  border-radius: 4px;
}

#s-area {
  background-color: #ffe8ee;
  cursor: pointer;
}

#ins-time {
  position: absolute;
  top: -29px;
  color: #fff;
  font-size: 12px;
  white-space: pre;
  padding: 5px 6px;
  border-radius: 4px;
  display: none;
}

#s-hover {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  opacity: 0.2;
  z-index: 2;
}

#ins-time,
#s-hover {
  background-color: #3b3d50;
}

#seek-bar {
  content: "";
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  width: 0;
  background-color: #fd6d94;
  transition: 0.2s ease width;
  z-index: 1;
}

#player-content {
  position: relative;
  height: 100%;
  background-color: #fff;
  border-radius: 15px;
  z-index: 2;
}

#album-art {
  position: absolute;
  top: -40px;
  width: 115px;
  height: 115px;
  margin-left: 40px;
  transform: rotateZ(0);
  transition: 0.3s ease all;
  box-shadow: 0 0 0 10px #fff;
  border-radius: 50%;
  overflow: hidden;
}

@media only screen and (max-width: 520px) {
  #album-art {
    position: absolute;
    top: 30px;
    width: 50px;
    height: 50px;
    margin-left: 20px;
    transform: rotateZ(0);
    transition: 0.3s ease all;
    box-shadow: 0 0 0 10px #fff;
    border-radius: 50%;
    overflow: hidden;
  }
}

#album-art.active {
  top: -60px;
  box-shadow: 0 0 0 4px #fff7f7, 0 30px 50px -15px #afb7c1;
}

#album-art:before {
  content: "";
  position: absolute;
  top: 50%;
  right: 0;
  left: 0;
  width: 20px;
  height: 20px;
  margin: -10px auto 0 auto;
  background-color: #d6dee7;
  border-radius: 50%;
  box-shadow: inset 0 0 0 2px #fff;
  z-index: 2;
}

#album-art img {
  display: block;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  z-index: -1;
}

#album-art img.active {
  opacity: 1;
  z-index: 1;
}

#album-art.active img.active {
  z-index: 1;
  animation: rotateAlbumArt 3s linear 0s infinite forwards;
}

@keyframes rotateAlbumArt {
  0% {
    transform: rotateZ(0);
  }

  100% {
    transform: rotateZ(360deg);
  }
}

#buffer-box {
  position: absolute;
  top: 50%;
  right: 0;
  left: 0;
  height: 13px;
  color: #1f1f1f;
  font-size: 13px;
  text-align: center;
  font-weight: bold;
  line-height: 1;
  padding: 6px;
  margin: -12px auto 0 auto;
  background-color: rgba(255, 255, 255, 0.19);
  opacity: 0;
  z-index: 2;
}

#album-art img,
#buffer-box {
  transition: 0.1s linear all;
}

#album-art.buffering img {
  opacity: 0.25;
}

#album-art.buffering img.active {
  opacity: 0.8;
  filter: blur(2px);
  -webkit-filter: blur(2px);
}

#album-art.buffering #buffer-box {
  opacity: 1;
}

#player-controls {
  width: 250px;
  height: 100%;
  margin: 0 5px 0 141px;
  float: right;
  overflow: hidden;
}

@media only screen and (max-width: 520px) {
  #player-controls {
    width: 176px;
    height: 100%;
    margin: 0 5px 0 141px;
    float: right;
    overflow: hidden;
  }
}

.control {
  width: 33.333%;
  float: left;
  padding: 12px 0;
}

.button {
  width: 26px;
  height: 26px;
  padding: 25px;
  background-color: #fff;
  border-radius: 6px;
  cursor: pointer;
}

.button i {
  display: block;
  color: #1a3760;
  font-size: 26px;
  text-align: center;
  line-height: 1;
}

.button,
.button i {
  transition: 0.2s ease all;
}

.button:hover {
  background-color: #d6d6de;
}

.button:hover i {
  color: #303760;
}

/*------------------------------------------------- AUDIO PLAYER CSS ENDS --------------------------------------------------------- */

/*------------------------------------------------- HOME PAGE  CSS STARTS --------------------------------------------------------- */

.info-box {
  box-shadow: 0 0 10px 0 rgba(183, 183, 183, .2);
  transition: 0.3s, border 0.3s, border-radius 0.3s, box-shadow 0.3s;
  margin: 20px 0 20px 0;
  padding: 25px 25px 25px 25px;

}

@media only screen and (max-width: 991px) {
  .info-box {
    box-shadow: 0 0 10px 0 rgba(183, 183, 183, .2);
    transition: 0.3s, border 0.3s, border-radius 0.3s, box-shadow 0.3s;
    margin: 0px 0 20px 0;
    padding: 25px 25px 25px 25px;
  }
}

@media only screen and (max-width: 425px) {
  .info-box {
    box-shadow: 0 0 10px 0 rgba(183, 183, 183, .2);
    transition: 0.3s, border 0.3s, border-radius 0.3s, box-shadow 0.3s;
    margin: 0px 0 15px 0;
    padding: 25px 25px 25px 25px;
  }
}

.info-box-2 {
  border-style: solid;
  border-width: 0 0 2.5px 0;
  border-color: #FF6F14;
  box-shadow: 0 0 10px 0 rgba(183, 183, 183, .2);
  transition: 0.3s, border 0.3s, border-radius 0.3s, box-shadow 0.3s;
  margin: 0 0 20px 0;
  padding: 20px 20px 20px 20px;
  background: white;
}

.grid-2 {
  display: inline-grid;
  column-gap: 2rem;
  grid-template-columns: repeat(2, 1fr);
}

@media only screen and (max-width: 520px) {

  .grid-2 {
    display: inline-grid;
    column-gap: 2rem;
    grid-template-columns: repeat(1, 1fr);
  }

}

.grid-3 {
  display: inline-grid;
  column-gap: 2rem;
  grid-template-columns: repeat(3, 1fr);
}

@media only screen and (max-width: 768px) {

  .grid-3 {
    display: inline-grid;
    column-gap: 2rem;
    grid-template-columns: repeat(1, 1fr);
  }

}

.pt {
  padding: 50px 0px 50px 0px;
}

@media only screen and (max-width: 991px) {
  .pt {
    padding: 50px 0px 50px 0px;
  }
}

@media only screen and (max-width: 520px) {
  .pt {
    padding: 0px 0px 50px 0px;
  }
}

/* FORM CSS  */

.form-style {
  box-shadow: 0 0 10px 0 rgba(183, 183, 183, .2);
  transition: 0.3s, border 0.3s, border-radius 0.3s, box-shadow 0.3s;
  padding: 30px 30px 35px 30px;
  background-color: white;
}

.form-control:focus {
  border-color: #86b7fe;
  outline: 0;
  box-shadow: 0 0 0 0.25rem rgba(255, 255, 255, 0.25);
}

.w1 {
  width: 20%;
}

@media only screen and (max-width: 1399px) {
  .w1 {
    width: 25%;
  }
}

@media only screen and (max-width: 1299px) {
  .w1 {
    width: 25%;
  }
}

@media only screen and (max-width: 1199px) {
  .w1 {
    width: 30%;
  }
}

@media only screen and (max-width: 1024px) {
  .w1 {
    width: 30%;
  }
}

@media only screen and (max-width: 991px) {
  .w1 {
    width: 18%;
  }
}

@media only screen and (max-width: 768px) {
  .w1 {
    width: 24%;
  }
}

@media only screen and (max-width: 425px) {
  .w1 {
    width: 22%;
  }
}

@media only screen and (max-width: 320px) {
  .w1 {
    width: 30%;
  }
}

.w2-convenient {
  width: 34%;
}

@media only screen and (max-width: 1399px) {
  .w2-convenient {
    width: 40%;
  }
}

@media only screen and (max-width: 1299px) {
  .w2-convenient {
    width: 40%;
  }
}

@media only screen and (max-width: 1199px) {
  .w2-convenient {
    width: 50%;
  }
}

@media only screen and (max-width: 1024px) {
  .w2-convenient {
    width: 50%;
  }
}

@media only screen and (max-width: 991px) {
  .w2-convenient {
    width: 22%;
  }
}

@media only screen and (max-width: 768px) {
  .w2-convenient {
    width: 30%;
  }
}

@media only screen and (max-width: 425px) {
  .w2-convenient {
    width: 26%;
  }
}

@media only screen and (max-width: 375px) {
  .w2-convenient {
    width: 30%;
  }
}

@media only screen and (max-width: 325px) {
  .w2-convenient {
    width: 38%;
  }
}

.carousel-indicators [data-bs-target] {
  background-color: #ff6f14 !important;
  width: 10px;
  height: 10px;
  border-radius: 50%;
}

.carousel-indicators {
  position: absolute;
  right: 0;
  bottom: 0;
  left: 0;
  top: 335px;
  z-index: 2;
  display: flex;
  justify-content: center;
  padding: 0;
  margin-right: 15%;
  margin-bottom: 1rem;
  margin-left: 15%;
}

@media only screen and (max-width: 1199px) {
  .carousel-indicators {
    top: 355px;
  }
}


@media only screen and (max-width: 991px) {
  .carousel-indicators {
    top: 320px;
  }
}

@media only screen and (max-width: 876px) {
  .carousel-indicators {
    top: 335px;
  }
}

@media only screen and (max-width: 500px) {
  .carousel-indicators {
    top: 350px;
  }
}

@media only screen and (max-width: 380px) {
  .carousel-indicators {
    top: 380px;
  }
}

/*------------------------------------------------- HOME PAGE CSS ENDS --------------------------------------------------------- */


/* FAQ'S SECTION  */

.faq-accordion .faq-heading {
  font-weight: bold;
  font-size: 18px;
}

.faq-heading {
  line-height: 30px;
  color: #3c3c3c;
  align-items: start;
}

.faq-div p {
  line-height: 24px;
  padding: 10px 20px 10px 20px;
  margin-top: 2px;
}

.mb-3-para p {
  margin-bottom: 0rem !important;
}

.faq-accordion {
  display: flex;
  flex-direction: column;
}

.faq-accordion button {
  text-decoration: none !important;
}

.faq-btn:first-child:active,
:not(.faq-btn-check)+.faq-btn:active {
  background-color: white !important;
}

.faq-accordion a {
  text-decoration: none !important;
}

.faq-card-header {
  display: flex;
  background-color: #ffffff;
  color: #717171;
  cursor: pointer;
  align-items: self-start;
  padding: 20px;
  box-shadow: 0 0 10px 0 rgba(183, 183, 183, .2);
}

.faq-para {
  box-shadow: 3px 5px 14px 0px rgba(0, 0, 0, 0.1);
}

.btn[aria-expanded="true"] {
  background-color: white;
}

.btn[aria-expanded="true"] .faq-heading {
  color: #1a3760;
}

.btn[aria-expanded="true"]:hover {
  background-color: white;
}

.btn[aria-expanded="true"] .faq-card-header {
  background-color: transparent;
}

@keyframes fade-in {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

.bg-faq {
  background: #FF6F14;
}

.fa-facebook {
  color: #cdcdcd;
}

.fa-facebook:hover {
  color: #ff6f14;
}

.fa-instagram {
  color: #cdcdcd;
}

.fa-instagram:hover {
  color: #ff6f14;
}

/* FAQ'S SECTION  */


/********************************* FEATURE PAGE  *************************/
.margin-f {
  margin-top: 20px;
  margin-bottom: -20px;
}

/* contact form  */
.email-div {
  background: #ffffff;
  border-radius: 20px;
  width: 35vw;
}

.email-center {
  align-items: center;
}

.bg-light-blue {
  background: #e0efff3b;
}

.img-h-500 {
  height: 500px;
}

/* contact text-flelds  */


.input-form-style {
  width: 100%;
  padding: 12px;
  border: 1px solid #ccc;
  border-radius: 8px;
  border-style: none none solid none;
  background: #f0f0f000;
  box-sizing: border-box;
  margin-top: 6px;
  margin-bottom: 16px;
  resize: vertical;
  outline: none !important;
}

.container1 {
  border-radius: 20px;
  background-color: #FFEEE7;
  padding: 60px;
  border: 1px solid #000000;
  width: 645px;
}

@media screen and (max-width: 768px) {
  .container1 {
    padding: 36px;
    width: 450px;
  }
}

@media only screen and (max-width: 320px) {
  .container1 {
    padding: 15px;
    width: 290px !important;
  }
}

@media only screen and (max-width: 425px) {
  .container1 {
    padding: 15px;
    width: 330px;
  }
}

.fly-img {
  background-repeat: no-repeat;
  background-size: 200px;
  background-position: 88% 50%;
}

@media only screen and (max-width: 1399px) {
  .fly-img {
    background-repeat: no-repeat;
    background-size: 200px;
    background-position: 95% 50%;
  }
}

@media only screen and (max-width: 1199px) {
  .fly-img {
    background-repeat: no-repeat;
    background-size: 200px;
    background-position: 105% 50%;
  }
}

@media only screen and (max-width: 991px) {
  .fly-img {
    background-repeat: no-repeat;
    background-size: 0px;
    background-position: 109% 50%;
  }
}

input::placeholder {
  color: #938d8d;
  opacity: 1;
}

input:focus {
  color: #000000 !important;
}

:focus {
  outline: 0px solid transparent !important;
}

textarea {
  border: none;
  border-bottom: 1px solid #938d8d;
  outline: none;
  transition: border-bottom 0.3s;
}

textarea::placeholder {
  color: #938d8d;
}

select:focus {
  outline: none;
}

.contact-info {
  font-size: 17px;
}

@media only screen and (max-width: 1399px) {
  .contact-info {
    font-size: 17px;
  }
}

@media only screen and (max-width: 1199px) {
  .contact-info {
    font-size: 17px;
  }
}

@media only screen and (max-width: 991px) {

  .contact-info {
    font-size: 17px;
  }
}

/* contact text-flelds  */
/* Blog CSS Start */

@media only screen and (max-width: 1024px) {
  .px-small-pad {
    padding-left: 0.5rem !important;
    padding-right: 0.5rem !important;
  }
}

/* Blog CSS End */

/* Price Section Start */
.price-para {
  font-size: 13px;
}

.price-listing {
  list-style-type: none;
  padding-left: 0;
}

.price-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 1rem;
}

.price-EM-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1rem;
}

.price-WMS-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
}

.price-items-1 {
  padding-top: 27px;
  margin-top: 20px !important;
  padding-bottom: 27px;
  padding-left: 23px;
  padding-right: 23px;
  border-radius: 16px;
  background: #F8FAFB;
}

.price-items-1-btn {
  border-radius: 5px;
  border: 1px solid #D6D6D6;
  background: #EBEBEB;
}

.price-items-2 {
  padding-top: 27px;
  margin-top: 20px !important;
  padding-bottom: 27px;
  padding-left: 23px;
  padding-right: 23px;
  border-radius: 16px;
  background: #f5f6f6;
}

.price-items-2-btn {
  border-radius: 5px;
  color: #FFF;
  background: #ff6f14;
  border: 1px solid #ff6f14;
}

.price-items-3 {
  padding-top: 27px;
  margin-top: 20px !important;
  padding-bottom: 27px;
  padding-left: 23px;
  padding-right: 23px;
  border-radius: 16px;
  background: #ffeee7;
}

.price-items-3-btn {
  border-radius: 5px;
  color: #FFF;
  background: #ff6f14;
  border: 1px solid #ff6f14;
}

.price-list {
  font-size: 14px;
}

.most-popular-blue {
  border-radius: 10px 10px 10px 0px;
  background: #E13362;
  color: white;
  border: 1px solid #E13362;
  position: absolute;
  z-index: -1;
  margin-top: -60px;
  margin-left: -23px;
}

.most-popular-purple {
  border-radius: 10px 10px 10px 0px;
  background: #ff6f14;
  color: white;
  border: 1px solid #ff6f14;
  position: absolute;
  z-index: -1;
  margin-top: -60px;
  margin-left: -23px;
}

.most-popular-grey {
  border-radius: 10px 10px 10px 0px;
  background: #675F72;
  color: white;
  border: 1px solid #675F72;
  position: absolute;
  z-index: -1;
  margin-top: -60px;
  margin-left: -23px;
}

@media only screen and (max-width: 1199px) {
  .price-EM-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .price-EM-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .price-EM-div {
    display: none;
  }
}

@media only screen and (max-width: 991px) {
  .price-grid {
    grid-template-columns: repeat(3, 1fr);
  }

  .price-WMS-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media only screen and (max-width: 768px) {
  .price-grid {
    grid-template-columns: repeat(2, 1fr);
    row-gap: 2.5rem;
  }

  .price-WMS-grid {
    grid-template-columns: repeat(2, 1fr);
    row-gap: 2.5rem;
  }
}

@media only screen and (max-width: 566px) {
  .price-grid {
    grid-template-columns: repeat(1, 1fr);
    row-gap: 2.5rem;
  }

  .price-WMS-grid {
    grid-template-columns: repeat(1, 1fr);
    row-gap: 2.5rem;
  }

  .price-EM-grid {
    grid-template-columns: repeat(1, 1fr);
    row-gap: 2.5rem;
  }
}

.form-check-input:checked {
  background-color: #FF6F14;
  /* toggl switch */
  border-color: #0d6efd00;
}

.form-check-input:focus {
  border-color: #86b6fe00;
  outline: 0;
  box-shadow: 0 0 0 0.25rem rgba(13, 109, 253, 0);
}

.form-range {
  width: 100%;
  height: 0rem;
  padding: 0;
  background-color: transparent;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}

.form-switch .form-check-input {
  --bs-form-switch-bg: url(data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='rgba%280, 0, 0, 0.25%29'/%3e%3c/svg%3e);
  width: 60px;
  height: 25px;
  margin-left: -2.5em;
  background-image: var(--bs-form-switch-bg);
  background-position: left center;
  border-radius: 2em;
  transition: background-position .15s ease-in-out;
}

.d-form-8 {
  display: grid;
  grid-template-columns: repeat(8, auto);
}

@media only screen and (max-width: 600px) {

  .d-form-8 {
    display: grid;
    grid-template-columns: repeat(4, auto);
  }

}

.d-form-10 {
  display: grid;
  grid-template-columns: repeat(10, auto);
}

@media only screen and (max-width: 600px) {

  .d-form-10 {
    display: grid;
    grid-template-columns: repeat(4, auto);
  }

}

/* Price Section End */

.text-dark-color {
  color: #bfbfbf;
  margin-bottom: 10px;
  font-size: 20px;
}

.text-help-color {
  color: #171717;
  margin-bottom: 10px;
  font-size: 18px;
}

.list-pricing {
  font-size: 27px;
  font-weight: 400;
  line-height: 32px;
  letter-spacing: 0em;
}

@media only screen and (max-width: 1199px) {

  .list-pricing {
    font-size: 20px;
    font-weight: 400;
    line-height: 32px;
    letter-spacing: 0em;
  }

}

.robo-h {
  font-size: 33px;
  font-weight: 600;
  /* line-height: 3rem; */
}

@media only screen and (max-width: 1199px) {
  .robo-h {
    font-size: 23px;
  }

}

.robo-style {
  margin-top: -50px;
  margin-right: 50px;
  margin-left: 50px;
  padding: 0px 20px 0px 20px;
}

@media only screen and (max-width: 1199px) {

  .robo-style {
    margin-right: 10px;
    margin-left: 10px;
  }

}

/* COUNTER ABOUT US  */

.container-counter {
  display: flex;
  flex-flow: wrap row;
  justify-content: center;
  /* margin-top:3rem; */
}

.counter {
  display: flex;
  align-items: center;
  align-content: center;
  justify-content: center;
  font-size: 70px;
  font-weight: bolder;
  color: #1a3760;
}

.bt-border {
  border-bottom: 2px solid #e6e4e4;
}

.bt-border:hover {
  border-bottom: 2px solid #FF6F14;
}

/* Knowledge Base CSS Start */

/* .HELP_first-container {
  box-shadow: 0 10px 100px 0 rgba(40, 47, 98, 0.08);
} */

.horizontal {
  width: 100%;
  height: 0px;
  border: 1px solid #00000066;
}

.HELP-links a {
  text-decoration: none !important;
  color: #000;
}

.HELP-create {
  border: solid 1px rgb(102, 102, 251);
}

.HELP-buy {
  border: solid 1px rgb(102, 102, 251);
}

.HELP-links a:hover {
  color: rgb(10, 88, 202);
}

.HELP-btn {
  border: solid 1px rgb(102, 102, 251);
}

.HELP-right-head {
  border-radius: 50%;
  background-color: #2571BE;
  width: 40px;
  height: 40px;
  color: #FFF;
}

.HELP-choose-file {
  font-size: 15px;
}

.model-h-w {
  min-width: 575px;
  height: 700px;
  margin-left: -45px;
}

.radio-w {
  margin-left: 88px;
}

.HELP-container-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  row-gap: 2rem;
  column-gap: 2rem;
}

@media (max-width: 991px) {
  .HELP-container-grid {
    grid-template-columns: repeat(1, 1fr);
  }
}

.create-ticket-btn {
  background-color: #3E5AFA;
  width: max-content;
}

@media only screen and (min-width: 351px) {
  .Help-top-btn .margin-left {
    margin-left: 1rem;
  }
}

@media only screen and (max-width: 767px) {
  .model-h-w {
    margin-left: -40px;
    min-width: 585px;
    height: 700px;
  }
}

@media only screen and (max-width: 575px) {
  .model-h-w {
    min-width: 200px;
    height: 900px;
    margin: 2px;
  }

  .f-col {
    flex-direction: column;
  }

}

@media only screen and (max-width: 350px) {
  .Help-top-btn {
    flex-direction: column;
    gap: 1rem;
  }

  .Help-top-btn button {
    margin: auto;
  }
}

.TYC-bg {
  background-color: #0f0f11;
  padding: 2rem;
  border-radius: 30px;
  display: flex;
  align-items: flex-start;
  justify-content: center;
}

@media only screen and (max-width: 360px) {
  .TYC-bg {
    padding: 0.8rem;
  }
}

/* Lnowledge Base CSS End */