/*------------------------------------------------- navbar css Starts --------------------------------------------------------- */
.sticky-navbar {
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 9999;
    background-color: #ffffff;
    transition: background-color 0.3s ease-in-out;
}

.navbar-brand {
    display: flex;
    align-items: center;
}

.navbar-brand img {
    max-height: 100%;
    margin-right: 10px;
}

#navbar {
    width: 100%;
    position: relative;
    display: flex;
    align-items: center;
    position: sticky;
    top: 0;
    background-color: white;
    z-index: 1;
    justify-content: space-between;
}

#logo img {
    height: 60px;
}

.nav-link {
    color: #000;
    font-size: 13px;
    font-weight: 600;
    letter-spacing: 1.1px;
}

.links {
    text-decoration: none;
    font-weight: 500;
    color: #343F52;
    font-size: 16px;
}

.links:hover {
    color: #168cd1;
    text-decoration: none;
}

.head-product {
    font-size: 1rem;
    font-weight: 700;
}

.theme-color-bg {
    background-color: #FFF;
}

.navbar-collapse {
    display: flex;
    justify-content: flex-end;
}

.schedule-btn {
    background: #236EB9;
    /* box-shadow: 0px 4px 4px 0px #1b51e745; */
    font-size: 19px;
    width: fit-content;
    color: #FFF;
}

.border-for-talktohuman{
    border: 1px solid #343F52;
    width: fit-content;
}
.border-for-talktohuman:hover{
    border: 1px solid #343F52;
}
@media only screen and (max-width: 991px) {
    .navbar-nav {
        align-items: flex-start !important;
        margin-bottom: 10px;
    }

    .nav-item {
        width: 100%;
        text-decoration: none;
        font-weight: 500;
        color: #525260;
        font-size: 16px;
        min-height: 55px;
    }

    .horzontal-line {
        border: 1px solid #999999;
    }
}

/*------------------------------------------------- FOOTER CSS STARTS --------------------------------------------------------- */

/*
body {
    background-color: #f3f6fd;
    font-size: 13px;
    font-weight: bold;
    color: rgb(189, 196, 203)
} */

ul {
    list-style-type: none;
    margin: 0;
    padding: 0
}

ul>li {
    padding: 4px;
    color: black;
}



hr {
    border-width: 3px
}

/* .card {
    padding: 2% 7%
} */

.social>i {
    padding: 1%;
    font-size: 15px
}

.social>i:hover {
    color: #286fb4;
    cursor: pointer
}

.policy>div {
    padding: 4px
}

.heading {
    color: black
}

.divider {
    border-top: 1px solid #e0e0e2
}

.logo {
    height: 80px;
}

.icon-height {
    height: 1.5vw;
}

.flex {
    display: flex;
    padding-top: 15px;
}

.padding-left {
    padding: 0px 0px 0px 11px;
}

.height {
    height: 30px;

}

.font-size20 {
    font-size: 20px;
}

.footer-inner-heading {
    font-size: 2rem;
    line-height: 2rem;
    color: #3E5AFA;
}

.other-Route {
    margin-bottom: 10px;
    cursor: pointer;
    color: #FFF;
}

.other-Route:hover {
    color: #FFF;
}

.footer-hr {
    color: #BBBBBB !important;
    border-top: 1px solid #BBBBBB;
    opacity: 1 !important;
    border-width: 1px !important;
    margin: 1rem !important;
}

.para-PPT {
    color: #F6F2FA;
    text-decoration: none !important;
    font-size: 18px;
}

.para-PPT:hover {
    text-decoration: none !important;
}

.head-PPT {
    color: #F6F2FA;
    text-decoration: none !important;
    font-size: 18px;
}

.head-PPT:hover {
    text-decoration: none !important;
}

/*------------------------------------------------- FOOTER CSS ENDS --------------------------------------------------------- */
/*------------------------------------------------- All Products CSS START --------------------------------------------------------- */

.global-header {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1040;
    background-color: white;
}

.home {
    gap: 1rem;
}

.first {
    gap: 1rem;
}

.second {
    gap: 1rem;
}

.hambur {
    display: none;
}

.second a {
    position: relative;
}

.home a {
    color: rgb(0, 0, 0);
    text-align: center;
    padding: 0px 5px;
}

.number-div {
    color: #000;
}

.c-dropdown {
    float: left;
    z-index: 1040;
    /* overflow: hidden; */
}

.c-dropdown .c-dropbtn {
    outline: none;
    color: rgb(69, 43, 43);
    background: inherit;
}

.home a:hover {
    color: black;
    text-decoration: none;
}

.c-dropdown:hover .c-dropbtn {
    color: #005ce2;
}

.topheading:hover {
    color: #005ce2 !important;
}

.c-dropdown-content {
    display: none;
    left: 0;
    box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.2);
    z-index: 1060;
    padding: 0 !important;
    margin: 0 !important;
}

.c-dropdown.active .c-dropdown-content {
    display: block;
}

.division {
    gap: 5%;
}

.support1 {
    background: #F0FFFF;
}

.support2 {
    gap: 2%;
}

.flex-res .column {
    width: 250px !important;
}

.side-column {
    width: 5%;
    background-color: #577399;
}

.topbar a:hover {
    background-image: linear-gradient(45deg, #eef3fb, transparent);
}


.column a:hover {
    background-image: linear-gradient(45deg, #eef3fb, transparent);
}

.icon-show:hover .icon {
    display: block !important;
}

.verti-line {
    width: 2px;
    height: 45px;
    background-color: #30303063;
}

.row:after {
    content: "";
    display: table;
    clear: both;
}

.dropdown-menu {
    padding-top: 0rem !important;
}

/* .dp-img-container {
    padding: 0rem !important;
} */

ul>li {
    padding: 0rem !important;
}

/* ul>li:hover {

} */

.div-img {
    height: 90px;
    width: 90px;
    background: #969696;
    box-shadow: 4px 4px 15px 0px rgba(0, 0, 0, 0.15);
}

/* .dp-img {
    height: 90px;
    width: 90px;
    position: relative;
    top: 40px;
    left: 127px;
    border-radius: 50%;
    background: #969696;
    box-shadow: 4px 4px 15px 0px rgba(0, 0, 0, 0.15);
}

.dp-img img {
    width: 90px;
    height: 90px;
    border-radius: 50%;
    position: absolute;
    top: 50%;
    right: -50%;
    transform: translate(-50%, -50%);
} */

/* .btn[aria-expanded="true"] {
    background-color: transparent !important;
} */

.dropdown-menu {
    border-radius: 20px !important;
    width: 350px !important;
}

.title-para {
    color: #797979;
    font-size: 18px;
}

.mid-gap {
    gap: 0.7rem !important;
}

.bg-div {
    border-radius: 15px 15px 0px 0px !important;
}

.hover-effect .d-flex:hover {
    color: #0d6efd;
}

.dropdown-item {
    box-shadow: none !important;
}


/* li button:hover {
    background-color:  !important;
} */

li button:hover .icon {
    display: block !important;
    color: #797979;
}

/* .details-div p {
    padding: 0rem !important;
    margin: 0rem !important;
} */

@media screen and (max-width: 1199px) {
    .column a {
        height: 135px !important;
    }
}

@media screen and (max-width: 991px) {
    .home img {
        float: right;
        display: block;
    }

    .hambur {
        display: block !important;
        position: absolute;
        top: 5px;
        right: 5px;
        background: transparent;
        border: none;
    }

    .home {
        justify-content: space-between;
    }

    .first,
    .second {
        display: none;
    }

    .showhead {
        display: flex;
        flex-direction: column;
    }

    .homedirection {
        flex-direction: column;
        align-items: baseline;
    }
}

@media screen and (max-width: 991px) {
    .home.responsive {
        position: relative;
    }

    .home.responsive img {
        position: absolute;
        right: 0;
        top: 0;
    }

    .home.responsive a {
        float: none;
        display: block;
        text-align: left;
    }

    .home.responsive .c-dropdown {
        float: none;
    }

    .home.responsive .c-dropdown-content {
        position: relative;
    }

    .home.responsive .c-dropdown .c-dropbtn {
        display: block;
        width: 100%;
        text-align: left;
    }
}

/* .dropdown:hover .dropdown-menu {
    display: block;
    width: auto;
    height: auto;
    transition: 0.3s ease;
    box-shadow: 0px 2px 0px 0px rgba(193, 91, 91, 0.2);
} */

.dropdown>.dropdown-toggle:active {
    pointer-events: none;
}

.dropdown-item:hover {
    background: rgb(228, 228, 228);
    box-shadow: inset 2px 1px 4px #b3b3b3;
    transition: 0.3s ease;
}

@media screen and (max-width: 991px) {
    .column {
        /* width: 100%; */
        height: auto;
    }

    .row .flex-res {
        gap: 0 !important;
    }

    .column a {
        height: 150px !important;
    }
}

.c-dropdown-content {
    animation: fade-in 0.3s;
}

@keyframes fade-in {
    from {
        opacity: 0;
    }

    to {
        opacity: 1;
    }
}

@media only screen and (max-width: 767px) {
    .flex-res {
        flex-direction: column;
    }

    .flex-res .column {
        width: 100% !important;
    }

    .row .flex-res {
        gap: 0 !important;
    }

    .c-dropdown-content {
        max-height: 400px;
        overflow-y: auto;
    }

    .support2 {
        flex-direction: column;
        row-gap: 1rem !important;
    }

    .column a {
        height: auto !important;
    }
}

.show {
    display: block !important;
}

.head-btn[aria-expanded="true"] {
    background-color: transparent !important;
}

/*------------------------------------------------- All Products CSS END --------------------------------------------------------- */
