@extends('components.rokdi.main')

@section('webpage')
    {{-- Head Section Start --}}
    <div class="container-fluid pt-lg-5 pb-lg-5 pt-5" style="background: #910B2B;">
        <div class="container-lg pt-5">
            <div class="d-flex align-items-center flex-lg-row flex-column">
                <div class="col-lg-6 ">
                    <h2 class="text-white">Choose a Plan That Is Right for Your Business
                    </h2>
                    <p class="text-white fs-5">Explore our diverse range of plans and effortlessly discover the one that
                        perfectly
                        suits your unique business needs.</p>
                    <div class="max-width-btn">
                        <a aria-label="link"
                            class="text-decoration-none color-btn text-black fw-bold rounded-pill border-0 d-flex align-items-center pt-2 pb-2 pe-4 ps-4 gap-2"
                            href="{{ route('rokdi.contact') }}">
                            Talk to a human
                            <img style="height: 35px;" src="{{ asset('assets/global/images/button.webp') }}" alt="">
                            </button>
                        </a>
                    </div>
                </div>
                <div class="col-lg-6 pt-4 pt-lg-0" data-aos="zoom-in" data-aos-duration="800">
                    <img class="img-fluid" src="{{ asset('assets/rokdi/images/pricing.png') }}" alt="">
                </div>
            </div>
        </div>
    </div>
    {{-- Head Section End --}}
    {{-- Pricing Section Start --}}

    <div class="container-fluid pb-5 pt-5">
        <div class="container-lg">
            <div class="ms-lg-5 me-lg-5">
                <h6 class="fw-bold text-center">
                    <img class="pe-1" src="{{ asset('assets/rokdi/images/Vector.png') }}">
                    Costing
                </h6>
                <h3 class="text-center text-dark-color">Pricing plans for every need</h3>
                <p class="text-center fs-5 mt-lg-4 text-dark-color">Our pricing plans are intended to be reasonably priced.
                    Adaptable and suited to your specific requirements.</p>
            </div>
            <div class="price-grid pt-5">
                <div class="price-div"></div>
                <div class="price-items-2">
                    <button class="most-popular-btn pt-2 pb-3 pe-4 ps-4 mb-3">
                        Most Popular
                    </button>
                    <h4 class="text-center fw-bolder">Custom (Monthly)</h4>
                    <p class="price-para">Flexible billing solution tailored to your specific business needs.</p>
                    <div class="text-center pb-2">
                        @if (auth()->check())
                            @if (auth()->user()->hidePrice == 0)
                                <a href="{{ route('cart.add', ['product_id' => 5]) }}"
                                    class="text-decoration-none pt-1 pb-1 pe-4 ps-4 price-items-2-btn mb-3">Add to cart</a>
                            @else
                                <button class=" pt-1 pb-1 pe-4 ps-4 price-items-2-btn mb-3" data-bs-toggle="modal"
                                    data-bs-target="#getquoteModal">Get quote</button>
                            @endif
                        @else
                            <a href="{{ route('login') }}"
                                class="text-decoration-none pt-1 pb-1 pe-4 ps-4 price-items-2-btn mb-3">Login to see
                                Price</a>
                        @endif
                    </div>
                    <ol class="price-listing">
                        <div class="d-flex align-items-baseline gap-2 mb-3"><i class="fa-solid fa-circle-check"></i>
                            <li class="price-list">Automated sales tax</li>
                        </div>
                        <div class="d-flex align-items-baseline gap-2 mb-3"><i class="fa-solid fa-circle-check"></i>
                            <li class="price-list">Advanced analytics</li>
                        </div>
                        <div class="d-flex align-items-baseline gap-2 mb-3"><i class="fa-solid fa-circle-check"></i>
                            <li class="price-list">CRM integration</li>
                        </div>
                        <div class="d-flex align-items-baseline gap-2 mb-3"><i class="fa-solid fa-circle-check"></i>
                            <li class="price-list">Accounting integration</li>
                        </div>
                        <div class="d-flex align-items-baseline gap-2 mb-3"><i class="fa-solid fa-circle-check"></i>
                            <li class="price-list">Multi-language support</li>
                        </div>
                    </ol>
                </div>
                <div class="price-items-3">
                    <h4 class="text-center fw-bolder">Premium (Annual)</h4>
                    <p class="price-para">Comprehensive billing software with added features and cost-saving benefits.</p>
                    <div class="text-center pb-2">
                        @if (auth()->check())
                            @if (auth()->user()->hidePrice == 0)
                                <a href="{{ route('cart.add', ['product_id' => 5]) }}"
                                    class="text-decoration-none pt-1 pb-1 pe-4 ps-4 price-items-3-btn mb-3">Add to cart</a>
                            @else
                                <button class=" pt-1 pb-1 pe-4 ps-4 price-items-3-btn mb-3" data-bs-toggle="modal"
                                    data-bs-target="#getquoteModal">Get quote</button>
                            @endif
                        @else
                            <a href="{{ route('login') }}"
                                class="text-decoration-none pt-1 pb-1 pe-4 ps-4 price-items-3-btn mb-3">Login to see
                                Price</a>
                        @endif
                    </div>


                    <ol class="price-listing">
                        <div class="d-flex align-items-baseline gap-2 mb-3"><i class="fa-solid fa-circle-check"></i>
                            <li class="price-list">Automated sales tax</li>
                        </div>
                        <div class="d-flex align-items-baseline gap-2 mb-3"><i class="fa-solid fa-circle-check"></i>
                            <li class="price-list">Advanced analytics</li>
                        </div>
                        <div class="d-flex align-items-baseline gap-2 mb-3"><i class="fa-solid fa-circle-check"></i>
                            <li class="price-list">CRM integration</li>
                        </div>
                        <div class="d-flex align-items-baseline gap-2 mb-3"><i class="fa-solid fa-circle-check"></i>
                            <li class="price-list">Accounting integration</li>
                        </div>
                        <div class="d-flex align-items-baseline gap-2 mb-3"><i class="fa-solid fa-circle-check"></i>
                            <li class="price-list">Multi-language support</li>
                        </div>
                    </ol>
                </div>
                <div class="price-div"></div>
            </div>
        </div>
    </div>

    {{-- FAQ SECTION Start --}}

    @include('components.rokdi.faq')

    {{-- FAQ SECTION End --}}

    {{-- CONTACT-LINK SECTION  --}}
    @include('components.rokdi.contact-link')
@endsection
