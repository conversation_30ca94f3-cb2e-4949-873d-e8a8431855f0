@extends('global.main')

@section('GlobalPages')
    <div class="container-fluid">
        <div class="container">
            <section class="pt-md-5 pb-lg-5 pb-md-5 pb-4 pb-lg-0">
                <div class="container-fluid">
                    <div class="container-lg mt-4 mt-lg-0 d-flex flex-column align-items-center">
                        <div class="pt-lg-5">
                            <h1 class="text-center font-weight-bold header-show-class pt-lg-0 pt-5">AFFILIATE
                            </h1>
                            <p class="para1 text-center pt-2">We look forward to hearing from you. Bring your questions in,
                                and we'll
                                be pleased to respond.</p>
                        </div>
                        <div class="contact-width mt-3">
                            <div class="container1 light-green-bg">
                                <form id="contactForm" action="{{ route('contactform.submit') }}"
                                    enctype="multipart/form-data" method="POST" onsubmit="conCountry()">
                                    @csrf
                                    <div class="">


                                        <label for="fname" class="font-fam-rubik required">Full Name</label>
                                        <input type="text" id="fname" name="name" style="color: #000000"
                                            class="input-form-style" autocomplete="off" placeholder="Enter Your Full Name"
                                            value="{{ old('name') }}">
                                        @error('name')
                                            <div class="text-danger">{{ $errors->first('name') }}</div>
                                        @enderror
                                    </div>
                                    <div class="">

                                        <label for="lname " class="font-fam-rubik">Email
                                            <sup class="text-danger " style="font-size: 15px;">*</sup>
                                        </label>
                                        <input type="text" id="lname" name="email" style="color: #000000"
                                            class="input-form-style" autocomplete="off" placeholder="Enter Your Email"
                                            value="{{ old('email') }}">
                                        @error('email')
                                            <div class="text-danger">{{ $errors->first('email') }}</div>
                                        @enderror
                                    </div>
                                    <input type="hidden" name="code" value="" id="code">
                                    <label for="lname" class="font-fam-rubik">Mobile
                                        <sup class="text-danger " style="font-size: 15px;">*</sup>
                                    </label>

                                    <div>
                                        <input autofocus type="text" class="number w-100 input-form-style"
                                            style="color: #000000" name="mobile" autocomplete="off" id="number"
                                            step="" value="{{ old('mobile') }}">
                                        @error('mobile')
                                            <div class="text-danger">{{ $errors->first('mobile') }}</div>
                                        @enderror
                                    </div>

                                    <div class="">

                                        <label for="country" class="pt-3 outline-none" class="font-fam-rubik">How many
                                            people do
                                            you plan to interact with in the coming year?</label>
                                        <select id="" name="noOfUsers" class="input-form-style">
                                            <option class="font-fam-rubik" value="< 1K">
                                                < 1K </option>
                                            <option class="font-fam-rubik" value=" 1K - 10K"> 1K - 10K</option>
                                            <option class="font-fam-rubik" value=" 10K - 50K "> 10K - 50K </option>
                                            <option class="font-fam-rubik" value=" 50K - 100K"> 50K - 100K</option>
                                            <option class="font-fam-rubik" value=" 100K - 1M">100K - 1M</option>
                                            <option class="font-fam-rubik" value="  1M - 10M"> 1M - 10M</option>
                                            <option class="font-fam-rubik" value="  >10M"> >10M</option>
                                        </select>
                                        @error('noOfUsers')
                                            <div class="text-danger">{{ $errors->first('noOfUsers') }}</div>
                                        @enderror
                                    </div>
                                    <label for="subject" class="pt-4" class="font-fam-rubik input-form-style"
                                        style="color: #000000">What are the
                                        topics you would like to cover on the call with our sales team?
                                        <sup class="text-danger" style="font-size: 15px;">*</sup>
                                    </label>
                                    <textarea id="subject" name="message" placeholder="Message" class="input-form-style" required>{{ old('message') }}</textarea>
                                    @error('message')
                                        <div class="text-danger">{{ $errors->first('message') }}</div>
                                    @enderror
                                    <input type="hidden" name="token" id="token">
                                    <button
                                        class="bg-black border-0 pb-2 pt-2 pe-4 ps-4 mt-4 rounded-pill text-white d-flex align-items-center gap-2 button-shadow g-recaptcha"
                                        data-sitekey="{{ env('RECAPTCHA_SITE_KEY') }}" data-callback='onSubmit'>
                                        <div class="d-flex f-s-18 center-items">Pass us your details!</div>
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </div>
    </div>
@endsection
