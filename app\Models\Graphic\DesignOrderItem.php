<?php

namespace App\Models\Graphic;

use App\Models\Graphic\DesignOrder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class DesignOrderItem extends Model
{
    use HasFactory;
    protected $table = 'graphic_design_order_item';
    public $timestamps = false;

    protected $fillable = [
        'graphic_design_order_id',
        'graphic_design_id',
        'graphic_lead_brand_id'
    ];
    //order
    public function order()
    {
        return $this->belongsTo(DesignOrder::class, 'graphic_design_order_id', 'id');
    }

    public function design()
    {
        return $this->belongsTo(Design::class, 'graphic_design_id', 'id')->with('category');
    }
    public function annotations()
    {
        return $this->hasMany(Annotation::class, 'graphic_design_order_item_id', 'id')->whereNotNull('annotationId');
    }
    public function comments()
    {
        return $this->hasMany(Annotation::class, 'graphic_design_order_item_id', 'id')->whereNull('annotationId');
    }
}
