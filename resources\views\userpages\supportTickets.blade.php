@extends('userpages.main')

@section('title', 'Support Tickets')

@section('userpagesection')
    <div class="ms-lg-2 m-0">
        <div class="mb-3">

            <div class="bg-white pt-2 pb-2 pe-3 ps-3 d-flex justify-content-between header-btn-flex"
                style="border-radius: 7px;">
                <h4 class="heading-font">Support Ticket</h4>
                <div style="width: fit-content;">
                    <a href="{{ route('supportCreate') }}">
                        <button
                            class="btn-bg-blue text-white rounded-pill border-0 ps-3 pe-3 pt-1 pb-1 d-flex align-items-center gap-1">
                            <i class="fa-solid fa-plus"></i>
                            Create Ticket
                        </button>
                    </a>

                </div>
            </div>
        </div>

        <div class="overflow-auto change-scroll bg-white" style="border-radius: 7px; height: 61vh;">
            @if ($data->count() != 0)
                <div class="table-responsive" style="max-width: 100vw;">
                    <table class="table text-center table-striped">
                        <thead style="--bs-table-bg: transparent;">
                            <tr class="text-nowrap">

                                <th scope="col">Ticket ID</th>
                                <th scope="col">Subject</th>
                                <th scope="col">Services</th>
                                <th scope="col">Created On</th>
                                <th scope="col">Status</th>
                                <th scope="col">Updated On</th>
                                <th scope="col">Action</th>
                                <th scope="col"></th>
                            </tr>
                        </thead>
                        <tbody>

                            @foreach ($data as $ticket)
                                <tr>

                                    <td class="gray-dark-text">{{ $ticket->id }}</td>
                                    <td class="gray-dark-text text-break">{{ Str::limit($ticket->subject, 30) }}</td>
                                    <td class="gray-dark-text">{{ Str::ucfirst($ticket['product']->name) }}</td>
                                    <td class="gray-dark-text text-nowrap">{{ humanDate($ticket->datetime) }}</td>
                                    <td>
                                        @if ($ticket->isDone == 0)
                                            <div class="rounded-pill border-0 px-2 pt-1 pb-1 text-nowrap "
                                                style="background-color: #EAF1FF; color: #194DAB;">
                                                <i class="fa-solid fa-box-open"></i>
                                                Open
                                            </div>
                                        @else
                                            <div class="rounded-pill border-0 px-2 pt-1 pb-1 text-nowrap"
                                                style="background-color: #FFDEDE; color: #D72500;">
                                                <i class="fa-solid fa-trash"></i>
                                                Closed
                                            </div>
                                        @endif
                                    </td>
                                    <td class="gray-dark-text text-nowrap">
                                        {{ humanDate($ticket->updateDatetime) }}
                                    </td>
                                    <td class=" ">
                                        <div class="">
                                            <form action="{{ route('supportStatus', ['id' => $ticket->id]) }}"
                                                method="post">
                                                @csrf
                                                <input type="hidden" name="status"
                                                    value="{{ $ticket->isDone == 1 ? 0 : 1 }}">
                                                @if ($ticket->isDone == 0)
                                                    <button type="submit"
                                                        class="btn btn-sm btn-danger rounded-5 text-nowrap"><i
                                                            class="fa-solid fa-trash me-2"></i>Mark Closed</button>
                                                @else
                                                    <button type="submit"
                                                        class="btn btn-sm rounded-5 text-white text-nowrap"
                                                        style="background-color: #194DAB"><i
                                                            class="fa-solid fa-box-open pe-2"></i>Reopen</button>
                                                @endif
                                            </form>
                                        </div>
                                    </td>
                                    <td>
                                        @if ($ticket->isDone == 0)
                                            <a href="{{ route('supportChat', ['id' => $ticket->id]) }}">
                                                <i class="bi bi-chevron-right fs-4 fw-bolder text-dark"></i>
                                            </a>
                                        @endif
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>

                    </table>
                </div>
            @else
                <div class="d-flex align-items-center flex-column bg-white justify-content-center ">
                    <img src="{{ asset('assets/userpages/images/billing.png') }}" alt="blank_page_img" class="img-fluid">
                    <a class="bg-transparent fs-4 fw-medium mb-4 border border-0 border-bottom text"style="color: #194dab;"
                        href="{{ route('supportCreate') }}">
                        Create Support Ticket
                    </a>
                </div>
            @endif
        </div>

        <x-showPagination :data=$data />

    </div>

@endsection
@section('PAGE-script')
    <!-- Modal -->
    <div class="modal fade" id="staticBackdrop" data-bs-keyboard="false" tabindex="-1"
        aria-labelledby="staticBackdropLabel" aria-hidden="true">
        <div class="me-0 modal-dialog modal-dialog-centered my-0">
            <div class="modal-content min-vh-100 rounded-0">

                <div class="modal-body">
                    <div class="card border-0 p-0">
                        <div class="card-header d-flex bg-white border-0 justify-content-between">
                            <div class="card-title">
                                <button class=" btn-light-danger btn btn-icon h-30px w-30px" data-bs-dismiss="modal"
                                    aria-label="Close">
                                    <i class="bi bi-x-lg"></i>
                                </button>
                            </div>

                            <div class="card-toolbar">
                                <div class="badge rounded-pill" id="badge"></div>

                            </div>
                        </div>
                        <div class="card-body p-2">
                            <div class="row">
                                <div class="fs-6 fw-bold col-sm-12 col-md-4 col-lg-4 d-flex justify-content-between">

                                    <span>Subject </span>
                                    <span class="d-none d-lg-block d-md-block">:</span>
                                </div>
                                <div id="title" class="text-break fs-6 mb-4  col-sm-12 col-md-8 col-lg-8">

                                </div>
                            </div>
                            <div class="row">
                                <div class="fs-6 fw-bold col-sm-12 col-md-4 col-lg-4 d-flex justify-content-between">

                                    <span>Product </span>
                                    <span class="d-none d-lg-block d-md-block">:</span>
                                </div>
                                <div id="prod" class="text-break fs-6 mb-4  col-sm-12 col-md-8 col-lg-8">

                                </div>
                            </div>
                            <div class="row">
                                <div class="fs-6 fw-bold col-sm-12 col-md-4 col-lg-4 d-flex justify-content-between">

                                    <span>Description</span>
                                    <span class="d-none d-lg-block d-md-block">:</span>
                                </div>
                                <div id="desc" class="text-break fs-6 mb-4  col-sm-12 col-md-8 col-lg-8">

                                </div>
                            </div>
                            <div class="d-flex justify-content-center">

                                <div class="border rounded-3  p-2 d-none w-lg-50 w-sm-100 bg-secondary-subtle "
                                    id="attachDiv">
                                    <a aria-label="link" href="" download id="downloadBtn"
                                        class="text-decoration-none text-dark-emphasis">
                                        <div class="d-flex justify-content-around gap-2">
                                            <div class="">
                                                Download Attachment
                                            </div>
                                            <div>
                                                <svg width="20px" height="20px" viewBox="0 0 24 24" fill="none"
                                                    xmlns="http://www.w3.org/2000/svg">
                                                    <path
                                                        d="M17 17H17.01M17.4 14H18C18.9319 14 19.3978 14 19.7654 14.1522C20.2554 14.3552 20.6448 14.7446 20.8478 15.2346C21 15.6022 21 16.0681 21 17C21 17.9319 21 18.3978 20.8478 18.7654C20.6448 19.2554 20.2554 19.6448 19.7654 19.8478C19.3978 20 18.9319 20 18 20H6C5.06812 20 4.60218 20 4.23463 19.8478C3.74458 19.6448 3.35523 19.2554 3.15224 18.7654C3 18.3978 3 17.9319 3 17C3 16.0681 3 15.6022 3.15224 15.2346C3.35523 14.7446 3.74458 14.3552 4.23463 14.1522C4.60218 14 5.06812 14 6 14H6.6M12 15V4M12 15L9 12M12 15L15 12"
                                                        stroke="#000000" stroke-width="2" stroke-linecap="round"
                                                        stroke-linejoin="round" />
                                                </svg>
                                            </div>
                                        </div>
                                    </a>
                                </div>
                            </div>
                        </div>

                    </div>

                </div>

            </div>
        </div>
    </div>


    <script>
        function showModal(id) {
            // alert(id);
            $.ajax({
                type: "get",
                url: "/show/ajax/ticket",
                data: {
                    'id': id
                },
                dataType: "json",
                success: function(response) {
                    console.log(response);
                    $('#title').text(response.data.subject);
                    $('#desc').text(response.data.purpose);
                    $('#prod').text(response.prod);
                    if (response.data.isDone == 1) {
                        $('#badge').css({
                            "backgroundColor": "#57AF9F1a",
                            "color": "#57AF9F"
                        }).text('Complete');

                    } else {
                        $('#badge').css({
                            "backgroundColor": "#ff76401a",
                            "color": "#FF7640"
                        }).text('Pending');

                    }

                    if (response.hasAttach == true) {

                        $('#downloadBtn').attr('href', response.attachment);
                        $('#attachDiv').removeClass('d-none');
                    } else {
                        $('#attachDiv').addClass('d-none');

                    }
                }
            });
        }
    </script>
@endsection
