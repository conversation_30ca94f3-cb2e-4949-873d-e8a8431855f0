<?php $__env->startSection('title','Billing Profile'); ?>
<?php $__env->startSection('userpagesection'); ?>

<div class="ms-lg-2 m-0">
    <div class="">
        <div class="bg-white pt-2 pb-2 pe-3 ps-3 mb-3 d-flex align-items-center justify-content-between" style="border-radius: 7px;">
            <h4 class="heading-font">Billing Profile</h4>
            <div style="width: fit-content;">
                
                <button class="text-white rounded-pill border-0 px-3 pt-1 pb-1 d-flex align-items-center gap-2 btn-bg-blue" data-bs-toggle="modal" data-bs-target="#addAddress">
                    <i class="fa-solid fa-plus"></i>
                    Create Billing Profile
                </button>
                
            </div>
        </div>

        <?php if($addresses->count() != 0): ?>
        <div class="overflow-auto change-scroll" style="border-radius: 7px; height: 64vh;">
            <div class="billing-grid">
                <?php $__currentLoopData = $addresses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $address): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="billing-grid-items p-3 overflow-hidden" style="min-height: 220px">
                    <?php if($address->isPrimary == 1): ?>
                    <div class="ribbon">Default</div>
                    <?php endif; ?>
                    <div class="d-flex align-items-center justify-content-between h-100 flex-column <?php if($address->address == null): ?>  <?php endif; ?>">
                        <div class="align-self-baseline">
                            <h4 style="font-size: 20px !important; font-weight:500 !important;">
                                <?php echo e($address->name ?? '-'); ?>

                            </h4>
                            <div class="d-flex align-items-center gap-2">
                                <h4 class="mb-0" style="font-size: 20px !important; font-weight:500 !important;">
                                    <?php echo e($address->company ?? '-'); ?>

                                </h4>
                                <span class="gray-dark-text"><?php echo e($address->gst ?? '-'); ?></span>
                            </div>
                            <p class="gray-dark-text mb-0">
                                
                                <?php echo e($address->address ? $address->address . ', ' : ''); ?>

                                <br>
                                <?php echo e($address->city ? $address->city . ', ' : ''); ?>

                                <?php echo e($address->state ? $address->state . ', ' : ''); ?>

                                <?php echo e($address->country ? $address->country : ''); ?>

                                <?php echo e($address->pincode ? ' (' . $address->pincode . ') ' : ''); ?>


                            </p>
                        </div>
                        <div class="d-flex align-items-center align-self-baseline gap-2">
                            <button type="button" class="btn btn-link text-decoration-none p-0 a billingProfileEditBtn" style="color: #194DAB;" value="<?php echo e($address->id); ?>">
                                Edit
                            </button>
                            <div class="border-verti"></div>

                            <form action="<?php echo e(route('billingAddress.destroy', ['billingAddress' => $address->id])); ?>" method="POST">
                                <?php echo csrf_field(); ?>
                                <?php echo method_field('DELETE'); ?>
                                <button type="submit" class="btn btn-link text-decoration-none px-0" style="color: #194DAB;">Delete</button>
                            </form>
                            <?php if($address->isPrimary != 1): ?>
                            <div class="border-verti"></div>
                            <form action="<?php echo e(route('billingAddress.setasDefault')); ?>" method="post">
                                <?php echo csrf_field(); ?>
                                <input type="hidden" name="id" value="<?php echo e($address->id); ?>" />
                                <input type="hidden" name="event" value="setAsDefault" />
                                <button type="submit" class="btn btn-link text-decoration-none px-0" style="color: #194DAB;">Set as
                                    default</button>
                            </form>
                            <?php endif; ?>

                        </div>
                    </div>
                </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        </div>
        <?php else: ?>
        <div class="d-flex align-items-center flex-column bg-white justify-content-center">
            <img src="<?php echo e(asset('assets/userpages/images/billing.png')); ?>" alt="blank_page_img" class="img-fluid">
            <button class="bg-transparent fs-4 fw-medium mb-4 border border-0 border-bottom text" style="color: #194dab;" data-bs-toggle="modal" data-bs-target="#addAddress">
                Create Billing Profile
            </button>
        </div>
        <?php endif; ?>
    </div>


    <?php if (isset($component)) { $__componentOriginalbf337c76d1c477654f5ce60a66e22809 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalbf337c76d1c477654f5ce60a66e22809 = $attributes; } ?>
<?php $component = App\View\Components\ShowPagination::resolve(['data' => $addresses] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('showPagination'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(App\View\Components\ShowPagination::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalbf337c76d1c477654f5ce60a66e22809)): ?>
<?php $attributes = $__attributesOriginalbf337c76d1c477654f5ce60a66e22809; ?>
<?php unset($__attributesOriginalbf337c76d1c477654f5ce60a66e22809); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalbf337c76d1c477654f5ce60a66e22809)): ?>
<?php $component = $__componentOriginalbf337c76d1c477654f5ce60a66e22809; ?>
<?php unset($__componentOriginalbf337c76d1c477654f5ce60a66e22809); ?>
<?php endif; ?>

</div>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('PAGE-script'); ?>
<?php echo $__env->make('userpages.billingAddress.addModal', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

<?php echo $__env->make('userpages.billingAddress.EditModal', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

<?php if(session('isEditModalShow') == true): ?>
<script>
    $(document).ready(function() {
        new bootstrap.Modal($('#billingProfileEdit')).show()
    });
</script>
<?php endif; ?>
<?php if(session('isAddModalShow') == true): ?>
<script>
    $(document).ready(function() {
        new bootstrap.Modal($('#addAddress')).show()
    });
</script>
<?php endif; ?>



<?php $__env->stopSection(); ?>

<?php echo $__env->make('userpages.main', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\live\websites_laravel\resources\views/userpages/address.blade.php ENDPATH**/ ?>