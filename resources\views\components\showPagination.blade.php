@if ($data->lastPage() > 1)
    @php
        $currentPage = $data->currentPage();
        $lastPage = $data->lastPage();
        $navButtonClass = "m-0 p-0 rounded-circle bg-white d-flex align-items-center justify-content-center";
        $navStyle = "width:33px; height: 33px;";
        $linkClass = "text-decoration-none pb-0";
    @endphp
    <div class="m-3">
        <div class="mt-2 mb-2">
            <div class="rounded-pill d-flex align-items-center justify-content-center" style="height: 33px;">
                {{-- First page button --}}
                @if ($currentPage != 1)
                    <div class="{{ $navButtonClass }}" style="{{ $navStyle }}">
                        <a href="{{ $data->url(1) }}" class="{{ $linkClass }} text-dark d-flex">
                            <i class="fa-solid fa-angle-left d-flex align-items-center"></i>
                            <i class="fa-solid fa-angle-left d-flex align-items-center"></i>
                        </a>
                    </div>
                @endif

                {{-- Previous page button --}}
                <div class="{{ $navButtonClass }} me-2" style="{{ $navStyle }}">
                    <a href="{{ $currentPage == 1 ? '#' : $data->url($currentPage - 1) }}"
                       class="{{ $linkClass }} {{ $currentPage == 1 ? 'text-secondary' : 'text-dark' }}">
                        <i class="fa-solid fa-angle-left d-flex align-items-center"></i>
                    </a>
                </div>

                {{-- Page numbers --}}
                <ul class="bg-white d-flex align-items-center justify-content-center gap-3 rounded-pill px-3 py-1">
                    @php
                        $start = $data->total() <= 6 ? 1 : max(1, $currentPage - 2);
                        $end = $data->total() <= 6 ? $lastPage : min($lastPage, $currentPage + 2);
                    @endphp
                    @for ($i = $start; $i <= $end; $i++)
                        <li class="m-0 p-0 rounded" @if ($currentPage == $i) style="background-color: #194DAB;" @endif>
                            <a href="{{ $data->url($i) }}"
                                class="pb-0 px-2 text-decoration-none {{ $currentPage == $i ? 'text-white' : 'text-dark' }}"
                                @disabled($currentPage == $i)>
                                {{ $i }}
                            </a>
                        </li>
                    @endfor
                </ul>

                {{-- Next page button --}}
                <div class="{{ $navButtonClass }} ms-2" style="{{ $navStyle }}">
                    <a href="{{ $currentPage == $lastPage ? '#' : $data->url($currentPage + 1) }}"
                       class="{{ $linkClass }} {{ $currentPage == $lastPage ? 'text-secondary' : 'text-dark' }}">
                        <i class="fa-solid fa-angle-right d-flex align-items-center"></i>
                    </a>
                </div>

                {{-- Last page button --}}
                @if ($currentPage != $lastPage)
                    <div class="{{ $navButtonClass }} me-3" style="{{ $navStyle }}">
                        <a href="{{ $data->url($lastPage) }}" class="{{ $linkClass }} text-dark d-flex">
                            <i class="fa-solid fa-angle-right d-flex align-items-center"></i>
                            <i class="fa-solid fa-angle-right d-flex align-items-center"></i>
                        </a>
                    </div>
                @endif
            </div>
        </div>
    </div>
@endif
