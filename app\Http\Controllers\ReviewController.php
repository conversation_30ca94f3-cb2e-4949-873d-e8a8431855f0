<?php

namespace App\Http\Controllers;

use App\Models\FeedbackByLead;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class ReviewController extends Controller
{
    public function store(Request $request, $lead)
    {
        $validator = Validator::make(
            $request->all(),
            [
                'rating' => 'required|numeric',
                'review' => 'nullable|max:250',
            ],
            [],
            [
                'rating' => 'Rating',
                'review' => 'Review'
            ]
        );
        if ($validator->fails()) {
            return redirect()->back()->withErrors($validator);
        }


        try {

            $record = new FeedbackByLead();

            $record->comment = $request->review;
            $record->rating = $request->rating;
            $record->lead_id = $lead;
            $record->save();
            return redirect('/')->with('success', 'Thank you for your feedback. For assistance, please submit a support ticket or contact us online.');
        } catch (Exception $e) {
            return redirect()->back()->with('error', ErrMsg());

        }


    }
}
