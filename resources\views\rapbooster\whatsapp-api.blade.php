@extends('components.rapbooster.main')
@section('rapbooster-CSS')
    @include('components.global.accordionStyles')
@endsection
@section('webpage')
    @php
        $category = ['id' => 1, 'name' => str_replace(' ', '-', 'WhatsApp APIs')];
    @endphp
    {{-- Head-section Start --}}
    <div class="container-fluid pt-lg-5 pb-lg-5 pt-md-5 pb-md-5 pt-4 pb-4 overflow-hidden"
        style="background: linear-gradient(180deg, #2D2DB0 0%, #2C8CF4 100%);">
        <div class="container-lg">
            <div class="row d-flex align-items-center rounded-5">
                <div class="col-lg-6 col-md-6 pb-md-4 p-0">
                    <h6 class="page-category" style="color: #FCD250;font-weight: 600;">WhatsApp API
                    </h6>
                    <h2 class="h2-custom text-white fw-bold pe-lg-5">Integrate to automate your <span
                            style="color: #FCD250;">customer
                            experience</span></h2>
                    <p class="text-white f-s-5 pe-lg-5">Engage with active customers where they are most present. Place
                        Click-to-WhatsApp ads on Facebook and Instagram to initiate conversations and build connections.
                    </p>
                    <div class="d-flex align-items-lg-center gap-lg-3 gap-2">
                        <div class="max-width-btn">
                            @include('components.rapbooster.talkToHumanYellow-Btn')
                        </div>
                        @include('components.rapbooster.getstarted-yellowBtn')

                    </div>
                </div>
                <div class="col-lg-6 col-md-6 text-center DP-img pt-3 pt-lg-0" data-aos="fade-left">
                    <img class="img-fluid" src="{{ asset('assets/rapbooster/images/wa-api1.png') }}" alt="">
                </div>
            </div>
        </div>
    </div>
    {{-- Head-section End --}}
    {{-- First-Section Start --}}
    <div class="container-fluid pt-lg-5 pb-lg-5 pt-md-5 pb-md-5 pt-4 pb-4">
        <div class="container-lg">
            <h3 class="text-center fw-bold h3-custom"><span style="color: #2c8cf4;">Be where your Customer needs</span></h3>
            <p class="text-center f-s-5 mb-0 mb-lg-2 mb-md-2">Tap into a vast user base of 2 Billion across 180 Countries
                with WhatsApp Business.
                Foster stronger connections, better communications and expand your business reach securely. Unlock new
                possibilities for your business growth!</p>
        </div>
    </div>
    {{-- First-Section End --}}
    {{-- Second-Section Start --}}
    <div class="container-fluid pt-lg-5 pb-lg-5 pt-md-5 pb-md-5 pt-4 pb-3 overflow-hidden" style="background: #fcf6e9;">
        <div class="container-lg">
            <h3 class="h3-custom text-center fw-bold">Ways to use <span style="color: #2c8cf4;">WhatsApp Business
                    Account</span>
                Effectively in
                Your Industry</h3>
            <div class="row d-flex align-items-center pt-4">
                <div class="col-lg-6 text-center" data-aos="fade-right">
                    <img class="img-fluid" src="{{ asset('assets/rapbooster/images/wa-api2.png') }}" alt="">
                </div>
                <div class="col-lg-6 pb-md-4 grid-WAPI-container pt-4 pt-lg-0">
                    {{-- <div class=""> --}}
                    <div class="WAPI-container">
                        <h4 class="h4-custom fw-bold">Ecommerce</h4>
                        <p class="f-s-5">Using WhatsApp APIs, you can enhance your customers' shopping experience and
                            increase
                            conversions by automating abandoned cart reminders, on-stock notifications, order updates,
                            and price drop alerts.</p>
                    </div>
                    <div class="WAPI-container">
                        <h4 class="h4-custom fw-bold">Automotive</h4>
                        <p class="f-s-5">Take control of client marketing and support by utilizing the WhatsApp Business
                            Platform to
                            deliver promotions and automated service dates, as well as reminders for insurance and
                            warranty renewals.</p>
                    </div>
                    <div class="WAPI-container">
                        <h4 class="h4-custom fw-bold">Retail</h4>
                        <p class="f-s-5">Attract new consumers and keep existing ones by giving them appealing discounts,
                            urging them
                            to join membership programmes, and even notifying them when their favourite brand releases
                            new items.</p>
                    </div>
                    <div class="WAPI-container">
                        <h4 class="h4-custom fw-bold">Healthcare</h4>
                        <p class="f-s-5">Make people feel as though their health and well-being is your top focus by
                            sending automated
                            WhatsApp confirmations and reminders for doctor visits, medical testing, prescription
                            refills, and more.</p>
                    </div>
                    <div class="WAPI-container">
                        <h4 class="h4-custom fw-bold">Finance</h4>
                        <p class="f-s-5">Put an end to lengthy wait periods and late paper communications by sending out
                            bank
                            statements, account updates, credit card approvals, payment receipts, and other important
                            documents on time.</p>
                    </div>
                    <div class="WAPI-container">
                        <h4 class="h4-custom fw-bold">Travel & Tourism</h4>
                        <p class="f-s-5">Serve as the gateway to fulfill your customers' travel needs, whether it's
                            purchasing
                            tickets, making itinerary changes, or providing location-based services on the road.</p>
                    </div>
                    {{-- </div> --}}
                </div>
            </div>
        </div>
    </div>
    {{-- Second-Section End --}}
    {{-- Third-Section Start --}}
    <div class="container-fluid pt-lg-5 pb-lg-5 pt-md-5 pb-md-5 pt-4 pb-4" style="background: #f4f4fe;">
        <div class="container-lg">
            <h3 class="h3-custom text-center fw-bold">Why <span style="color: #2c8cf4;">Choose Us</span> for WhatsApp
                Business API?
            </h3>
            <div class="WAPI-grid-container pt-4">
                <div class="WAPI-grid-item">
                    <i class="fa-solid fa-check-double" style="color: #6320b9"></i>
                    <p class="f-s-5">Create a professional business profile in order to interact with consumers.</p>
                </div>
                <div class="WAPI-grid-item">
                    <i class="fa-solid fa-check-double" style="color: #6320b9"></i>
                    <p class="f-s-5">End-to-end encryption ensures secure communication (HTTPS connection).</p>
                </div>
                <div class="WAPI-grid-item">
                    <i class="fa-solid fa-check-double" style="color: #6320b9"></i>
                    <p class="f-s-5">Communicate effectively in clients' preferred language.</p>
                </div>
                <div class="WAPI-grid-item">
                    <i class="fa-solid fa-check-double" style="color: #6320b9"></i>
                    <p class="f-s-5">API integration with your CRM to provide clients real-time updates.</p>
                </div>
                <div class="WAPI-grid-item">
                    <i class="fa-solid fa-check-double" style="color: #6320b9"></i>
                    <p class="f-s-5">Enhance engagement with interactive responses through integration.</p>
                </div>
                <div class="WAPI-grid-item">
                    <i class="fa-solid fa-check-double" style="color: #6320b9"></i>
                    <p class="f-s-5">Efficiently manage operations with an intuitive Admin Portal.</p>
                </div>
                <div class="WAPI-grid-item">
                    <i class="fa-solid fa-check-double" style="color: #6320b9"></i>
                    <p class="f-s-5">Seamless Transition from Bot to Customer Support Executive.</p>
                </div>
                <div class="WAPI-grid-item">
                    <i class="fa-solid fa-check-double" style="color: #6320b9"></i>
                    <p class="f-s-5">Enhance Conversations with Detailed Performance Reports.</p>
                </div>
                <div class="WAPI-grid-item">
                    <i class="fa-solid fa-check-double" style="color: #6320b9"></i>
                    <p class="f-s-5">Personalize Greetings and Set Away Messages Conveniently.</p>
                </div>
            </div>
        </div>
    </div>
    {{-- Third-Section End --}}
    {{-- Forth-Section Start --}}
    <div class="container-fluid overflow-hidden pt-lg-5 pb-lg-5 pt-md-5 pb-md-5 pt-4 pb-4">
        <div class="container-lg">
            <div class="row d-flex align-items-center pb-lg-4">
                <div class="col-lg-6 col-md-6 pe-lg-5 pb-md-4">
                    <div class="">
                        <h3 class="h3-custom fw-bold">Boost sales with <span style="color: #2c8cf4;">CTA
                                Notifications</span> that
                            triple conversion rates</h3>
                        <p class="f-s-5">Experience the power of Smart CTA-based Notifications, where every message
                            becomes an opportunity
                            for instant conversions. With clickable links and quick reply buttons, WhatsApp CTA
                            Notifications drive remarkable click rates of 45-60%, igniting engagement and skyrocketing
                            sales. Embrace this game-changing tool to boost your business and witness the boom in customer
                            interaction and conversion rates.
                        </p>
                    </div>
                    @include('components.rapbooster.getStarted-dpBtn')


                </div>
                <div class="col-lg-6 col-md-6 text-center" data-aos="fade-left">
                    <img class="img-fluid" src="{{ asset('assets/rapbooster/images/wa-api3.png') }}" alt="">
                </div>
            </div>
        </div>
    </div>
    {{-- Forth-Section End --}}
    {{-- Fifth-Section Start --}}
    <div class="container-fluid pt-lg-5 pb-lg-5 pt-md-5 pb-md-5 pt-4 pb-4 overflow-hidden"
        style="background: linear-gradient(180deg, #2D2DB0 0%, #2C8CF4 100%);">
        <div class="container-lg">
            <div class="row d-flex align-items-center">
                <div class="col-lg-6 col-md-6 text-center" data-aos="fade-right">
                    <img class="img-fluid" src="{{ asset('assets/rapbooster/images/wa-api4.png') }}" alt="">
                </div>
                <div class="col-lg-6 col-md-6 pb-md-4 pt-3 pt-lg-0 pt-md-0">
                    <h3 class="h3-custom fw-bold text-white">Unlocking <span style="color: #FCD250;">Benefits</span> for
                        your
                        business</h3>
                    <div class="pb-0 pb-lg-4 pb-md-4">
                        <div class="d-flex align-items-baseline gap-3"><i class="fa-solid fa-circle-check fa-xl"
                                style="color: #FCD250;"></i>
                            <p class="WAPI-text mb-3">Seamlessly connect with customers globally, ensuring constant
                                accessibility and communication.</p>
                        </div>
                        <div class="d-flex align-items-baseline gap-3"><i class="fa-solid fa-circle-check fa-xl"
                                style="color: #FCD250;"></i>
                            <p class="WAPI-text mb-3">Streamlined onboarding and rapid integration of your business
                                processes
                                for swift implementation.</p>
                        </div>
                        <div class="d-flex align-items-baseline gap-3"><i class="fa-solid fa-circle-check fa-xl"
                                style="color: #FCD250;"></i>
                            <p class="WAPI-text mb-3">Craft a personalized and immersive customer experience with limitless
                                possibilities.</p>
                        </div>
                        <div class="d-flex align-items-baseline gap-3"><i class="fa-solid fa-circle-check fa-xl"
                                style="color: #FCD250;"></i>
                            <p class="WAPI-text mb-3">Enhance client engagement through quick message settings for
                                greetings
                                and
                                availability.</p>
                        </div>
                        <div class="d-flex align-items-baseline gap-3"><i class="fa-solid fa-circle-check fa-xl"
                                style="color: #FCD250;"></i>
                            <p class="WAPI-text mb-3">Integrate your business with our bot, freeing your agents from
                                repetitive
                                customer inquiries.</p>
                        </div>
                        <div class="d-flex align-items-baseline gap-3"><i class="fa-solid fa-circle-check fa-xl"
                                style="color: #FCD250;"></i>
                            <p class="WAPI-text mb-3">Present an appealing business portfolio to boost lead generation and
                                foster customer engagement.</p>
                        </div>
                    </div>
                    @include('components.rapbooster.getstarted-yellowBtn')

                </div>
            </div>
        </div>
    </div>
    {{-- Fifth-Section End --}}
    {{-- Features-Section Start --}}
    <div class="container-fluid pt-lg-5 pb-lg-5 pt-md-5 pb-md-5 pt-4 pb-4" style="background: #FCF6E9;">
        <div class="container-lg">
            <h3 class="text-center fw-bold h3-custom">Unlock the power of <span style="color: #2c8cf4;">WhatsApp Business
                    Solutions</span> for
                unmatched Results</h3>
            <div class="grid-container pt-4">
                <div class="d-flex FS-container">
                    <i class="fa-solid fa-images fa-2xl" style="color: #E99F00"></i>
                    <div class="text-content">
                        <h4 class="h4-custom fw-bold">Share Product Catalogs at Scale</h4>
                        <p class="f-s-5">Effortlessly share product catalogs on WhatsApp for impactful campaigns and
                            automated replies.
                        </p>
                    </div>
                </div>
                <div class="d-flex FS-container">
                    <i class="fa-solid fa-mobile-screen-button fa-2xl" style="color: #E99F00"></i>
                    <div class="text-content">
                        <h4 class="h4-custom fw-bold">Automate Business Communication</h4>
                        <p class="f-s-5">Automate FAQs responses and receive COD confirmations, abandoned cart alerts, and
                            offers
                            notifications.</p>
                    </div>
                </div>
                <div class="d-flex FS-container">
                    <i class="fa-solid fa-envelopes-bulk fa-2xl" style="color: #E99F00"></i>
                    <div class="text-content">
                        <h4 class="h4-custom fw-bold">Send Bulk Campaigns & Broadcast</h4>
                        <p class="f-s-5">Create impactful one-time or recurring campaigns to actively engage your
                            consumers and
                            significantly boost sales.</p>
                    </div>
                </div>
                <div class="d-flex FS-container">
                    <i class="fa-solid fa-handshake fa-2xl" style="color: #E99F00"></i>
                    <div class="text-content">
                        <h4 class="h4-custom fw-bold">Collaborate using Team Inbox & Chat Widgets</h4>
                        <p class="f-s-5">Add WhatsApp widgets to your e-commerce site and work with an unlimited number of
                            team members to
                            provide customer service on WhatsApp at scale.</p>
                    </div>
                </div>
                <div class="d-flex FS-container">
                    <i class="fa-solid fa-inbox fa-2xl" style="color: #E99F00"></i>
                    <div class="text-content">
                        <h4 class="h4-custom fw-bold">Monitor Chat & Campaign Analytics</h4>
                        <p class="f-s-5">Add WhatsApp widgets to your e-commerce site and work with an unlimited number of
                            team members to
                            provide customer service on WhatsApp at scale.
                        </p>
                    </div>
                </div>
                <div class="d-flex FS-container">
                    <i class="fa-solid fa-check fa-2xl" style="color: #E99F00"></i>
                    <div class="text-content">
                        <h4 class="h4-custom fw-bold">Verification with a Green Tick</h4>
                        <p class="f-s-5">We assist you in obtaining the Green Tick on WhatsApp, allowing you to establish
                            brand
                            credibility and trust.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {{-- Features-Section End --}}
    {{-- Pricing-Section Heading Start --}}
    <div class="container-fluid  pt-lg-5 pt-md-5 pt-4 overflow-hidden">
        <div class="container-lg ">
            <div class="row d-flex align-items-center">
                <div class="col-lg-6 col-md-6 pe-lg-5 pb-md-4 pb-4 pb-lg-0">
                    <h3 class="h3-custom fw-bold">Flexible <span style="color: #2C8CF4;">Plans</span> &
                        competitive <span style="color: #2C8CF4;">Pricing</span></h3>
                    <p class="f-s-5">We're here to support your scaling needs, whether you require
                        comprehensive
                        automation solutions or smaller, targeted implementations.</p>
                    @include('components.rapbooster.getStarted-dpBtn')

                </div>
                <div class="col-lg-6 col-md-6 text-center z-n1">
                    <img class="img-fluid" src="{{ asset('assets/rapbooster/images/price18.png') }}" alt="">
                </div>
            </div>
        </div>
    </div>
    {{-- Pricing-Section Heading End --}}

    {{-- FAQ SECTION STARTS --}}
    <div class="container-fluid pb-lg-5 pb-md-5 pb-4 pe-0 ps-0">
        <div class="container-lg">
            <h3 class="h3-custom text-center text-dark-color fw-bold">Frequently Asked Questions</h3>
            @php
                $faqs = [
                    [
                        'question' => 'How can businesses use the WhatsApp Business API?',
                        'answer' =>
                            'Businesses can use the WhatsApp Business API to send notifications, updates, customer support messages, and engage in conversations with users, providing a personalized and efficient communication channel.',
                    ],
                    [
                        'question' => 'How secure is the WhatsApp Business API?',
                        'answer' =>
                            'WhatsApp Business API prioritizes security and privacy. It utilizes end-to-end encryption for messages, ensuring that user data is protected throughout the communication process.',
                    ],
                    [
                        'question' => 'Can businesses automate responses with the WhatsApp Business API?',
                        'answer' =>
                            'Yes, businesses can automate responses using the WhatsApp Business API. Automated responses help streamline customer interactions, providing quick and efficient replies.',
                    ],
                    [
                        'question' => 'What industries can benefit from the WhatsApp Business API?',
                        'answer' =>
                            'WhatsApp Business API is versatile and beneficial for a wide range of industries, including retail, healthcare, finance, e-commerce, and more. It offers tailored solutions to meet industry-specific communication needs.',
                    ],
                    [
                        'question' => 'How can customers Opt-In to receive messages from Businesses on WhatsApp?',
                        'answer' =>
                            'Customers need to initiate the conversation or explicitly opt-in to receive messages from businesses on WhatsApp. This ensures that businesses communicate with engaged and interested users.',
                    ],
                    [
                        'question' => 'Can businesses send promotional content using the WhatsApp Business API?',
                        'answer' =>
                            'Yes, businesses can send promotional content through the WhatsApp Business API. However, it\'s essential to adhere to WhatsApp\'s policies and regulations regarding promotional messaging.',
                    ],
                ];
            @endphp

            <div class="container-lg">
                <x-faq-accordion :faqs="$faqs" collapsed-color="#f9f9f9" active-color="#2c8cf4" />
            </div>
        </div>
    </div>
    {{-- FAQ SECTION End --}}
    {{-- Earn With Rapbooster Start --}}
    @include('components.rapbooster.SEWU')
    @include('global.modal.plans.whatsappMarketingPlan')
@endsection
@section('title', 'Automate WhatsApp Business with API for targeted marketing')
@section('metaData')
    <meta name="description"
        content="Transform your business with our services. We offer a chat automation, analytics and messaging API to create campaigns for your business.Book a Demo Now!" />
    <meta name="keywords"
        content="WhatsApp Business API, business API, bulk messaging API, chat communication, chat automation" />
@endsection
