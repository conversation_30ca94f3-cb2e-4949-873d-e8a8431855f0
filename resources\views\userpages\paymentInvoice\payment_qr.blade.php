@extends('userpages.invoice.components.main')
@section('webpage')
    <div class="">
        @php
            $method = 'UPI QR Code';
        @endphp
        <div class="row">
            <div class="col-lg-8 col-md-12 col-sm-12">
                @include('userpages.invoice.card_cart')
                @include('userpages.invoice.card_billing')
                @include('userpages.paymentInvoice.currentMethod')
                <div class="d-flex align-items-center gap-3 p-3" style="background-color: #EAF1FF;border-radius:7px 7px 0 0;">
                    <span class="align-items-center justify-content-center badge d-flex rounded-circle text-white"
                        style="height: 30px;width: 30px;background-color:#194DAB;">4</span>
                    <h4 style="color: #194DAB;">Pay with UPI QR Code</h4>
                </div>
                <div class="bg-white p-3">
                    <div class="w-25 ms-5">
                        <h4 class="text-center countdown" style="color: #194DAB;">
                            <span class="hour"></span>0
                            <span class="fs-6">H</span> :
                            <span class="minute">04</span>
                            <span class="fs-6">M</span> :
                            <span class="second">09</span>
                            <span class="fs-6">S</span>
                        </h4>
                        <hr>
                        <div>
                            @if ($hostdata->enableUpi && $hostdata->upi)
                                {!! $CompanyQr !!}
                            @endif
                            @if ($hostdata->enablePaytmQr)
                                @if ($hostdata->paytm_merchant_mid && $hostdata->paytm_merchant_key)
                                    <img src="{{ $qrString }}" alt="QR CODE" id="qrimg" class="img-fluid  "
                                        style="width: 250px;">
                                @endif
                            @endif

                        </div>
                        <img class="img-fluid img-upiContent d-block mb-1" src="{{ asset('media/icons/upi-icons.png') }}"
                            alt="">
                        <div class="border text-center rounded fw-bold fs-3 p-1">₹ {{ number_format($paybleAmt) }}</div>
                    </div>
                </div>
            </div>
            <div class="col-lg-4 col-md-12 col-sm-12">
                @include('userpages.cart.cartCalculations')
            </div>
        </div>
    </div>
@endsection
@section('PAGE-script')
    <script>
        $(document).ready(function() {

            var timer2 = "05:10";
            var interval = setInterval(function() {


                var timer = timer2.split(':');
                var minutes = parseInt(timer[0], 10);
                var seconds = parseInt(timer[1], 10);
                --seconds;
                minutes = (seconds < 0) ? --minutes : minutes;
                seconds = (seconds < 0) ? 59 : seconds;
                seconds = (seconds < 10) ? '0' + seconds : seconds;
                $('.minute').html(minutes);
                $('.second').html(seconds);
                if (minutes < 0) clearInterval(interval);
                if ((seconds <= 0) && (minutes <= 0)) clearInterval(interval);
                timer2 = minutes + ':' + seconds;
                
                if (minutes == 0 && seconds == 0) {
                    location.reload(true)

                }
            }, 1000);
        });
    </script>
@endsection
