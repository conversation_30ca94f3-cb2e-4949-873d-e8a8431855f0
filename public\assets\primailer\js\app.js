const navbarToggler = document.querySelector(".navbar-toggler");
const navbarContent = document.querySelector(".navbar-collapse");
const dropdowns = document.querySelectorAll(".dropdown");

navbarToggler.addEventListener("click", toggleMenu);

function toggleMenu() {
    navbarToggler.classList.toggle("active");
    navbarContent.classList.toggle("show");

    dropdowns.forEach((dropdown) => {
        dropdown.querySelector("ul.dropdown-menu").classList.remove("show");
    });
}

dropdowns.forEach((dropdown) => {
    dropdown.addEventListener("mouseenter", showDropdown);
    dropdown.addEventListener("mouseleave", hideDropdown);
});

function showDropdown() {
    if (window.innerWidth > 991) {
        this.querySelector("ul.dropdown-menu").classList.add("show");
    }
}

function hideDropdown() {
    if (window.innerWidth > 991) {
        this.querySelector("ul.dropdown-menu").classList.remove("show");
    }
}

navbarToggler.addEventListener("click", function () {
    navbarCollapse.classList.toggle("show");
});

function showMenu() {
    document.getElementById("home").classList.toggle("homedirection");
    document.getElementById("first").classList.toggle("showhead");

    document.getElementById("second").classList.toggle("showhead");
}

//   change toggler icon

$(document).ready(function () {
    $('.navbar-toggler').click(function () {
        //   $(this).toggleClass('active');
        if ($('.navbar-collapse').hasClass('show')) {
            $('.navbar-toggler-icon').removeClass('navbar-toggler-icon').addClass('navbar-cross-icon');
            $("#changeIcon1").css("display", "none");
            $("#changeIcon2").removeClass('d-none').addClass('d-block');
        } else {
            $('.navbar-cross-icon').removeClass('navbar-cross-icon').addClass('navbar-toggler-icon');
            $("#changeIcon1").css("display", "block");
            $("#changeIcon2").removeClass('d-block').addClass('d-none');
        }
    });
});

// space to set for both header to show it properly

var prevScrollPos = window.pageYOffset;
var globalHeader = document.querySelector('.global-header');
var navbarHeader = document.querySelector('.navbar-header');
var showGlobalHeader = true;

window.addEventListener('scroll', function () {
    var currentScrollPos = window.pageYOffset;

    if (currentScrollPos <= 0) {
        showGlobalHeader = true;
    }

    if (showGlobalHeader) {
        globalHeader.style.top = "0";
        navbarHeader.style.top = "2rem";
    } else {
        globalHeader.style.top = "-3rem";
        navbarHeader.style.top = "0";
    }

    if (currentScrollPos == 0) {
        showGlobalHeader = true;
        navbarHeader.style.top = "2rem";
        globalHeader.style.top = "0";
    } else {
        showGlobalHeader = false;
    }

    prevScrollPos = currentScrollPos;
});

