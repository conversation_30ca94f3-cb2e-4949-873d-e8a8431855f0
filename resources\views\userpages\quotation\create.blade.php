@extends('userpages.cart.main')


@section('only-Page-css')
    <style>
        .active-radio {
            border: 1.5px rgb(35, 149, 243) solid !important;
        }

        .checkbox-container {
            border: 1.5px #f5ecec solid;
        }

        .budget-select {
            border: 1.5px #f5ecec solid;
        }
    </style>
@endsection
@section('title','Quotation Create')
@section('userpagesection')
    <div class="">
        @include('components.core.alerts')
        <div class="card ">
            <form action="{{ route('quotation.store') }}" method="POST" enctype="multipart/form-data">
                @csrf
                <!--begin::Row-->
                <div class="row p-3 text-center">
                    <h4 class="fs-4 mb-3 mt-3">Get product Quotation</h4>
                    <h4 class="">Select the services you are looking for <span class=" text-danger"><sup>*</sup></span>
                    </h4>
                </div>
                <!--begin::Row-->
                <!--begin::Row-->
                <div class="row  justify-content-lg-between m-0">
                    <!--begin::Col-->
                    @forelse ($leadfor as $item)
                        <!--begin::Col-->
                        <div class=" col-lg-3 col-md-6  col-sm-8 p-2">
                            <label
                                class="checkbox-container h-100 bg-light text-center w-100 rounded @if (in_array($item->id, old('requirement') ?? []) || $loop->first) {{ 'active-radio' }} @endif ">
                                <div class=" ">
                                    <div class="py-1 px-2 text-start d-none">
                                        <input class="form-check-input rounded-circle mt-2 ms-2 border-0 check-Box"
                                            type="checkbox" value="{{ $item->id }}" @checked(in_array($item->id, old('requirement') ?? []) || $loop->first)
                                            name="requirement[]" />
                                    </div>
                                    <div class="py-2">

                                        <h4 class="m-0">{{ $item->name }} </h4>
                                    </div>
                                </div>

                            </label>
                        </div>
                        <!--end::Col-->
                    @empty
                        <div class="col">
                            <div class="form-check-wrapper">
                                <div class="card bg-gray-200i">
                                    <div class="card-body ">
                                        <h2 class="card-title text-gray-700 pt-5 fs-2">Reach out to your account manager for
                                            issue resolution assistance.</h2>
                                    </div>
                                </div>
                            </div>

                        </div>
                    @endforelse
                    @error('requirement')
                        <span class="fs-6 text-danger pt-3">{{ $message }}</span>
                    @enderror

                </div>
                <!--end::Row-->
                <!--begin::Row-->
                <div class="row p-4 text-center">
                    <h4 class="">What is your budget? <span class="text-danger"><sup>*</sup></span></h4>
                </div>
                <!--end::Row-->
                <!--begin::Row-->
                <div class="row mb-3 w-100 justify-content-lg-between m-0">
                    @foreach ($budget as $list)
                        <!--begin::Col-->
                        <div class="col-lg-3 col-md-3 col-6 p-2">
                            <label
                                class="radio-container budget-select  text-center rounded-2  w-100 h-100 @if ($loop->first) {{ 'active-radio' }} @endif">
                                <div class="text-center">
                                    <div class="d-none">
                                        <input class="form-check-input budget-radio " type="radio"
                                            @if (old('budget') == $list['value'] || $loop->first) {{ 'checked' }} @endif
                                            value="{{ $list['value'] }}" name="budget" required />
                                    </div>
                                    <div class=" ">
                                        <h4 class=" m-0 text-gray-700 p-2 fw-semibold">₹
                                            {{ $list['name'] }}
                                        </h4>

                                    </div>
                                </div>
                            </label>
                        </div>
                        <!--end::Col-->
                    @endforeach
                    @error('budget')
                        <span class="fs-6 text-danger pt-3">{{ $message }}</span>
                    @enderror
                </div>
                <!--end::Row-->
                <input type="hidden" name="product_id" value="{{ $product }}">
                <!--begin::Row-->
                <div class="row mb-3 w-100 justify-content-lg-between m-0">
                    <div class="card ">

                        <div class="card-body ">
                            <!--begin::Row-->
                            <div class="row mb-3">

                                <div class="col-lg-6 col-md-12 col-sm-12 mb-3">
                                    <!--begin::Label-->

                                    <label class="form-label">
                                        <h4>
                                            Preferred Mode of Contact <span class="text-danger">*</span>
                                        </h4>
                                    </label>
                                    <div class="row">
                                        @foreach ($modeOfCCommunications as $mode)
                                            <div class="col">

                                                <div class="form-check  d-flex align-items-center">
                                                    <input class="form-check-input" type="radio" name="mode"
                                                        @if (old('preferredModeOfContact') == $mode['value'] || $loop->first) {{ 'checked' }} @endif
                                                        id="{{ $mode['name'] }}" value="{{ $mode['value'] }}">
                                                    <label class="form-check-label ps-1 pt-1 fs-5"
                                                        for="{{ $mode['name'] }}">
                                                        {{ $mode['name'] }}
                                                    </label>
                                                </div>
                                            </div>
                                        @endforeach
                                        @error('mode')
                                            <span class="text-danger"> {{ $message }}</span>
                                        @enderror
                                    </div>
                                    <!--end::Row-->
                                </div>

                                <div class="col-lg-6 col-md-12 col-sm-12 mb-3">
                                    <!--begin::Label-->

                                    <label class="form-label">
                                        <h4>
                                            Preferred Time <span class="text-danger">*</span>
                                        </h4>
                                    </label>
                                    <div class="row p-1">
                                        @foreach ($preferredTimes as $time)
                                            <div class="col">

                                                <div class="form-check d-flex align-items-center">
                                                    <input class="form-check-input" type="radio" name="preferredTime"
                                                        @if (old('preferredTime') == 0 || $loop->first) {{ 'checked' }} @endif
                                                        id="{{ $time['name'] }}" value="{{ $time['value'] }}">
                                                    <label class="form-check-label ps-1 pt-1 text-nowrap fs-5"
                                                        for="{{ $time['name'] }}">
                                                        {{ $time['name'] }}
                                                    </label>
                                                </div>
                                            </div>
                                        @endforeach
                                        @error('preferredTime')
                                            <span class="text-danger"> {{ $message }}</span>
                                        @enderror
                                    </div>
                                    <!--end::Row-->
                                </div>


                            </div>
                            <div class="row mb-3">
                                <!--begin::solid autosize textarea-->
                                <div class="col">

                                    <label>
                                        <h4>Details of
                                            Requirement <span class="text-danger"><sup>*</sup></span>
                                        </h4>
                                    </label>
                                    <textarea cols="8" placeholder="Details ..." rows="5" name="details" required
                                        class="form-control form-control form-control-solid " data-kt-autosize="true">{{ old('details') }}</textarea>
                                    @error('details')
                                        <span class="fs-6 text-danger">{{ $message }}</span>
                                    @enderror
                                </div>
                                <!--end::solid autosize textarea-->

                            </div>
                            <div class="row mb-3">
                                <div class="col">
                                    <div class="form-group">
                                        <label for="" class="form-label ">
                                            <h4>
                                                Attachment
                                            </h4>
                                        </label>
                                        <input id="my-input" class="form-control form-control form-control-solid  mb-2"
                                            type="file" name="attachment">
                                        @error('attachment')
                                            <span class="fs-6 text-danger">{{ $message }}</span><br>
                                        @enderror
                                        <span class="fs-6 text-secondary pt-3">Image, Zip, Pdf (Less than 20MB)</span>


                                    </div>
                                </div>
                            </div>


                            <!--end::Row-->

                        </div>
                    </div>
                </div>
                <!--begin::Row-->
                <div class="row text-end px-5 pb-3 pe-10">
                    <div class="col">
                        <button class="btn btn-primary px-5 py-2" type="submit">
                            Send
                        </button>
                    </div>
                </div>
            </form>
        </div>

    </div>
@endsection
@section('PAGE-script')
    <script>
        // $(".common-class").
        $(document).ready(function() {
            $('.budget-radio').on('change', function() {
                if ($(this).prop('checked')) {
                    // Remove active class from all radio containers
                    $('.radio-container').removeClass('active-radio');
                    // Add active class to the parent container
                    $(this).closest('.radio-container').addClass('active-radio');
                }
            });


            $('.check-Box').on('change', function() {
                // Get the parent container
                var parentContainer = $(this).closest('.checkbox-container');

                if ($(this).prop('checked')) {
                    // Add active class to the parent container if checkbox is checked
                    parentContainer.addClass('active-radio');
                } else {
                    // Remove active-radio class from the parent container if checkbox is unchecked
                    parentContainer.removeClass('active-radio');
                }
            });

        });
    </script>
@endsection
