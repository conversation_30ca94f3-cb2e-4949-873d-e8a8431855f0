<?php

namespace App\Models;

use App\Models\GraphicLeadBrand;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class GraphicBrandAttachments extends Model
{
    use HasFactory;
    protected $table = "graphic_lead_brand_attachment";
    public $timestamps = false;

    public function brand(){
        return $this->belongsTo(GraphicLeadBrand::class,'graphic_lead_brand_id','id');
    }

}
