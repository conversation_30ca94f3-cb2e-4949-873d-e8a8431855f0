@extends('userpages.invoice.components.main')
@section('PAGE-CSS')
    <style>
        .billing-grid-items {
            background-color: #FFF;
            border-radius: 7px;
            box-shadow: 0px 4px 7.199999809265137px 0px #00000008;
            position: relative;
        }

        .ribbon {
            position: absolute;
            top: 60px;
            right: -31px;
            background-color: #EAF1FF;
            color: #194DAB;
            font-size: 12px;
            font-weight: bold;
            padding: 2px 42px;
            transform: rotate(45deg);
            transform-origin: top right;
        }

        .active-profile {
            border: 1px solid #194DAB !important;
        }

        .address-radio {
            border: 1px solid #CCCCCC;
        }
    </style>
@endsection
@section('webpage')
    <div class="">
        <div class="row">
            <div class="col-lg-8  col-sm-12   col-md-12 ">
                @include('userpages.invoice.card_cart')
                <div class="bg-white">
                    <div class="d-flex align-items-center justify-content-between gap-3 px-3 mb-3 p-3"
                        style="background-color: #EAF1FF;border-radius:7px 7px 0 0;">
                        <div class="d-flex align-items-center gap-3">
                            <span class="align-items-center justify-content-center badge d-flex rounded-circle text-white"
                                style="height: 30px;width: 30px;background-color:#194DAB;">2</span>
                            <h4 style="color: #194DAB;" class="m-auto">Organizational information</h4>
                        </div>
                        <div class="text-end  ">
                            @if ($lead->addresses->count() != 0)
                                <button type="button" class="btn p-2 ps-3 pe-3 rounded-pill text-white"
                                    style="background-color: #194DAB;" data-bs-toggle="modal" data-bs-target="#addAddress">
                                    <i class="fa-solid fa-add"></i>
                                    Create Billing Address
                                </button>
                            @endif
                        </div>
                    </div>
                    @if ($lead->addresses->count() != 0)
                        <form action="{{ route('inv.address.update') }}" method="POST" id="Inv_address_form">
                            @csrf
                    @endif
                    <div class="p-3">
                        @forelse ($lead->addresses as $address)
                            <div
                                class="billing-grid-items overflow-hidden @if ($address->isPrimary) {{ 'active-profile' }} @endif address-radio  @if (!$loop->last) mb-3 @endif">
                                @if ($address->isPrimary)
                                    <div class="ribbon">Default</div>
                                @endif
                                <label class="d-flex justify-content-between flex-column h-100 p-3 py-1"
                                    for="billing_address_id_{{ $address->id }}">
                                    <div class="d-flex align-items-center gap-2">

                                        {{-- <input class="me-2" type="radio" value="{{ $address->id }}"
                                                name="address_id" id="billing_address_id_{{ $address->id }}"
                                                @checked($address->isPrimary)> --}}
                                        <div class="form-check">
                                            <input class="form-check-input  me-2" type="radio" value="{{ $address->id }}"
                                                name="address_id" id="billing_address_id_{{ $address->id }}"
                                                @checked($address->isPrimary)>
                                        </div>
                                        <div>
                                            <span class="m-0 ba-name">
                                                {{ $address->name }}
                                            </span>
                                            <span class="m-0 ba-company">
                                                {{ $address->company }}
                                            </span>
                                            <span class="gray-dark-text text-nowrap">
                                                {{ $address->gst }}
                                            </span>
                                            <span class="gray-dark-text m-0">
                                                {{-- {{ $address->id }} --}}
                                                {{ $address->address ? $address->address . ', ' : '' }}
                                                {{ $address->city ? $address->city . ', ' : '' }}
                                                {{ $address->state ? $address->state . ', ' : '' }}
                                                {{ $address->country ? $address->country : '' }}
                                                {{ $address->pinCode ? ' (' . $address->pinCode . ') ' : '' }}

                                            </span>
                                        </div>
                                    </div>
                                    <div class="d-flex align-items-center align-self-baseline text-end">
                                        <button type="button" class="btn btn-link billingProfileEditBtn"
                                            value="{{ $address->id }}">
                                            Edit Address
                                        </button>
                                        @if ($address->isPrimary != 1)
                                            <div class="text-secondary">|</div>

                                            <button type="button" class="btn btn-link setAsDefaultBtn"
                                                data-address-id="{{ $address->id }}">
                                                Set as default
                                            </button>
                                        @endif
                                    </div>
                                </label>
                            </div>
                        @empty
                            @include('components.core.billingAddressForm')
                        @endforelse

                    </div>
                    @if ($lead->addresses->count() != 0)
                        <hr>
                        <div class="text-end mt-3 p-3">
                            <button type="submit" class="btn p-2 ps-3 pe-3 rounded-pill text-white"
                                style="background-color: #194DAB;" onclick="$('#Inv_address_form').submit()">
                                Continue
                                <i class="fa-solid fa-chevron-right"></i>
                            </button>
                        </div>
                        </form>
                    @endif
                </div>
            </div>
            <div class="col-lg-4 col-md-12 col-sm-12">

                @include('userpages.cart.cartCalculations')

            </div>
        </div>
    </div>
@endsection
@section('PAGE-script')
    <script>
        $('#addpincode').change(async function() {
            var pincode = $(this).val();

            try {
                let res = await fetch("https://api.postalpincode.in/pincode/" + pincode);
                let response = await res.json();
                var postData = response[0]['PostOffice'][0];

                $('#addcountry').val(postData.Country);
                $('#addstate').val(postData.State);
                $('#addcity').val(postData.District);
            } catch (error) {
                console.log(error);
            }
        });
    </script>
    <style>
        .modal-backdrop {
            display: none !important;
        }
    </style>
    @if ($lead->addresses->count() != 0)
        @include('userpages.billingAddress.addModal')
    @endif

    @include('userpages.billingAddress.EditModal')

    <script>
        $(document).ready(function() {
            $('input[name="address_id"]').change(function() {
                $('.address-radio').removeClass('active-profile'); // Remove border from all elements
                $(this).closest('.address-radio').addClass(
                    'active-profile'); // Add border to the clicked element
            });
        });
    </script>

    <script>
        $('.setAsDefaultBtn').on('click', function() {
            var addressId = $(this).data('address-id');
            $(this).prop("disabled", true);
            $.ajax({
                url: "{{ route('billingAddress.setasDefault') }}",
                type: "POST",
                data: {
                    id: addressId,
                    _token: '{{ csrf_token() }}'
                },
                success: function(response) {
                    // Handle success
                    location.reload();
                },
                error: function(xhr) {
                    // Handle error
                    console.log(xhr.responseText);
                }
            });
        });
    </script>
@endsection
