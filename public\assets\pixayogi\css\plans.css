/*------------------------------------------------- FIRST SECTION CSS START --------------------------------------------------------- */
h1 {
    font-weight: bold;
    font-size: 65px;
    line-height: 1.2;
}

h2 {
    font-weight: bold;
    font-size: 60px;
    line-height: 1.2;
}

h3 {
    font-weight: 700;
    margin: 0;
    font-size: 50px;
}

@media screen and (max-width: 992px) {
    h2 {
        font-size: 40px !important;
    }
}

@media only screen and (max-width: 425px) {

    h1 {
        font-size: 40px;
    }

    h2 {
        font-size: 45px;
    }

    h3 {
        font-size: 35px;
    }
}

.bg-img-fixed {
    background-repeat: no-repeat;
    background-size: cover;
    background-attachment: fixed;
}

@media screen and (max-width: 992px) {

    .bg-img-fixed {
        background-size: 0px;
    }

}

@media only screen and (max-width: 330px) {
    .Help-top-btn {
        flex-direction: column;
    }

    .green-btn {
        width: max-content;
    }

    .black-btn {
        width: max-content;
    }
}

.tick-1 {
    width: 33px;
    height: 33px;
}

.card-button-container {
    justify-content: center;
}

.heading {
    font-size: 50px;
    font-weight: 700;
}

.contact-heading {
    font-size: 50px;
    font-weight: 700;
}

#section-1-img {
    width: 500px;
    height: 420px;
    border-radius: 20px;
}

.section1-right {
    padding-top: 40px;
    padding-right: 71px;
}

.first-button {
    background-color: #44c9f5;
    padding: 11px 23px;
    font-size: 13px;
    max-width: fit-content;
}

.card-button {
    background-color: #44c9f5;
    padding: 13px 47px;
    font-size: 18px;
}

.card-button:hover {
    background: linear-gradient(to bottom, #44c9f5, #78ebff);

}

.first-button:hover {
    background: linear-gradient(to bottom, #44c9f5, #78ebff);
}

#first-p {
    font-size: 17px;
    color: #7E7887;
}

/*------------------------------------------------- FIRST SECTION CSS end --------------------------------------------------------- */
/*------------------------------------------------- second SECTION CSS start --------------------------------------------------------- */
.comman-font {
    color: #7E7887;
}

.second-sec-p {
    color: #7E7887;
    font-size: 21px;
}

.second-container {
    background-color: rgba(33, 158, 188, 0.1);
    border-radius: 10px;
    padding-top: 10px;
}

.card-container {
    background-color: #FFFFFF;
    border-radius: 10px;
}

.card-heading {
    font-size: 30px;
    font-weight: 700;
    padding: 20px;
    padding-left: 0px;
}

.card-p {
    font-size: 18px;
    color: #7E7887;
}

.custom-color {
    color: #219ebc
}

.strike-through {
    text-decoration: line-through;
}

.tick-icon {
    color: #219ebc;
    padding-right: 30px;
}

.tick-desc {
    font-size: 15px;
    color: #7E7887;
    margin-bottom: 0px;
}

.footer-p {
    font-size: 11px;
}


/*------------------------------------------------- second SECTION CSS end --------------------------------------------------------- */
/*------------------------------------------------- third SECTION CSS start --------------------------------------------------------- */
.pb-column-p {
    font-size: 20px;
    color: #7E7887;
}

/*------------------------------------------------- third SECTION CSS end --------------------------------------------------------- */
/*------------------------------------------------- fourth SECTION CSS start --------------------------------------------------------- */
.sixth-container {
    background-color: #44C9F5;
}

.number {
    font-size: 70px;
    color: white;
    font-weight: 100;
}

.sixth-sec-heading {
    font-size: 25px;
    color: white;
    margin-bottom: 0;
    font-weight: 700;
    padding-left: 20px;
}

.sixth-sec-p {
    font-size: 20px;
}

.div-collapse {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
}

.div-collapse.show {
    max-height: auto;
    /* Set an appropriate value */
}

.div-collapse div {
    color: #7E7887;
    font-size: 15px;

}

.col-lg-11 a:hover {
    color: linear-gradient(to bottom, #44c9f5, #78ebff);

}


.robo-p {
    color: #7E7887;
    font-size: 20px;
}

.text-black {
    color: black;
    font-weight: 300;
}

/*------------------------------------------------- fourth SECTION CSS END --------------------------------------------------------- */
/*------------------------------------------------- fifith SECTION CSS start --------------------------------------------------------- */
.last-img {
    width: 586px;
    height: 600px;
    padding-top: 71px;
    padding-left: 76px;
    margin-left: 26px;
}

.last-sec-p {
    color: #7E7887;
    font-size: 21px;

}

.last-number {
    color: #44c9F5;
    font-size: 36px;
}

.separator {
    border-top: 1px solid black;
    width: 80px;
    position: relative;
    top: 20px;
}

.last_button {
    background-color: #44c9f5;
    padding: 11px 23px;
    font-size: 18px;
    position: relative;
    top: 40px;
    font-weight: 500;
    text-decoration: none;
    width: 300px;
}

.last_button:hover {
    background: linear-gradient(to bottom, #44c9f5, #78ebff);
    text-decoration: none;
}

.contact-container-left {
    padding-top: 100px;
}

.card-button-container {
    width: 100%;
}

/*------------------------------------------------- eighth SECTION CSS END --------------------------------------------------------- */
@media only screen and (min-width: 993px) {
    .section1-left {
        margin-top: 100px;
    }

    .first-button {
        max-width: fit-content;

    }

    #plans-colored-container {
        margin-bottom: 100px;
    }

    .card-buttons {
        width: 100%;
        background-color: #44c9f5;
        padding: 11px 23px;
        font-size: 18px;
        margin-top: 28px;
        margin-bottom: 72px;
    }

    .last-card-p {
        margin-bottom: 17px;
        margin-top: 12px;
    }



}

@media (width: 993px) {
    .last-img {
        width: 492px;
        height: 539px;
        padding-top: 71px;
        padding-left: 0px;
        margin-left: 0px;
    }
}

@media only screen and (max-width: 992px) {
    .first-container {
        flex-direction: column;
    }

    .contact-heading {
        line-height: 52px;
    }

    #section-1-img {
        width: 725px;
        height: 500px;
        border-radius: 20px;
    }

    .section1-left {
        margin-top: 0px;
    }


    .footer-p {
        font-size: 13px;
    }

    #first-p {
        font-size: 20px;
        color: #7E7887;
        padding: 10px;
    }

    .heading {
        font-size: 45px;
    }


    .sec-container.row {
        padding: 36px;
        padding-bottom: 0px;
        padding-top: 0px;
        /* column-gap: 176px; */
        gap: 50px;
    }

    .tick-desc {
        font-size: 20px;
        color: #7E7887;
        margin-bottom: 0px;
        margin-top: 5px;
    }

    .tick-icon {
        margin-top: 8px;
    }

    .second-container {
        background-color: rgba(33, 158, 188, 0.1);
        border-radius: 10px;
        padding-top: 10px;
        width: 100%;
        padding: 27px;

    }

    .card-p {
        font-size: 20px;
    }

    .card-heading {
        font-size: 40px;
        font-weight: 600;
        padding: 20px;
        padding-left: 0px;
    }

    .card-button-container {
        justify-content: center;
    }

    .pb-img-container {
        margin-bottom: 30px;
    }

    .pb-column-p {
        padding: 0px 70px;
    }

    .second-sec-p {
        padding: 0px 45px;
    }

    .robo-p {

        padding: 0px 50px;
    }

    .contact-num-container {
        margin-bottom: 40px;
    }

    .number-container {
        margin-left: 9px;
    }

    .last_button {
        padding: 11px 23px;
        font-size: 19px;
        position: relative;
        top: 0px;
        left: 6px;
        font-weight: 500;
        text-decoration: none;
        width: 300px;
    }

    .contact-img-container {
        display: flex;
        justify-content: center;
        margin-top: 20px;
    }

    .last-img {
        width: 586px;
        height: 600px;
        padding-top: 0px;
        padding-left: 76px;
        margin-left: 26px;
    }

    .contact-container-left {
        padding-top: 0px;
    }

    .section1-right {
        padding-top: 0px;
        padding-right: 0px;
    }
}

@media only screen and (max-width:767px) {
    .first-button {
        background-color: #44c9f5;
        padding: 11px 23px;
        font-size: 21px;
        max-width: fit-content;
        margin-top: 28px;
        margin-bottom: 0px;
    }

    #section-1-img {
        width: 600px;
        height: 500px;
        border-radius: 20px;
    }

    .contact-heading {
        font-size: 41px;
        font-weight: 700;
    }
}

@media only screen and (max-width:425px) {
    #section-1-img {
        width: 600px;
        height: 410px;
        border-radius: 20px;
    }

    #section-1-img {
        width: 359px;
        height: 300px;
        border-radius: 20px;
    }

    .last-img {
        width: 367px;
        height: 394px;
        padding-top: 0px;
        padding-left: 0px;
        margin-left: 0px;
    }

    .contact-heading {
        font-size: 35px;
        font-weight: 700;
        line-height: 40px;
    }

    .robo-p {
        padding: 0px 0px;
    }

    .heading {
        font-size: 50px;
    }

    .pb-column-p {
        padding: 0px 0px;
    }

    .second-sec-p {
        padding: 0px 0px;
    }

    .heading {
        font-size: 55px;
    }

    .sec-container.row {
        padding: 0px;
        /* column-gap: 176px; */
        gap: 50px;
    }

    .first-button {
        background-color: #44c9f5;
        padding: 11px 23px;
        font-size: 17px;
        width: 100%;
        margin-top: 28px;
        margin-bottom: 0px;
    }

    .card-heading {
        font-size: 30px;
    }

    .card-p {
        font-size: 17px;
        margin-bottom: 20px;
    }

    .section1-right {
        padding-top: 0px;
        padding-right: 0px;
    }

}

@media (min-width: 425px) and (max-width: 614px) {
    .section1-right {
        padding-top: 0px;
        padding-right: 0px;
    }

    #section-1-img {
        width: 600px;
        height: 450px;
        border-radius: 20px;
    }

    .last-img {
        width: 460px;
        height: 495px;
        padding-top: 0px;
        margin-top: 50px;
        padding-left: 0px;
        margin-left: 0px;
    }
}

@media (min-width: 993px) and (max-width: 1399px) {
    .last-card-heading {
        padding-bottom: 0px;
    }

    .last-card-p {
        margin-top: 7px;
        margin-bottom: 15px;
    }

    .first-button {
        margin-bottom: 0px;
    }
}

@media only screen and (max-width: 320px) {
    .second-container {
        background-color: rgba(33, 158, 188, 0.1);
        border-radius: 10px;
        padding-top: 10px;
        width: 100%;
        padding: 0px;
    }

    .card-button {
        background-color: #44c9f5;
        padding: 11px 23px;
        font-size: 16px;
    }

    .heading {
        font-size: 45px;
    }

    .contact-heading {
        font-size: 33px;
        font-weight: 700;
        line-height: 39px;
    }

    #first-p {
        font-size: 18px;
        color: #7E7887;
        padding: 10px;
    }

    .section1-left {
        margin-top: 0px;
    }
}

@media only screen and (max-width: 595px) {
    .heading {
        font-size: 45px;
    }

    .card-button {
        background-color: #44c9f5;
        padding: 11px 23px;
        font-size: 18px;
    }

    .plan-heading {
        margin-top: 20px;
    }

    .second-sub-container {
        font-size: 18px !important;
    }

    .tick-desc {
        font-size: 18px;
        color: #7E7887;
        margin-bottom: 0px;
        margin-top: 5px;
    }

    .robo-p {
        color: #7E7887;
        font-size: 16px;
    }

    .last-sec-p {
        color: #7E7887;
        font-size: 18px;
    }

    .second-sec-p {
        color: #7E7887;
        font-size: 18px;
    }

    .pb-img-container {
        margin-bottom: 0px;
    }
}

/* Blog page CSS */

.blog-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    row-gap: 1rem;
    column-gap: 1rem;
}

@media only screen and (max-width: 768px) {
    .blog-grid {
        grid-template-columns: repeat(1, 1fr);
        row-gap: 2rem;
    }
}
