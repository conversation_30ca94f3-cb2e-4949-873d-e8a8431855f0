<?php

namespace App\Http\Controllers;

use Exception;
use App\Custom\Paytm;
use App\Models\Invoice;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Crypt;

class PaytmController extends Controller
{
    public function verify(Request $request)
    {
        try {

            $paytm = new Paytm;
            $res = $paytm->verify($request->upi);
            return response()->json(['status' => true, 'response' => $res]);
        } catch (Exception $e) {
            return response()->json(['status' => false]);
        }
    }

    public function response(Request $r)
    {
        return redirect()->route('inv.payment')->with('info', $r->RESPMSG);
    }
    public function showPopUp(Request $request)
    {
        try {
            $str = json_decode(Crypt::decryptString($request->paymentKey), true);
            $hostdata = hostData();
        } catch (Exception $e) {
            return response()->json(["status" => false, "msg" => ErrMsg()]);
        }
        try {

            if (isset($str['mode']) && $str['mode'] == 'addFund') {

                $invoice = null;
            } else {
                if (isset($str['invoice_id']) && $str['invoice_id'] != null) {
                    $inv_id = $str['invoice_id'];
                } else {
                    $inv = generateInvoice($str['lead_id']??auth()->id());
                    if ($inv['status'] == true) {
                        $inv_id = $inv['id'];
                    } else {
                        return redirect()->back()->with('error', ErrMsg());
                    }
                }
                $invoice = Invoice::find($inv_id  ?? null);
                if ($invoice) {
                    if ($invoice->isPaid == 1) {
                        return redirect()->route('alreadyPaid');
                    }
                }
            }
            $hostdata = hostData();
            if ($hostdata->enablePaytmLink && $hostdata->paytm_merchant_mid && $hostdata->paytm_merchant_key) {
                $paytm = new Paytm();
                $data['paytmGateWay'] = $paytm->gateway($str['amount'],  $str['lead_id'], $inv_id);
                if ($data['paytmGateWay']['status']) {
                    $data['paytmGateWayEnable'] = true;
                    $data['orderId'] = $data['paytmGateWay']['orderIdGW'];
                    $data['paytmTxnToken'] = $data['paytmGateWay']['paytmTxnToken'];
                    $data["paytm_merchant_mid"] = $hostdata->paytm_merchant_mid;
                    $data['amount'] = $str['amount'];
                    $data["status"] = true;
                }
                return response()->json($data);
            }
        } catch (Exception $e) {
            return response()->json(["status" => false, "msg" => ErrMsg()]);
        }
    }
}
