<div class="modal fade" id="billingProfileEdit" tabindex="-1" aria-labelledby="billingProfileEdit" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <div class="">
                    <h4 class="heading-font">
                        Edit Billing Profile
                    </h4>
                </div>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form action="<?php echo e(route('billingAddress.modal.update')); ?>" method="post">
                    <?php echo csrf_field(); ?>
                    <input type="hidden" name="address_id" id="id" value="<?php echo e(old('address_id')); ?>" />
                    <div class="row mb-3">
                        <div class="col-lg-6">
                            <label class="form-label support-label">Name<span style="color: red;">*</span> </label>
                            <div class="mb-3">
                                <input type="text" name="name" class="form-control fieldInput " id="name"
                                    placeholder="Name" value="<?php echo e(old('name')); ?>">
                                <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="text-danger"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>

                            <label class="form-label support-label">GST No.</label>
                            <div class="mb-3">
                                <input type="text" name="gst" class="form-control fieldInput" id="gst"
                                    placeholder="Ex: 08AAACH0000R0Z0" value="<?php echo e(old('gst')); ?>">
                                <?php $__errorArgs = ['gst'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="text-danger"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>

                            <label class="form-label support-label">Business Name</label>
                            <div class="mb-3">
                                <input type="text" name="company" class="form-control fieldInput"
                                    placeholder="Business Name" id="company" value="<?php echo e(old('company')); ?>">
                                <?php $__errorArgs = ['company'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="text-danger"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>

                            <label class="form-label support-label">Country</label>
                            <div class="mb-3">
                                <input type="text" name="country" class="form-control fieldInput" id="country"
                                    placeholder="Country" value="<?php echo e(old('country')); ?>">
                                <?php $__errorArgs = ['country'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="text-danger"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                            <div class="d-flex gap-4 mt-2">

                                <label class="form-label support-label">Make This Address Default </label>
                                <div class="">
                                    <input class="form-check-label form-check-input form-select-lg" name="isPrimary"
                                        type="checkbox" value="" id="addressPrimaryCheck">

                                    <?php $__errorArgs = ['isPrimary'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="text-danger"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6">
                            <label class="form-label support-label">PIN/ZIP Code</label>
                            <div class="mb-3">
                                <input type="text" name="pinCode" class="form-control fieldInput" placeholder="Pin"
                                    id="pincode" value="<?php echo e(old('pincode')); ?>">
                                <?php $__errorArgs = ['pinCode'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="text-danger"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>

                            <label class="form-label support-label">City</label>
                            <div class="mb-3">
                                <input type="text" name="city" class="form-control fieldInput" placeholder="City"
                                    id="city" value="<?php echo e(old('city')); ?>">
                                <?php $__errorArgs = ['city'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="text-danger"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>

                            <label class="form-label support-label">State</label>
                            <div class="mb-3">
                                <input type="text" name="state" class="form-control fieldInput" placeholder="State"
                                    id="state" value="<?php echo e(old('pincode')); ?>">
                                <?php $__errorArgs = ['pincode'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="text-danger"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>



                            <label class="form-label support-label">Billing Address</label>
                            <div class="mb-3">
                                <textarea class="form-control fieldInput" name="address" placeholder="Dunes Factory Pvt. Ltd., Bikaner"
                                    rows="3" id="address"> <?php echo e(old('address')); ?>" </textarea>
                                <?php $__errorArgs = ['address'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="text-danger"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>

                            <div class="align-items-center justify-content-end gap-3 mt-5 d-flex">
                                <button type="button" class="rounded-pill ps-4 pe-4 pt-1 pb-1 text-white"
                                    data-bs-dismiss="modal" aria-label="Close"
                                    style="background-color: #DE0505;border: 2px solid #DE0505;">
                                    Cancel
                                </button>
                                <button
                                    class="rounded-pill text-white ps-4 pe-4 pt-1 pb-1 d-flex align-items-center gap-3 btn-bg-blue">
                                    Update
                                    <i class="fa-solid fa-arrow-right text-white"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                    <?php echo $__env->make('components.core.modal-alerts', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                </form>
            </div>
        </div>
    </div>
</div>



<?php if(session('isEditModalShow') == true): ?>
    <script>
        $(document).ready(function() {
            new bootstrap.Modal($('#billingProfileEdit')).show()
        });
    </script>
<?php endif; ?>


<script>
    $('.billingProfileEditBtn').on("click", function() {
        let btn = $(this).val();
        $.ajax({
            url: window.location.origin + "/billingAddress/" + btn + "/edit",
            method: "GET",
            data: {
                id: btn
            },
            dataType: 'json',
            success: function(response) {
                console.log("response: ", response.address);
                $('#name').val(response.address.name);
                $('#gst').val(response.address.gst);
                $('#company').val(response.address.company);
                $('#pincode').val(response.address.pincode);
                $('#city').val(response.address.city);
                $('#state').val(response.address.state);
                $('#country').val(response.address.country);
                $('#address').text(response.address.address);
                $('#id').val(response.id);

                $('#addressPrimaryCheck').prop('checked', response.address.isPrimary == 1);

                $('#billingProfileEdit').modal('show');
            },
        });
    })
</script>
<?php /**PATH C:\Users\<USER>\Desktop\live\websites_laravel\resources\views/userpages/billingAddress/EditModal.blade.php ENDPATH**/ ?>