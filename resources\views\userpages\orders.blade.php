@extends('userpages.main')
@section('title', 'Orders')
@section('userpagesection')
    <div class="d-flex row  justify-content-around">
        <div class="col-lg-3 d-none d-lg-inline-block">
            <div class="">
                @include('userpages.sideMenu')
            </div>
        </div>
        <div class="col-lg-9 col-sm-12 col-md-12">
            <div class="p-2 ms-lg-2 m-0">
                <div class="card p-0 mb-4">
                    <div class="card-header bg-secondary">
                        <div class="card-title m-0">
                            <h4 class="fs-4 text-white m-0 ">
                                Orders
                            </h4>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <table class="table table-hover table-striped">
                            <thead class="text-capitalize  bg-secondary">
                                <tr class="fw-bold fs-6">
                                    <th class="ps-8">Invoice ID</th>
                                    <th>
                                        Date
                                    </th>
                                    <th>Remarks</th>
                                    <th>Items</th>
                                    <th>action</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse ($data as $order)
                                    @php
                                        $hash = Crypt::encrypt($order->id);
                                    @endphp
                                    {{-- {{ $order }} --}}
                                    <tr>
                                        <td class="ps-5">{{ $order->invoice->invoiceNumber ?? '-' }}</td>
                                        <td>{{ humanDate($order->date) }}</td>

                                        <td>{{ $order->remarks }}</td>
                                        <td>
                                            @if ($order->orderItems->count() != 0)
                                                <ul style="list-style: unset;">

                                                    @foreach ($order->orderItems as $item)
                                                        <li>
                                                            <b>
                                                                {{ $item->product->name }}
                                                                x
                                                                {{ $item->quantity }}
                                                            </b>
                                                            for <b>
                                                                {{ Str::swap(['y' => ' Year', 'd' => ' Day', 'm' => ' Month'], $item->period) }}
                                                            </b>
                                                            @
                                                            <b>
                                                                ₹{{ $item->product->sellingPrice }}
                                                            </b>
                                                        </li>
                                                    @endforeach
                                                @else
                                                    ----
                                                </ul>
                                            @endif
                                        </td>

                                        <td>
                                            <!-- Large button groups (default and split) -->

                                            {{-- <div class="dropdown ms-3">
                            <button class="btn btn-light-primary btn-icon btn-sm h-30px w-30px" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fa-solid fa-ellipsis-vertical"></i>
                            </button>
                            <ul class="dropdown-menu rounded-1 w-100 p-0">
                                <li class="m-0"><a aria-label="link" class="dropdown-item p-2 " href="#">
                                        <span class="px-2">
                                            <svg xmlns="http://www.w3.org/2000/svg" height="1em" viewBox="0 0 512 512"><!--! Font Awesome Free 6.4.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2023 Fonticons, Inc. -->
                                                <path d="M288 32c0-17.7-14.3-32-32-32s-32 14.3-32 32V274.7l-73.4-73.4c-12.5-12.5-32.8-12.5-45.3 0s-12.5 32.8 0 45.3l128 128c12.5 12.5 32.8 12.5 45.3 0l128-128c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0L288 274.7V32zM64 352c-35.3 0-64 28.7-64 64v32c0 35.3 28.7 64 64 64H448c35.3 0 64-28.7 64-64V416c0-35.3-28.7-64-64-64H346.5l-45.3 45.3c-25 25-65.5 25-90.5 0L165.5 352H64zm368 56a24 24 0 1 1 0 48 24 24 0 1 1 0-48z" />
                                            </svg>
                                        </span>
                                        Download
                                    </a></li>

                                <li class="m-0">
                                    <a aria-label="link" class="dropdown-item p-2" href="{{ route('repeat.order', ['order' => $order]) }}">
                                        <span class="px-2">
                                            <svg xmlns="http://www.w3.org/2000/svg" height="1em" viewBox="0 0 512 512"><!--! Font Awesome Free 6.4.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2023 Fonticons, Inc. -->
                                                <path d="M0 224c0 17.7 14.3 32 32 32s32-14.3 32-32c0-53 43-96 96-96H320v32c0 12.9 7.8 24.6 19.8 29.6s25.7 2.2 34.9-6.9l64-64c12.5-12.5 12.5-32.8 0-45.3l-64-64c-9.2-9.2-22.9-11.9-34.9-6.9S320 19.1 320 32V64H160C71.6 64 0 135.6 0 224zm512 64c0-17.7-14.3-32-32-32s-32 14.3-32 32c0 53-43 96-96 96H192V352c0-12.9-7.8-24.6-19.8-29.6s-25.7-2.2-34.9 6.9l-64 64c-12.5 12.5-12.5 32.8 0 45.3l64 64c9.2 9.2 22.9 11.9 34.9 6.9s19.8-16.6 19.8-29.6V448H352c88.4 0 160-71.6 160-160z" />
                                            </svg>
                                        </span>
                                        Repeat order
                                    </a>
                                </li>
                            </ul>
                        </div> --}}
                                            <a aria-label="link" class="btn btn-primary btn-sm px-3 py-1"
                                                href="{{ route('repeat.order', ['order' => $order]) }}">
                                                <span>
                                                    <svg xmlns="http://www.w3.org/2000/svg" height="1em" fill="#fff"
                                                        viewBox="0 0 512 512"><!--! Font Awesome Free 6.4.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2023 Fonticons, Inc. -->
                                                        <path
                                                            d="M0 224c0 17.7 14.3 32 32 32s32-14.3 32-32c0-53 43-96 96-96H320v32c0 12.9 7.8 24.6 19.8 29.6s25.7 2.2 34.9-6.9l64-64c12.5-12.5 12.5-32.8 0-45.3l-64-64c-9.2-9.2-22.9-11.9-34.9-6.9S320 19.1 320 32V64H160C71.6 64 0 135.6 0 224zm512 64c0-17.7-14.3-32-32-32s-32 14.3-32 32c0 53-43 96-96 96H192V352c0-12.9-7.8-24.6-19.8-29.6s-25.7-2.2-34.9 6.9l-64 64c-12.5 12.5-12.5 32.8 0 45.3l64 64c9.2 9.2 22.9 11.9 34.9 6.9s19.8-16.6 19.8-29.6V448H352c88.4 0 160-71.6 160-160z" />
                                                    </svg>
                                                </span>
                                                Repeat order
                                            </a>

                                        </td>

                                    </tr>

                                @empty
                                    <tr>
                                        <td colspan="5">
                                            <h2 class="text-danger text-center" style="font-size: 25px !important;">
                                                @if ($search ?? 0)
                                                    No Record Found with keyword "{{ $search }}"
                                                @else
                                                    It seems there are no orders associated with your account.
                                                @endif
                                            </h2>
                                        </td>
                                    </tr>
                                @endforelse

                            </tbody>

                        </table>
                    </div>
                    @if ($data->count() > 9)
                        <div class="card-footer">
                            {{ $data->links() }}
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>


@endsection
