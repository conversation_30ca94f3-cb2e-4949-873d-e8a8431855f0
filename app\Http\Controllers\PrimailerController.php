<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use App\Models\KnowladgeBaseArtical;
use App\Models\KnowladgeBaseCategory;

class PrimailerController extends Controller
{
    public function PageHelp()
    {
        try {
            DB::beginTransaction();
            $data['host'] = $host = request()->getHttpHost();
            $data['Cats'] = KnowladgeBaseCategory::where('domains', 'Like', "%{$host}%")->with('SubCategories', 'Articals')->where('isActive', 1)->get();
            $data['forAllCategory'] = KnowladgeBaseCategory::where('kbParentCategory_id', NULL)->get();
            $data['ids'] = $data['Cats']->pluck('id');
            $data['openArticals'] = KnowladgeBaseArtical::where('domains', null)->orWhere('domains', '')->where('isOnAll', 1)->where('isActive', 1)->get();
            DB::commit();
            return view("global.help", $data);
        } catch (\Exception $e) {
            
            return redirect()->back()->with("error", ErrMsg());
        }
    }
}
