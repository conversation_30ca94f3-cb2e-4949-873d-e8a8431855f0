<?php

namespace App\Models\Graphic;

use App\Models\Graphic\DesignCategory;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Design extends Model
{
    use HasFactory;

    protected $table = "graphic_design";
    public $timestamps = false;

    public function category()
    {
        return $this->belongsTo(DesignCategory::class, 'graphic_design_category_id', 'id');
    }
}
