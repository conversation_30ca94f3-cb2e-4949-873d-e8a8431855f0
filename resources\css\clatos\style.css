/*------------------------------------------------- navbar css Starts --------------------------------------------------------- */
.sticky-navbar {
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 9999;
    background-color: #ffffff;
    transition: background-color 0.3s ease-in-out;
}

.navbar-brand {
    display: flex;
    align-items: center;
}

.navbar-brand img {
    max-height: 100%;
    margin-right: 10px;
}

#navbar {
    width: 100%;
    position: relative;
    display: flex;
    align-items: center;
    position: sticky;
    top: 0;
    background-color: white;
    z-index: 1;
    justify-content: space-between;
}

#logo img {
    height: 60px;
}


.links {
    text-decoration: none;
    font-weight: 500;
    color: #525260;
    font-size: 16px;
}

.links:hover {
    color: #168cd1;
    text-decoration: none;
}

.head-product {
    font-size: 1rem;
    font-weight: 700;
}

.hover-underline-animation {
    display: inline-block;
    position: relative;
    text-decoration: none;
}

.hover-underline-animation:after {
    content: "";
    position: absolute;
    width: 100%;
    transform: scaleX(0);
    height: 2px;
    bottom: 0;
    left: 0;
    background-color: #0087ca;
    transform-origin: bottom right;
    transition: transform 0.25s ease-out;
    text-decoration: none;
}

.hover-underline-animation:hover:after {
    transform: scaleX(1);
    transform-origin: bottom left;
}

.hover-underline-animation:active {
    color: #0087ca;
}

.theme-color-bg {
    background-color: #FFF;
}

a.underline-hover-effect {
    text-decoration: none;
    color: inherit;
}

.underline-hover-effect {
    display: inline-block;
    padding-bottom: 0.25rem;
    position: relative;
}

.underline-hover-effect::before {
    content: "";
    position: absolute;
    left: 0;
    bottom: 0;
    width: 0;
    height: 3px;
    background-color: #0069c5;
    transition: width 0.25s ease-out;
}

.underline-hover-effect:hover::before {
    width: 100%;
}

.navbar-collapse {
    display: flex;
    justify-content: flex-end;
}

.schedule-btn {
    background: linear-gradient(180deg, #2d2db0 0%, #2c8cf4 100%);
    box-shadow: 0px 4px 4px 0px #1b51e745;
    font-size: 19px;
    width: fit-content;
}

.nav-items-responsive {
    align-items: center;
}

.header-div>.header-ul { 
    gap: 1.5rem !important;
}

.header-nav>.header-nav-text { 
    font-size: 1.3rem !important;
}

.schedule-nav>.schedule-text { 
    font-size: 1rem !important;
}

@media only screen and (max-width: 991px) {
    .navbar-nav {
        align-items: flex-start !important;
        margin-bottom: 10px;
    }

    .nav-item {
        width: 100%;
        text-decoration: none;
        font-weight: 500;
        color: #525260;
        font-size: 16px;
        background-color: #FFF;
        min-height: 55px;
    }

    .horzontal-line {
        border: 1px solid #999999;
    }

    .nav-items-responsive {
        align-items: flex-start !important;
    }
}

/*------------------------------------------------- NAVBAR CSS Ends --------------------------------------------------------- */

/*------------------------------------------------- FOOTER CSS STARTS --------------------------------------------------------- */
ul {
    list-style-type: none;
    margin: 0;
    padding: 0
}

ul>li {
    padding: 4px;
    color: black;
}

ul>li:hover {
    color: #286fb4;
    cursor: pointer
}

hr {
    border-width: 3px
}

/* .card {
    padding: 2% 7%
} */

.social>i {
    padding: 1%;
    font-size: 15px
}

.social>i:hover {
    color: #286fb4;
    cursor: pointer
}

.heading { 
    color: black
}

.divider {
    border-top: 1px solid #e0e0e2
}

.logo {
    height: 70px;
}

.icon-height {
    height: 30px;
}

.flex {
    display: flex;
    padding-top: 15px;
}

.padding-left {
    padding: 0px 0px 0px 11px;
}

.height {
    height: 30px;

}

.font-size20 {
    font-size: 20px;
}

.footer-heading {
    font-size: inherit;
    line-height: 26.98px;
}

.footer-inner-heading {
    font-size: 22px;
    color: #10559A;
}

.footer-color {
    background: -webkit-linear-gradient(180deg, #3E5AFA 0%, #e94526 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    margin: 0 auto;
    font-size: 4rem;
}

.footer-padding {
    padding-left: 50px;
    padding-right: 10px;
}

.para-PPT {
    color: #483A56;
    text-decoration: none !important;
    font-size: 18px;
}

.para-PPT:hover {
    text-decoration: none !important;
}

.other-Route {
    margin-bottom: 5px;
    cursor: pointer;
    color: #FFF;
}

.other-Route:hover {
    color: #FFF;
}

.other-links {
    margin-bottom: 15px;
    cursor: pointer;
    background-color: #FFF;
    color: #000;
    border-radius: 10px;
    margin-right: 5rem;
    width: max-content;
}

.other-links:hover {
    background: #0044e4;
    color: #FFF;
}

.hover-underline-animation-footer {
    position: relative;
    text-decoration: none;
}

.hover-underline-animation-footer:after {
    content: "";
    position: absolute;
    width: 75%;
    transform: scaleX(0);
    height: 2px;
    bottom: 0;
    left: 0;
    background-color: #FFF;
    transform-origin: bottom right;
    transition: transform 0.25s ease-out;
    text-decoration: none;
}

.hover-underline-animation-footer:hover:after {
    transform: scaleX(1);
    transform-origin: bottom left;
}

.hover-underline-animation-footer:active {
    color: #FFF;
}

@media only screen and (max-width: 991px) {
    .footer-bottom {
        display: flex;
        /* flex-direction: column; */
    }

    .footer-padding {
        padding-left: 0px;
        padding-right: 0px;
    }
}

@media only screen and (max-width: 768px) {
    .footer-flex {
        display: flex;
        flex-direction: column;
    }

    .footer-JC {
        justify-content: flex-start !important;
    }
}

/*------------------------------------------------- FOOTER CSS ENDS --------------------------------------------------------- */