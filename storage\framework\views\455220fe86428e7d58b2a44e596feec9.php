<form action="<?php echo e(route('billingAddress.store')); ?>" method="post">
    <?php echo csrf_field(); ?>

    <div class="row">
        <div class="col-lg-6">
            <div class="mb-3">
                <label class="form-label support-label">Name<span style="color: red;">*</span> </label>

                <input type="text" name="name" class="form-control fieldInput" placeholder="Ex: James Lee"
                    value="<?php echo e(old('name')); ?>" required>
                <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <div class="text-danger"><?php echo e($message); ?></div>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>
            <label class="form-label support-label">GST number</label>
            <div class="mb-3">
                <input type="text" name="gst" class="form-control fieldInput" placeholder="Ex: 22AAAAA0000A1Z5"
                    value="<?php echo e(old('gst')); ?>">
                <?php $__errorArgs = ['gst'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <div class="text-danger"><?php echo e($message); ?></div>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>

            <label class="form-label support-label">Company</label>
            <div class="mb-3">
                <input type="text" name="company" class="form-control fieldInput" placeholder="Ex: XYZ Pvt. Ltd."
                    value="<?php echo e(old('company')); ?>">
                <?php $__errorArgs = ['company'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <div class="text-danger"><?php echo e($message); ?></div>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>

            <label class="form-label support-label">Country</label>
            <div class="mb-3">
                <input type="text" id="addcountry" name="country" class="form-control fieldInput" placeholder="USA"
                    value="<?php echo e(old('country')); ?>">
                <?php $__errorArgs = ['country'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <div class="text-danger"><?php echo e($message); ?></div>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>


        </div>
        <div class="col-lg-6">
            <label class="form-label support-label">PIN/ZIP Code</label>
            <div class="mb-3">
                <input type="text" id="addpincode" name="pinCode" class="form-control fieldInput" placeholder="00038"
                    value="<?php echo e(old('pincode')); ?>">
                <?php $__errorArgs = ['pincode'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <div class="text-danger"><?php echo e($message); ?></div>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>
            <label class="form-label support-label">City</label>
            <div class="mb-3">
                <input type="text" id="addcity" name="city" class="form-control fieldInput" placeholder="Los Angeles"
                    value="<?php echo e(old('city')); ?>">
                <?php $__errorArgs = ['city'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <div class="text-danger"><?php echo e($message); ?></div>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>
            <label class="form-label support-label">State</label>
            <div class="mb-3">
                <input type="text" id="addstate" name="state" class="form-control fieldInput" placeholder="California"
                    value="<?php echo e(old('state')); ?>">
                <?php $__errorArgs = ['state'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <div class="text-danger"><?php echo e($message); ?></div>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>

            <label class="form-label support-label">Billing Address<span class="text-danger">*</span> </label>
            <div class="mb-3">
                <textarea class="form-control fieldInput" name="address" rows="3" required
                    placeholder="House no. 52, California, USA."><?php echo e(old('address')); ?></textarea>
                <?php $__errorArgs = ['address'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <div class="text-danger"><?php echo e($message); ?></div>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>

        </div>
        <div class="col-lg-12">
            <div class="d-flex flex-column flex-lg-row justify-content-between">

                <div class="d-flex align-items-center gap-4 ">
                    <label class="form-label support-label">Make This Address Default: </label>
                    <div class="form-check form-switch">
                        <input class="form-check form-check-input form-select-lg" name="isPrimary" type="checkbox"
                            id="addressDefault" checked>
                    </div>
                </div>
                <div class="align-items-center justify-content-end gap-3 mt-5 d-flex">
                    <button data-bs-dismiss="modal" aria-label="Close"
                        class="rounded-pill ps-4 pe-4 pt-1 pb-1 text-white"
                        style="background-color: #DE0505;border: 2px solid #DE0505;">
                        Cancel
                    </button>

                    <button
                        class="rounded-pill text-white ps-4 pe-4 pt-1 pb-1 d-flex align-items-center gap-2 btn-bg-blue">
                        Add
                        <i class="fa-solid fa-arrow-right text-white"></i>
                    </button>

                </div>
            </div>
        </div>

    </div>

</form>
<?php /**PATH C:\Users\<USER>\Desktop\live\websites_laravel\resources\views/components/core/billingAddressForm.blade.php ENDPATH**/ ?>