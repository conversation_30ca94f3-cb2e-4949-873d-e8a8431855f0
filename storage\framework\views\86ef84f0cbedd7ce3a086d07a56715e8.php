<!DOCTYPE html>
<html>

<head>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@annotorious/annotorious@latest/dist/annotorious.css">
    <script src="https://cdn.jsdelivr.net/npm/@annotorious/annotorious@latest/dist/annotorious.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    <style>
        /* Minimal essential styles - using Bootstrap where possible */
        .annotation-comment {
            position: absolute;
            z-index: 1000;
            pointer-events: auto;
            transform: translateX(-50%);
            max-width: 280px;
        }

        .annotation-comment::before {
            content: '';
            position: absolute;
            top: -8px;
            left: 50%;
            transform: translateX(-50%);
            width: 0;
            height: 0;
            border-left: 8px solid transparent;
            border-right: 8px solid transparent;
            border-bottom: 8px solid rgba(0, 0, 0, 0.9);
        }

        .comment-actions {
            display: none;
            position: absolute;
            top: -30px;
            right: 0;
            z-index: 1002;
        }

        .annotation-comment:hover .comment-actions {
            display: flex;
        }

        .comment-edit-area {
            display: none;
            position: absolute;
            top: 100%;
            left: 50%;
            transform: translateX(-50%);
            z-index: 1001;
            min-width: 200px;
        }

        .image-container {
            position: relative;
        }

        /* Annotation label styles */
        .annotation-label {
            position: absolute;
            background: #007bff;
            color: white;
            border-radius: 50%;
            width: 28px;
            height: 28px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 14px;
            z-index: 1000;
            border: 3px solid white;
            box-shadow: 0 3px 6px rgba(0, 0, 0, 0.4);
            pointer-events: auto;
            cursor: pointer;
            transition: all 0.3s ease;
            padding-left: 6px;
        }

        .annotation-label:hover {
            background: #0056b3;
            transform: scale(1.1);
        }

        .annotation-label.highlighted {
            background: #ff6b6b;
            transform: scale(1.1);
            border-color: #ffeb3b;
            box-shadow: 0 4px 8px rgba(255, 107, 107, 0.5);
        }

        /* Comments list styles */
        .comment-list-item {
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .comment-list-item:hover {
            background-color: #f8f9fa;
            transform: translateX(5px);
        }

        .comment-list-item.highlighted {
            background-color: #e3f2fd;
            border-left: 4px solid #007bff;
        }

        .annotation-badge {
            background: #007bff;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 11px;
        }

        /* Comment box styles */
        .comment-box {
            position: absolute;
            background: white;
            border: 2px solid #007bff;
            border-radius: 8px;
            padding: 12px;
            max-width: 280px;
            min-width: 220px;
            z-index: 1001;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            display: none;
            font-family: inherit;
        }

        .comment-box.show {
            display: block;
        }

        .comment-box::before {
            content: '';
            position: absolute;
            top: -8px;
            left: 20px;
            width: 0;
            height: 0;
            border-left: 8px solid transparent;
            border-right: 8px solid transparent;
            border-bottom: 8px solid #007bff;
        }

        .comment-box-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
            padding-bottom: 5px;
            border-bottom: 1px solid #e9ecef;
        }

        .comment-box-badge {
            background: #007bff;
            color: white;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 12px;
        }

        .comment-box-content {
            font-size: 14px;
            line-height: 1.4;
            color: #333;
            margin-bottom: 8px;
        }

        .comment-box-meta {
            font-size: 11px;
            color: #6c757d;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
    </style>
</head>

<body>
    <div class="container-lg p-3">
        <div class="row">
            <div class="col-md-8">
                <img id="my-image" src="https://images.pexels.com/photos/31241763/pexels-photo-31241763.jpeg"
                    alt="Annotatable image" class="img-fluid" />
            </div>
            <div class="col-md-4">
                <!-- Standalone Comment Box -->


                <!-- Annotation Comments List -->
                <div class="card shadow-sm mb-3">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-map-marker-alt me-2"></i>
                            Annotation Details (<span id="annotation-count">0</span>)
                        </h5>
                    </div>
                    <div class="card-body p-2" style="max-height: 300px; overflow-y: auto;">
                        <div id="annotation-comments-list" class="list-group list-group-flush">
                            <div class="text-muted text-center py-3" id="no-annotation-comments-message">
                                <i class="fas fa-map-marker-alt fa-2x mb-2"></i>
                                <p class="mb-0">No annotation comments yet. Draw on the image to create annotations
                                    with
                                    comments.</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Standalone Comments List -->
                <div class="card shadow-sm mb-3">
                    <div class="card-header bg-warning text-dark">
                        <h5 class="mb-0">
                            <i class="fas fa-sticky-note me-2"></i>
                            Comments (<span id="standalone-count"><?php echo e(count($orderItem->comments)); ?></span>)
                        </h5>
                    </div>
                    <div class="card-body p-2" style="max-height: 300px; overflow-y: auto;">


                        
                        <div id="standalone-comments-list" class="list-group list-group-flush">
                            <?php $__empty_1 = true; $__currentLoopData = $orderItem->comments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $comment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                <?php
                                    if ($comment->byUser) {
                                        $commentBy = $comment->byUser;
                                    } else {
                                        $commentBy = $comment->byLead;
                                    }
                                ?>
                                <div class="list-group-item comment-list-item border-0 p-1 <?php echo e($loop->last ? '' : 'border-bottom'); ?>"
                                    id="standalone-item-<?php echo e($comment->id); ?>">

                                    <div class="d-flex align-items-start ">
                                        <div class="annotation-badge me-2 flex-shrink-0 bg-secondary">
                                            <?php echo e($commentBy->name[0]); ?>


                                        </div>
                                        <div class="flex-grow-1">
                                            <div class="d-flex justify-content-between align-items-center mb-1">
                                                <small class="text-muted fw-bold"><?php echo e($commentBy->name); ?></small>
                                                <small class="text-muted"><?php echo e($comment->created_at); ?></small>
                                            </div>
                                            <div class="text-dark"><?php echo e($comment->comment); ?></div>
                                        </div>
                                    </div>

                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                
                                <div class="text-muted text-center py-3" id="no-standalone-comments-message">
                                    <i class="fas fa-sticky-note fa-2x mb-2"></i>
                                    <p class="mb-0">No standalone comments yet. Use the comment box to add notes.
                                    </p>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <div class="card-footer bg-white ">
                        <div class="mb-3">
                            <textarea class="form-control" id="standalone-comment" placeholder="Write a comment without annotation..."
                                rows="3"></textarea>
                        </div>
                        <div class="d-flex gap-2">
                            <button class="btn btn-success btn-sm" onclick="saveStandaloneComment()">
                                <i class="fas fa-save me-1"></i>Save Comment
                            </button>
                            <button class="btn btn-secondary btn-sm" onclick="clearStandaloneComment()">
                                <i class="fas fa-times me-1"></i>Clear
                            </button>
                        </div>
                    </div>
                </div>
                <!-- Instructions -->
                <div class="card shadow-sm">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-info-circle me-2"></i>
                            Instructions
                        </h5>
                    </div>
                    <div class="card-body p-3">

                        <div class="d-grid gap-2 mt-3">
                            <button class="btn btn-outline-danger btn-sm" onclick="clearAllComments()">
                                <i class="fas fa-trash-alt me-2"></i>Clear All Annotations
                            </button>
                            <button class="btn btn-outline-info btn-sm" onclick="showDataStructure()">
                                <i class="fas fa-eye me-2"></i>Show Data Structure
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script>
        /*
                                                                                                ========================================
                                                                                                API INTEGRATION GUIDE
                                                                                                ========================================

                                                                                                This application currently uses localStorage for data persistence.
                                                                                                To integrate with a backend API (Laravel/PHP), replace the localStorage
                                                                                                functions with the corresponding API calls shown in the comments.

                                                                                                REQUIRED API ENDPOINTS:

                                                                                                1. INTEGRATED ANNOTATIONS API:
                                                                                                   - POST   /api/annotations           (Create/Update annotation with comment)
                                                                                                   - GET    /api/annotations           (Fetch all annotations with comments)
                                                                                                   - GET    /api/annotations/{id}      (Fetch single annotation with comment)
                                                                                                   - DELETE /api/annotations/{id}      (Delete annotation with comment)
                                                                                                   - DELETE /api/annotations/clear-all (Clear all data)

                                                                                                EXPECTED DATA STRUCTURE FOR SINGLE TABLE (Laravel Backend):

                                                                                                Database Table: annotations
                                                                                                Fields: (id, graphic_design_order_item_id, datetime, comment, annotationJson, user_id, lead_id, annotationId)

                                                                                                Frontend Request Format:
                                                                                                {
                                                                                                    "graphic_design_order_item_id": 1,
                                                                                                    "comment": "asdasd",
                                                                                                    "user_id": 57289,
                                                                                                    "lead_id": 123,
                                                                                                    "annotationId": "fb98c481-ed71-4987-96b5-33a0c91ca063",
                                                                                                    "annotationJson": {
                                                                                                        "id": "fb98c481-ed71-4987-96b5-33a0c91ca063",
                                                                                                        "bodies": [],
                                                                                                        "target": {
                                                                                                            "annotation": "fb98c481-ed71-4987-96b5-33a0c91ca063",
                                                                                                            "selector": {
                                                                                                                "type": "RECTANGLE",
                                                                                                                "geometry": {
                                                                                                                    "bounds": {
                                                                                                                        "minX": 108.34375,
                                                                                                                        "minY": 12,
                                                                                                                        "maxX": 177.34375,
                                                                                                                        "maxY": 113
                                                                                                                    },
                                                                                                                    "x": 108.34375,
                                                                                                                    "y": 12,
                                                                                                                    "w": 69,
                                                                                                                    "h": 101
                                                                                                                }
                                                                                                            },
                                                                                                            "creator": {
                                                                                                                "id": "57289",
                                                                                                                "name": "sourabh"
                                                                                                            },
                                                                                                            "created": "2025-07-10T05:53:51.868Z"
                                                                                                        }
                                                                                                    }
                                                                                                }

                                                                                                Backend Response Format:
                                                                                                {
                                                                                                    "id": 1,
                                                                                                    "graphic_design_order_item_id": 1,
                                                                                                    "datetime": "2025-07-10T05:53:51.868Z",
                                                                                                    "comment": "asdasd",
                                                                                                    "annotationJson": { ... }, // Full annotation object
                                                                                                    "user_id": 57289,
                                                                                                    "lead_id": 123,
                                                                                                    "annotationId": "fb98c481-ed71-4987-96b5-33a0c91ca063"
                                                                                                }

                                                                                                CSRF TOKEN:
                                                                                                Make sure to include CSRF token in your HTML head:
                                                                                                <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">

                                                                                                ========================================
                                                                                                */

        // Global variables for annotation labeling
        window.annotationLabels = new Map(); // Map annotation IDs to labels
        window.labelCounter = 0; // Counter for generating labels
        window.standaloneComments = []; // Array to store standalone comments

        // Function to generate next available label
        function getNextLabel() {
            return (++window.labelCounter).toString();
        }

        // Function to assign label to annotation
        function assignLabelToAnnotation(annotationId) {
            if (!window.annotationLabels.has(annotationId)) {
                const label = getNextLabel();
                window.annotationLabels.set(annotationId, label);
                return label;
            }
            return window.annotationLabels.get(annotationId);
        }

        // Function to get label for annotation
        function getLabelForAnnotation(annotationId) {
            return window.annotationLabels.get(annotationId) || '';
        }

        // Function to add visual label to annotation on image
        function addAnnotationLabel(annotation) {
            const label = getLabelForAnnotation(annotation.id);
            if (!label) return;

            const labelId = `label-${annotation.id}`;

            // Remove existing label if any
            const existingLabel = document.getElementById(labelId);
            if (existingLabel) {
                existingLabel.remove();
            }

            // Get annotation bounds and image element
            const bounds = annotation.target.selector.geometry.bounds;
            const imageElement = document.getElementById('my-image');
            const imageContainer = imageElement.parentElement;

            // Calculate position relative to the container
            const imageRect = imageElement.getBoundingClientRect();
            const scaleX = imageRect.width / imageElement.naturalWidth;
            const scaleY = imageRect.height / imageElement.naturalHeight;

            const labelX = bounds.minX * scaleX - 14; // Offset to position at top-left corner
            const labelY = bounds.minY * scaleY - 14;

            // Create label element
            const labelElement = document.createElement('div');
            labelElement.id = labelId;
            labelElement.className = 'annotation-label';
            labelElement.style.cssText = `
                left: ${labelX}px;
                top: ${labelY}px;
                display: block;
            `;
            labelElement.textContent = label;
            labelElement.title = `Annotation #${label} - Click to highlight comment`;

            // Add click handler to highlight annotation and comment
            labelElement.addEventListener('click', function(e) {
                e.stopPropagation();
                highlightAnnotationOnImage(annotation.id);
                highlightCommentInList(annotation.id);
                console.log(`Clicked annotation label ${label} for annotation ${annotation.id}`);
            });

            // Add to image container
            imageContainer.appendChild(labelElement);

            console.log(`Added label ${label} for annotation ${annotation.id} at position (${labelX}, ${labelY})`);
        }

        // Function to remove annotation label
        function removeAnnotationLabel(annotationId) {
            const labelId = `label-${annotationId}`;
            const labelElement = document.getElementById(labelId);
            if (labelElement) {
                labelElement.remove();
            }
        }

        // Function to update both comments lists
        function updateCommentsList() {
            updateAnnotationCommentsList();
            updateStandaloneCommentsList();
        }

        function updateAnnotationCommentsList() {
            const annotationCommentsList = document.getElementById('annotation-comments-list');
            const noAnnotationCommentsMessage = document.getElementById('no-annotation-comments-message');
            const annotationCount = document.getElementById('annotation-count');

            // Get all annotations from storage
            const annotations = JSON.parse(localStorage.getItem('annotorious-annotations') || '[]');

            // Clear existing list
            annotationCommentsList.innerHTML = '';

            if (annotations.length === 0) {
                if (noAnnotationCommentsMessage) {
                    annotationCommentsList.appendChild(noAnnotationCommentsMessage);
                }
                annotationCount.textContent = '0';
                return;
            }

            // Hide no comments message
            if (noAnnotationCommentsMessage && noAnnotationCommentsMessage.parentNode) {
                noAnnotationCommentsMessage.remove();
            }

            // Sort annotations by creation time
            annotations.sort((a, b) => new Date(a.datetime) - new Date(b.datetime));

            // Create list items for each annotation
            annotations.forEach((annotation, index) => {
                const label = getLabelForAnnotation(annotation.annotationId);
                const commentText = annotation.comment || 'No comment';
                const userName = annotation.annotationJson?.target?.creator?.name || 'Unknown User';
                const timeAgo = getTimeAgo(annotation.datetime);

                const listItem = document.createElement('div');
                listItem.className = 'list-group-item comment-list-item border-0 border-bottom';
                listItem.id = `comment-item-${annotation.annotationId}`;
                listItem.innerHTML = `
                    <div class="d-flex align-items-start">
                        <div class="annotation-badge me-2 flex-shrink-0">${label}</div>
                        <div class="flex-grow-1">
                            <div class="d-flex justify-content-between align-items-center mb-1">
                                <small class="text-muted fw-bold">${userName}</small>
                                <small class="text-muted">${timeAgo}</small>
                            </div>
                            <div class="text-dark">${commentText}</div>
                        </div>
                    </div>
                `;

                // Add click handler to highlight annotation
                listItem.addEventListener('click', function() {
                    highlightAnnotationOnImage(annotation.annotationId);
                    highlightCommentInList(annotation.annotationId);
                });

                annotationCommentsList.appendChild(listItem);
            });

            // Update count
            annotationCount.textContent = annotations.length.toString();
        }

        // Function to update standalone comments list
        function updateStandaloneCommentsList() {
            const standaloneCommentsList = document.getElementById('standalone-comments-list');
            const noStandaloneCommentsMessage = document.getElementById('no-standalone-comments-message');
            const standaloneCount = document.getElementById('standalone-count');

            // Get all standalone comments from storage
            const standaloneComments = JSON.parse(localStorage.getItem('standalone-comments') || '[]');

            // Clear existing list
            standaloneCommentsList.innerHTML = '';

            if (standaloneComments.length === 0) {
                if (noStandaloneCommentsMessage) {
                    standaloneCommentsList.appendChild(noStandaloneCommentsMessage);
                }
                standaloneCount.textContent = '0';
                return;
            }

            // Hide no comments message
            if (noStandaloneCommentsMessage && noStandaloneCommentsMessage.parentNode) {
                noStandaloneCommentsMessage.remove();
            }

            // Sort standalone comments by creation time
            standaloneComments.sort((a, b) => new Date(a.datetime) - new Date(b.datetime));

            // Create list items for each standalone comment
            standaloneComments.forEach((comment, index) => {
                const commentText = comment.comment;
                const userName = comment.user_name || 'Unknown User';
                const timeAgo = getTimeAgo(comment.datetime);

                const listItem = document.createElement('div');
                listItem.className = 'list-group-item comment-list-item border-0 border-bottom bg-light';
                listItem.id = `standalone-item-${comment.id}`;
                listItem.innerHTML = `
                    <div class="d-flex align-items-start">
                        <div class="annotation-badge me-2 flex-shrink-0 bg-secondary">${userName[0]}</div>
                        <div class="flex-grow-1">
                            <div class="d-flex justify-content-between align-items-center mb-1">
                                <small class="text-muted fw-bold">${userName}</small>
                                <small class="text-muted">${timeAgo}</small>
                            </div>
                            <div class="text-dark">${commentText}</div>
                        </div>
                      
                    </div>
                `;

                standaloneCommentsList.appendChild(listItem);
            });

            // Update count
            standaloneCount.textContent = standaloneComments.length.toString();
        }


        // Function to highlight comment in list
        function highlightCommentInList(annotationId) {
            // Remove existing highlights
            document.querySelectorAll('.comment-list-item').forEach(item => {
                item.classList.remove('highlighted');
            });

            // Highlight the selected comment
            const commentItem = document.getElementById(`comment-item-${annotationId}`);
            if (commentItem) {
                commentItem.classList.add('highlighted');
                commentItem.scrollIntoView({
                    behavior: 'smooth',
                    block: 'nearest'
                });
            }
        }

        // Function to highlight annotation label
        function highlightAnnotationLabel(annotationId) {
            // Remove existing highlights
            document.querySelectorAll('.annotation-label').forEach(label => {
                label.classList.remove('highlighted');
            });

            // Highlight the selected label
            const labelElement = document.getElementById(`label-${annotationId}`);
            if (labelElement) {
                labelElement.classList.add('highlighted');
            }
        }

        // Standalone comment functions
        function saveStandaloneComment() {
            const textarea = document.getElementById('standalone-comment');
            const commentText = textarea.value.trim();

            if (!commentText) {
                alert('Please enter a comment before saving.');
                return;
            }

            // Create standalone comment object
            const standaloneComment = {
                graphic_design_order_item_id: 1,
                id: 'standalone-' + Date.now(),
                type: 'standalone',
                comment: commentText,
                user_name: 'sourabh', // Replace with actual user
                datetime: new Date().toISOString(),
                tag: 'Note' // Special tag for standalone comments
            };

            /////////////////////////////

            fetch('/annotations', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify(standaloneComment)
                })
                .then(response => response.json())
                .then(data => {
                    console.log('Updating standalone-comments ', data);


                    let standaloneComments = JSON.parse(localStorage.getItem('standalone-comments') || '[]');
                    standaloneComments.push(standaloneComment);
                    localStorage.setItem('standalone-comments', JSON.stringify(standaloneComments));

                    // Clear the textarea
                    textarea.value = '';

                    // Update the comments list
                    updateCommentsList();

                    console.log('Saved standalone comment:', standaloneComment);
                })
                .catch(error => {
                    console.error('Error saving annotation with integrated comment:', error);
                    // Fallback to localStorage on error
                });

        }

        function clearStandaloneComment() {
            const textarea = document.getElementById('standalone-comment');
            textarea.value = '';
        }

        function loadStandaloneComments() {
            return JSON.parse(localStorage.getItem('standalone-comments') || '[]');
        }

        function removeStandaloneComment(commentId) {
            let standaloneComments = JSON.parse(localStorage.getItem('standalone-comments') || '[]');
            standaloneComments = standaloneComments.filter(comment => comment.id !== commentId);
            localStorage.setItem('standalone-comments', JSON.stringify(standaloneComments));
            updateCommentsList();
        }

        // Simplified annotation with integrated comment management
        function getCommentFromStorage(annotationId) {
            // TODO: REPLACE WITH API CALL - FETCH ANNOTATION WITH COMMENT
            /*
            DEMO API CALL FOR FETCHING ANNOTATION WITH COMMENT:

            fetch(`/api/annotations/${annotationId}`)
            .then(response => response.json())
            .then(data => {
                console.log('Annotation with comment fetched from database:', data);
                return {
                    comment: data.comment || '',
                    user: data.annotationJson?.target?.creator?.name || '<?php echo e(auth()->user()->name); ?>',
                    timestamp: data.datetime || new Date().toISOString(),
                    id: data.annotationId
                };
            })
            .catch(error => {
                console.error('Error fetching annotation with comment:', error);
                return null;
            });
            */

            // CURRENT LOCALSTORAGE IMPLEMENTATION (REPLACE WITH API CALL ABOVE)
            let annotations = JSON.parse(localStorage.getItem('annotorious-annotations') || '[]');
            const annotation = annotations.find(a => a.annotationId === annotationId);

            if (annotation && annotation.comment) {
                return {
                    comment: annotation.comment,
                    user: annotation.annotationJson?.target?.creator?.name || '<?php echo e(auth()->user()->name); ?>',
                    timestamp: annotation.datetime || new Date().toISOString(),
                    id: annotation.annotationId
                };
            }
            return null;
        }

        // Helper function to format time ago
        function getTimeAgo(timestamp) {
            if (!timestamp) return 'Unknown time';

            const now = new Date();
            const commentTime = new Date(timestamp);
            const diffInSeconds = Math.floor((now - commentTime) / 1000);

            if (diffInSeconds < 60) return 'Just now';
            if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;
            if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;
            if (diffInSeconds < 2592000) return `${Math.floor(diffInSeconds / 86400)}d ago`;

            return commentTime.toLocaleDateString();
        }

        function saveAnnotationToStorage(annotation, commentText = '', userName = 'sourabh') {
            console.log('annotation', annotation);



            // CURRENT LOCALSTORAGE IMPLEMENTATION (REPLACE WITH API CALL ABOVE)
            let annotations = JSON.parse(localStorage.getItem('annotorious-annotations') || '[]');
            console.log('annotations', annotations);


            // Remove existing annotation with same ID if it exists
            annotations = annotations.filter(a => a.annotationId !== annotation.id);

            // Assign label to annotation if it doesn't have one
            const label = assignLabelToAnnotation(annotation.id);

            // Create annotation object matching your single table structure
            const integratedAnnotation = {
                graphic_design_order_item_id: 1, // Replace with actual item ID
                datetime: new Date().toISOString(),
                comment: commentText,
                user_id: 57289, // Replace with actual user ID
                lead_id: 123, // Replace with actual lead ID
                annotationId: annotation.id,
                annotationJson: annotation,
                tag: label // Add the label as tag
            };
            /////////////////////////////////////////////
            fetch('/annotations', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify(integratedAnnotation)
                })
                .then(response => response.json())
                .then(data => {
                    let annotations = JSON.parse(localStorage.getItem('annotorious-annotations') || '[]');

                    annotations.push(integratedAnnotation);
                    localStorage.setItem('annotorious-annotations', JSON.stringify(annotations));
                    console.log('Saved integrated annotation with label:', integratedAnnotation);
                    updateCommentsList();
                })
                .catch(error => {
                    console.error('Error saving annotation with integrated comment:', error);
                    // Fallback to localStorage on error
                });
            //////////////////////////////////////////////






            // Update the comments list
        }

        function loadAnnotationsFromStorage() {
            // TODO: REPLACE WITH API CALL - FETCH ALL ANNOTATIONS WITH INTEGRATED COMMENTS
            /*
            DEMO API CALL FOR LOADING ALL ANNOTATIONS WITH INTEGRATED COMMENTS:

            fetch('/api/annotations')
            .then(response => response.json())
            .then(data => {
                console.log('All annotations with integrated comments fetched from database:', data);
                // Update local storage as backup
                localStorage.setItem('annotorious-annotations', JSON.stringify(data));
                // Return only the annotation objects for Annotorious
                return data.map(item => item.annotation);
            })
            .catch(error => {
                console.error('Error fetching annotations with integrated comments:', error);
                // Fallback to localStorage on error
                let annotations = JSON.parse(localStorage.getItem('annotorious-annotations') || '[]');
                return annotations.map(item => item.annotation || item);
            });
            */

            // CURRENT LOCALSTORAGE IMPLEMENTATION (REPLACE WITH API CALL ABOVE)
            let annotations = JSON.parse(localStorage.getItem('annotorious-annotations') || '[]');
            console.log('annotations Loaded', annotations);

            // Restore labels from stored annotations
            annotations.forEach(item => {
                if (item.tag && item.annotationId) {
                    window.annotationLabels.set(item.annotationId, item.tag);
                    // Update label counter to ensure no duplicates
                    const labelNumber = parseInt(item.tag);
                    if (!isNaN(labelNumber) && labelNumber >= window.labelCounter) {
                        window.labelCounter = labelNumber;
                    }
                }
            });

            // Update comments list
            updateCommentsList();

            // Return only the annotation objects for Annotorious
            return annotations.map(item => item.annotationJson || item);
        }

        function removeAnnotationFromStorage(annotationId) {
            // TODO: REPLACE WITH API CALL - DELETE ANNOTATION WITH INTEGRATED COMMENT
            /*
            DEMO API CALL FOR DELETING ANNOTATION WITH INTEGRATED COMMENT:

            fetch(`/api/annotations/${annotationId}`, {
                method: 'DELETE',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => response.json())
            .then(data => {
                console.log('Annotation with integrated comment deleted from database:', data);
                // Remove from local storage as well
                let annotations = JSON.parse(localStorage.getItem('annotorious-annotations') || '[]');
                annotations = annotations.filter(a => a.annotation_id !== annotationId);
                localStorage.setItem('annotorious-annotations', JSON.stringify(annotations));
            })
            .catch(error => {
                console.error('Error deleting annotation with integrated comment:', error);
                // Fallback to localStorage on error
            });
            */

            // CURRENT LOCALSTORAGE IMPLEMENTATION (REPLACE WITH API CALL ABOVE)
            let annotations = JSON.parse(localStorage.getItem('annotorious-annotations') || '[]');
            annotations = annotations.filter(a => a.annotationId !== annotationId);
            localStorage.setItem('annotorious-annotations', JSON.stringify(annotations));

            // Remove label mapping
            window.annotationLabels.delete(annotationId);

            // Remove visual label
            removeAnnotationLabel(annotationId);

            // Update comments list
            updateCommentsList();
        }

        // Function to highlight annotation on the image
        function highlightAnnotationOnImage(annotationId) {
            if (!window.annotoriusInstance) return;

            const annotation = window.annotoriusInstance.getAnnotations().find(a => a.id === annotationId);
            if (annotation) {
                // First, clear any existing selections
                window.annotoriusInstance.clearSelection();

                // Select the annotation to highlight it
                window.annotoriusInstance.setSelected(annotation);

                // Add custom styling for enhanced visibility
                window.annotoriusInstance.setStyle((ann, state) => {
                    if (ann.id === annotationId && state.selected) {
                        return {
                            fill: '#ff6b6b',
                            fillOpacity: 0.3,
                            stroke: '#ff6b6b',
                            strokeWidth: 3,
                            strokeOpacity: 1
                        };
                    }
                    // Default style for other annotations
                    return {
                        fill: '#007bff',
                        fillOpacity: 0.2,
                        stroke: '#007bff',
                        strokeWidth: 2,
                        strokeOpacity: 0.8
                    };
                });

                console.log('Annotation highlighted on image:', annotationId);

                // Also highlight the label and comment in list
                highlightAnnotationLabel(annotationId);
                highlightCommentInList(annotationId);
            }
        }

        // Function to add comment box display for annotation
        function addAnnotationComment(annotation) {
            // Get comment from integrated storage
            const commentData = getCommentFromStorage(annotation.id);
            const commentText = commentData ? commentData.comment : '';

            console.log('Adding comment box for annotation:', annotation.id, 'Text:', commentText);

            // Create a comment box element
            const commentBoxId = `comment-box-${annotation.id}`;

            // Remove existing comment box if any
            const existingCommentBox = document.getElementById(commentBoxId);
            if (existingCommentBox) {
                existingCommentBox.remove();
            }

            // Get annotation bounds and image element
            const bounds = annotation.target.selector.geometry.bounds;
            const imageElement = document.getElementById('my-image');

            // Get image dimensions and position
            const imageRect = imageElement.getBoundingClientRect();
            const imageContainer = imageElement.parentElement;

            // Calculate position relative to the container
            const scaleX = imageRect.width / imageElement.naturalWidth;
            const scaleY = imageRect.height / imageElement.naturalHeight;

            const commentX = (bounds.minX + (bounds.maxX - bounds.minX) / 2) * scaleX - 110; // Center the box
            const commentY = bounds.maxY * scaleY + 15; // 15px below the annotation

            // Create comment box element
            const commentBoxElement = document.createElement('div');
            commentBoxElement.id = commentBoxId;
            commentBoxElement.className = 'comment-box';
            commentBoxElement.style.cssText = `
                left: ${commentX}px;
                top: ${commentY}px;
                display: none;
            `;

            // Get user info and format time
            const userName = commentData ? commentData.user || 'sourabh' : 'sourabh';
            const timeAgo = commentData ? getTimeAgo(commentData.timestamp) : 'Just now';
            const label = getLabelForAnnotation(annotation.id);

            // Create comment box content structure
            commentBoxElement.innerHTML = `
                <div class="comment-box-header">
                    <div class="d-flex align-items-center">
                        <div class="comment-box-badge">${label}</div>
                        <span class="ms-2 fw-bold text-primary">Annotation ${label}</span>
                    </div>
                </div>
                <div class="comment-box-content">
                    ${commentText.trim() || 'No comment added'}
                </div>
                <div class="comment-box-meta">
                    <span class="fw-bold">${userName}</span>
                    <span>${timeAgo}</span>
                </div>
            `;

            // Add hover event listeners to keep comment box visible when hovering over it
            commentBoxElement.addEventListener('mouseenter', function() {
                commentBoxElement.style.display = 'block';
            });

            commentBoxElement.addEventListener('mouseleave', function() {
                // Check if annotation is selected
                const isSelected = window.selectedAnnotations &&
                    window.selectedAnnotations.some(selected => selected.id === annotation.id);

                // Only hide if not selected
                if (!isSelected) {
                    commentBoxElement.style.display = 'none';
                }
            });

            // Add to image container
            imageContainer.classList.add('image-container');
            imageContainer.appendChild(commentBoxElement);

            // Also add the visual label
            addAnnotationLabel(annotation);
        }

        // Function to show comment input box for new annotation
        function showCommentInputBox(annotation) {
            const bounds = annotation.target.selector.geometry.bounds;
            const imageElement = document.getElementById('my-image');
            const imageContainer = imageElement.parentElement;

            // Calculate position
            const scaleX = imageElement.getBoundingClientRect().width / imageElement.naturalWidth;
            const scaleY = imageElement.getBoundingClientRect().height / imageElement.naturalHeight;

            const commentX = (bounds.minX + (bounds.maxX - bounds.minX) / 2) * scaleX;
            const commentY = bounds.maxY * scaleY + 10;

            // Create comment input box
            const inputBoxId = `comment-input-${annotation.id}`;
            const inputBox = document.createElement('div');
            inputBox.id = inputBoxId;
            inputBox.className = 'bg-white border border-primary rounded p-2 shadow';
            inputBox.style.cssText = `
                position: absolute;
                left: ${commentX}px;
                top: ${commentY}px;
                transform: translateX(-50%);
                z-index: 1001;
                min-width: 200px;
            `;

            inputBox.innerHTML = `
                <textarea class="form-control form-control-sm mb-2" id="new-comment-${annotation.id}" placeholder="Add a comment..."
                          onkeydown="handleNewCommentKeydown(event, '${annotation.id}')" rows="3" autofocus></textarea>
                <div class="d-flex gap-1 justify-content-end">
                    <button class="btn btn-success btn-sm" onclick="saveNewComment('${annotation.id}')">Save</button>
                    <button class="btn btn-secondary btn-sm" onclick="cancelNewComment('${annotation.id}')">Cancel</button>
                </div>
            `;

            imageContainer.appendChild(inputBox);

            // Focus the textarea
            setTimeout(() => {
                const textarea = document.getElementById(`new-comment-${annotation.id}`);
                if (textarea) {
                    textarea.focus();
                }
            }, 100);

            // Store reference to current editing annotation
            window.currentEditingAnnotation = annotation;
        }

        // Function to remove annotation comment box
        function removeAnnotationComment(annotationId) {
            const commentBoxId = `comment-box-${annotationId}`;
            const commentBoxElement = document.getElementById(commentBoxId);
            if (commentBoxElement) {
                commentBoxElement.remove();
            }
        }

        // Function to show comment box
        function showComment(annotationId) {
            const commentBoxElement = document.getElementById(`comment-box-${annotationId}`);
            if (commentBoxElement) {
                commentBoxElement.classList.add('show');
                commentBoxElement.style.display = 'block';
            }
        }

        // Function to hide comment box
        function hideComment(annotationId) {
            const commentBoxElement = document.getElementById(`comment-box-${annotationId}`);
            if (commentBoxElement) {
                commentBoxElement.classList.remove('show');
                commentBoxElement.style.display = 'none';
            }
        }

        // Function to hide all comment boxes
        function hideAllComments() {
            const commentBoxElements = document.querySelectorAll('.comment-box');
            commentBoxElements.forEach(element => {
                element.classList.remove('show');
                element.style.display = 'none';
            });
        }

        // Function to edit annotation comment
        function editAnnotationComment(annotationId) {
            const commentElement = document.getElementById(`comment-${annotationId}`);
            const editArea = document.getElementById(`edit-area-${annotationId}`);
            const editInput = document.getElementById(`edit-input-${annotationId}`);

            if (commentElement && editArea && editInput) {
                // Ensure comment is visible during editing
                commentElement.style.display = 'block';

                // Get current comment from storage
                const commentData = getCommentFromStorage(annotationId);
                const currentText = commentData ? commentData.comment : '';
                editInput.value = currentText;

                editArea.style.display = 'block';
                editInput.focus();
                editInput.select();
            }
        }

        // Function to cancel editing annotation comment
        function cancelEditAnnotationComment(annotationId) {
            const editArea = document.getElementById(`edit-area-${annotationId}`);
            const editInput = document.getElementById(`edit-input-${annotationId}`);

            if (editArea && editInput) {
                editArea.style.display = 'none';
                // Reset input to original value from storage
                const commentData = getCommentFromStorage(annotationId);
                const originalText = commentData ? commentData.comment : '';
                editInput.value = originalText;
            }
        }

        // Function to save annotation comment
        function saveAnnotationComment(annotationId) {
            const editInput = document.getElementById(`edit-input-${annotationId}`);
            const editArea = document.getElementById(`edit-area-${annotationId}`);
            const commentTextElement = document.querySelector(`#comment-${annotationId} .comment-text`);

            if (!editInput || !editArea || !commentTextElement) return;

            const newText = editInput.value.trim();

            // Get existing comment data to preserve user info
            const existingComment = getCommentFromStorage(annotationId);
            const userName = existingComment ? existingComment.user : 'sourabh';

            // Get the annotation object and save with integrated comment
            const annotation = window.annotoriusInstance.getAnnotations().find(a => a.id === annotationId);
            if (annotation) {
                saveAnnotationToStorage(annotation, newText, userName);
            }

            // Update the display
            commentTextElement.textContent = newText || 'No comment';
            editArea.style.display = 'none';

            // Refresh the comment display to show updated time
            if (annotation) {
                addAnnotationComment(annotation);
            }
        }

        // Function to delete annotation comment (and annotation)
        function deleteAnnotationComment(annotationId) {
            if (confirm('Are you sure you want to delete this annotation and its comment?')) {
                // Remove from Annotorious
                if (window.annotoriusInstance) {
                    const annotation = window.annotoriusInstance.getAnnotations().find(a => a.id === annotationId);
                    if (annotation) {
                        window.annotoriusInstance.removeAnnotation(annotation);
                    }
                }

                // Remove from localStorage (integrated structure)
                removeAnnotationFromStorage(annotationId);

                // Remove the comment display
                removeAnnotationComment(annotationId);
            }
        }

        // Function to save new comment
        function saveNewComment(annotationId) {
            const textarea = document.getElementById(`new-comment-${annotationId}`);
            const inputBox = document.getElementById(`comment-input-${annotationId}`);

            if (textarea && inputBox) {
                const commentText = textarea.value.trim();

                if (commentText) {
                    // Use default user name
                    const currentUser = 'sourabh';

                    // Assign label to annotation
                    assignLabelToAnnotation(window.currentEditingAnnotation.id);

                    // Save annotation with integrated comment to storage
                    if (window.currentEditingAnnotation) {
                        saveAnnotationToStorage(window.currentEditingAnnotation, commentText, currentUser);
                    }

                    // Add comment display and label
                    if (window.currentEditingAnnotation) {
                        addAnnotationComment(window.currentEditingAnnotation);
                        addAnnotationLabel(window.currentEditingAnnotation);
                    }
                } else {
                    // No comment text, delete the annotation
                    if (window.annotoriusInstance && window.currentEditingAnnotation) {
                        window.annotoriusInstance.removeAnnotation(window.currentEditingAnnotation);
                        console.log('Deleted annotation due to empty comment:', window.currentEditingAnnotation.id);
                    }
                }

                // Remove input box after processing
                if (inputBox && inputBox.parentNode) {
                    inputBox.remove();
                }

                // Clear the current editing annotation
                window.currentEditingAnnotation = null;
            }
        }

        // Function to cancel new comment (and delete annotation)
        function cancelNewComment(annotationId) {
            const inputBox = document.getElementById(`comment-input-${annotationId}`);

            if (inputBox) {
                inputBox.remove();
            }

            // Delete the annotation since no comment was added
            if (window.annotoriusInstance && window.currentEditingAnnotation) {
                window.annotoriusInstance.removeAnnotation(window.currentEditingAnnotation);
            }

            window.currentEditingAnnotation = null;
        }

        // Function to handle keyboard events for new comment
        function handleNewCommentKeydown(event, annotationId) {
            if (event.key === 'Enter' && event.ctrlKey) {
                // Ctrl+Enter to save
                event.preventDefault();
                saveNewComment(annotationId);
            } else if (event.key === 'Escape') {
                // Escape to cancel
                event.preventDefault();
                cancelNewComment(annotationId);
            }
        }

        // Function to handle keyboard events in edit mode
        function handleEditKeydown(event, annotationId) {
            if (event.key === 'Enter' && event.ctrlKey) {
                // Ctrl+Enter to save
                event.preventDefault();
                saveAnnotationComment(annotationId);
            } else if (event.key === 'Escape') {
                // Escape to cancel
                event.preventDefault();
                cancelEditAnnotationComment(annotationId);
            }
        }

        function clearAllComments() {
            if (confirm(
                    'Are you sure you want to clear all annotations and standalone comments? This action cannot be undone.'
                )) {
                // TODO: REPLACE WITH API CALL - DELETE ALL ANNOTATIONS WITH INTEGRATED COMMENTS
                /*
                DEMO API CALL FOR CLEARING ALL DATA:

                fetch('/api/annotations/clear-all', {
                    method: 'DELETE',
                    headers: {
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                })
                .then(response => response.json())
                .then(data => {
                    console.log('All annotations with integrated comments cleared from database:', data);
                    // Clear local storage as well
                    localStorage.removeItem('annotorious-annotations');

                    // Clear all annotations from the map
                    if (window.annotoriusInstance) {
                        const annotations = window.annotoriusInstance.getAnnotations();
                        annotations.forEach(annotation => {
                            window.annotoriusInstance.removeAnnotation(annotation);
                        });
                    }

                    // Remove all comment elements from the page
                    const commentElements = document.querySelectorAll('.annotation-comment, .comment-input-box');
                    commentElements.forEach(element => {
                        element.remove();
                    });
                })
                .catch(error => {
                    console.error('Error clearing all data:', error);
                    // Fallback to localStorage on error
                });
                */

                // CURRENT LOCALSTORAGE IMPLEMENTATION (REPLACE WITH API CALL ABOVE)
                localStorage.removeItem('annotorious-annotations');
                localStorage.removeItem('standalone-comments');

                // Reset labeling system
                window.annotationLabels.clear();
                window.labelCounter = 0;

                // Clear all annotations from the map
                if (window.annotoriusInstance) {
                    const annotations = window.annotoriusInstance.getAnnotations();
                    annotations.forEach(annotation => {
                        window.annotoriusInstance.removeAnnotation(annotation);
                    });
                }

                // Remove all comment boxes, input boxes and labels from the page
                const commentElements = document.querySelectorAll('.comment-box, .comment-input-box, .annotation-label');
                commentElements.forEach(element => {
                    element.remove();
                });

                // Update comments list
                updateCommentsList();
            }
        }

        // Function to show current data structure for testing
        function showDataStructure() {
            const annotations = JSON.parse(localStorage.getItem('annotorious-annotations') || '[]');
            console.log('=== CURRENT INTEGRATED DATA STRUCTURE ===');
            console.log('Total annotations:', annotations.length);

            annotations.forEach((item, index) => {
                console.log(`\n--- Annotation ${index + 1} ---`);
                console.log('Integrated Object (matches your desired format):', item);

                // Show the exact structure for single table
                const formattedStructure = {
                    id: item.id,
                    graphic_design_order_item_id: item.graphic_design_order_item_id,
                    datetime: item.datetime,
                    comment: item.comment,
                    annotationJson: item.annotationJson,
                    user_id: item.user_id,
                    lead_id: item.lead_id,
                    annotationId: item.annotationId
                };
                console.log('Formatted structure:', formattedStructure);
            });

            if (annotations.length === 0) {
                console.log('No annotations found. Create some annotations with comments to see the integrated structure.');
            }

            alert(
                `Check the browser console to see the integrated data structure. Found ${annotations.length} annotations.`
            );
        }

        function loadExistingAnnotations() {
            if (!window.annotoriusInstance) return;

            // TODO: REPLACE WITH API CALL - LOAD ANNOTATIONS AND COMMENTS ON PAGE LOAD
            /*
            DEMO API CALL FOR LOADING EXISTING DATA:

            Promise.all([
                fetch('/api/annotations').then(response => response.json()),
                fetch('/api/comments').then(response => response.json())
            ])
            .then(([annotationsData, commentsData]) => {
                console.log('Loaded annotations from database:', annotationsData);
                console.log('Loaded comments from database:', commentsData);

                // Update local storage as backup
                localStorage.setItem('annotorious-annotations', JSON.stringify(annotationsData));
                localStorage.setItem('annotation-comments', JSON.stringify(commentsData));

                // Load annotations into the map
                annotationsData.forEach(annotation => {
                    try {
                        window.annotoriusInstance.addAnnotation(annotation);
                        setTimeout(() => {
                            addAnnotationComment(annotation);
                        }, 500);
                        console.log('Restored annotation:', annotation.id);
                    } catch (error) {
                        console.error('Error restoring annotation:', error, annotation);
                    }
                });
            })
            .catch(error => {
                console.error('Error loading data from API:', error);
                // Fallback to localStorage on error
                const savedAnnotations = loadAnnotationsFromStorage();
                savedAnnotations.forEach(annotation => {
                    try {
                        window.annotoriusInstance.addAnnotation(annotation);
                        setTimeout(() => {
                            addAnnotationComment(annotation);
                        }, 500);
                        console.log('Restored annotation from localStorage:', annotation.id);
                    } catch (error) {
                        console.error('Error restoring annotation:', error, annotation);
                    }
                });
            });
            */

            // CURRENT LOCALSTORAGE IMPLEMENTATION (REPLACE WITH API CALL ABOVE)
            const savedAnnotations = loadAnnotationsFromStorage();
            console.log('Loading saved annotations:', savedAnnotations);

            savedAnnotations.forEach(annotation => {
                try {
                    // Add the annotation to the map
                    window.annotoriusInstance.addAnnotation(annotation);

                    // Add comment display and label for restored annotation
                    setTimeout(() => {
                        addAnnotationComment(annotation);
                        addAnnotationLabel(annotation);
                    }, 500); // Small delay to ensure annotation is rendered

                    console.log('Restored annotation:', annotation.id);
                } catch (error) {
                    console.error('Error restoring annotation:', error, annotation);
                }
            });
        }


        function storeCommentsAnnotationToLocal() {
            // console.log('storeAnnotationToLocal', '<?php echo e($orderItem->annotations); ?>');
            // console.log('storeCommentToLocal', JSON.parse('<?php echo json_encode($orderItem->annotations, 15, 512) ?> '));
            localStorage.setItem('annotorious-annotations', JSON.stringify(JSON.parse('<?php echo json_encode($orderItem->annotations, 15, 512) ?>')));
            localStorage.setItem('standalone-comments', JSON.stringify(JSON.parse('<?php echo json_encode($orderItem->comments, 15, 512) ?>')));


        }


        // Add click outside detection
        document.addEventListener('click', function(event) {
            if (window.currentEditingAnnotation) {
                const inputBox = document.getElementById(`comment-input-${window.currentEditingAnnotation.id}`);
                if (inputBox && !inputBox.contains(event.target)) {
                    // Check if there's text in the input
                    const textarea = document.getElementById(`new-comment-${window.currentEditingAnnotation.id}`);
                    if (textarea) {
                        const commentText = textarea.value.trim();
                        if (commentText) {
                            // Save the comment
                            saveNewComment(window.currentEditingAnnotation.id);
                        } else {
                            // Delete annotation if comment is empty
                            cancelNewComment(window.currentEditingAnnotation.id);
                        }
                    }
                }
            }
        });

        // jQuery document ready function
        $(document).ready(function() {
            storeCommentsAnnotationToLocal();
            // localStorage.removeItem('annotorious-annotations')
            // Initialize global variables
            window.selectedAnnotations = [];

            const anno = Annotorious.createImageAnnotator('my-image', {
                widgets: [
                    // Enables the default COMMENT input box but we'll override the behavior
                    {
                        widget: 'COMMENT'
                    }
                ],
                drawingEnabled: true
            });

            // Store the annotorious instance globally for access from other functions
            window.annotoriusInstance = anno;

            anno.setUser({
                id: 'user-123',
                name: '<?php echo e(auth()->user()->name); ?>'
            });

            // Load existing annotations from localStorage after a short delay
            setTimeout(() => {
                loadExistingAnnotations();
            }, 100);

            // Event: When annotation is created
            anno.on('createAnnotation', annotation => {
                // console.log('Created annotation:', annotation);

                // Show comment input box for new annotation
                showCommentInputBox(annotation);
            });

            // Event: When annotation is updated
            anno.on('updateAnnotation', (annotation, previous) => {
                console.log('Annotation updated:', annotation, previous);

                // Get existing comment data to preserve it
                const existingComment = getCommentFromStorage(annotation.id);
                const commentText = existingComment ? existingComment.comment : '';
                const userName = existingComment ? existingComment.user : 'DemoUser';

                // Update the annotation with integrated comment in localStorage
                saveAnnotationToStorage(annotation, commentText, userName);

                // Update the comment display
                addAnnotationComment(annotation);
            });

            // Event: When annotation is deleted
            anno.on('deleteAnnotation', annotation => {
                console.log('Annotation deleted:', annotation);

                // Remove annotation with integrated comment from localStorage
                removeAnnotationFromStorage(annotation.id);

                // Remove the comment display
                removeAnnotationComment(annotation.id);
            });

            // Event: When annotation selection changes
            anno.on('selectionChanged', annotations => {
                console.log('Selection changed:', annotations);

                // Store currently selected annotations
                window.selectedAnnotations = annotations || [];

                // Clear all highlights first
                document.querySelectorAll('.comment-list-item').forEach(item => {
                    item.classList.remove('highlighted');
                });
                document.querySelectorAll('.annotation-label').forEach(label => {
                    label.classList.remove('highlighted');
                });

                // Hide all comments first
                hideAllComments();

                // Show comments and highlight for selected annotations
                if (annotations && annotations.length > 0) {
                    annotations.forEach(annotation => {
                        showComment(annotation.id);
                        highlightCommentInList(annotation.id);
                        highlightAnnotationLabel(annotation.id);
                    });
                }
            });

            // Event: When annotation is clicked
            anno.on('clickAnnotation', (annotation, event) => {
                console.log('Annotation clicked:', annotation);
                // Show comment for clicked annotation
                showComment(annotation.id);
                // Highlight the corresponding comment in the list
                highlightCommentInList(annotation.id);
                // Highlight the annotation label
                highlightAnnotationLabel(annotation.id);
            });

            // Event: When mouse enters annotation - SHOW COMMENT
            anno.on('mouseEnterAnnotation', (annotation, event) => {
                // console.log('Mouse entered annotation:', annotation);
                // Show comment for this annotation
                showComment(annotation.id);
            });

            // Event: When mouse leaves annotation - HIDE COMMENT (unless selected)
            anno.on('mouseLeaveAnnotation', (annotation, event) => {
                // console.log('Mouse left annotation:', annotation);

                // Check if this annotation is currently selected
                const isSelected = window.selectedAnnotations &&
                    window.selectedAnnotations.some(selected => selected.id === annotation.id);

                // Only hide comment if annotation is not selected
                if (!isSelected) {
                    hideComment(annotation.id);
                }
            });

            // Handle window resize to reposition labels
            $(window).on('resize', function() {
                if (window.annotoriusInstance) {
                    const annotations = window.annotoriusInstance.getAnnotations();
                    annotations.forEach(annotation => {
                        // Reposition labels
                        addAnnotationLabel(annotation);
                    });
                }
            });
        });
    </script>
</body>

</html>
<?php /**PATH C:\Users\<USER>\Desktop\live\websites_laravel\resources\views/userpages/projects/graphic/orderImageAnnotaion.blade.php ENDPATH**/ ?>