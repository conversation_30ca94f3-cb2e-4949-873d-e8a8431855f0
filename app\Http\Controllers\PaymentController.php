<?php

namespace App\Http\Controllers;

use App\Models\LeadAddress;
use DateTime;
use Exception;
use App\Models\User;
use App\Custom\Paytm;
use App\Models\Invoice;
use App\Custom\Razorpay;
use App\Models\CartView;
use App\Models\InvoiceItems;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\Redirect;
use SimpleSoftwareIO\QrCode\Facades\QrCode;

class PaymentController extends Controller
{

    function invoiceAddress()
    {
        try {

            $lead = request()->user();
            $data['cartViewData'] = CartView::where(['lead_id' => $lead->id, 'parentCart_id' => null])->with('addons')->get();
            $data['lead'] = auth()->user();
            $data['country'] = DB::table("country")->get();
            $data['calculation'] = getCartTotals($data['cartViewData']);
            return view('userpages.invoice.addr', $data);
        } catch (Exception $e) {
            return redirect('/')->with('error', $e->getMessage());
        }
    }
    function invoicePayment()
    {
        $data['paytmGateWay'] = null;
        $data['paytmTxnToken'] = null;
        $data['upiPaymentLink'] = null;
        $data['paytmGateWayEnable'] = false;
        $data['accountDetails'] = null;
        try {
            $lead = auth()->user();
            $data['cartViewData'] = CartView::where(['lead_id' => $lead->id, 'parentCart_id' => null])->with('addons')->get();
            if ($data['cartViewData']->count() == 0) {
                return redirect('/')->with("error", "Empty Cart.");
            }
            $data['lead'] = $lead;
            $data['country'] = DB::table("country")->get();
            $data['calculation'] = getCartTotals($data['cartViewData']);
            $data['selectedAddress'] = LeadAddress::find(session()->get('address_id'));
            $hostdata = hostData();

            if (empty($hostdata)) {
                return redirect('/')->with("error", "⚠️ Oops! Unable to find host  ⚠️");
            }
            $data['lead'] = User::where("id", $lead->id)->first();
            if ($hostdata->enableRazorPayVirtualAccount && $hostdata->razorpay_key_id && $hostdata->razorpay_secret_key) {
                $checkVirtualAccount = DB::table('lead_company_wise_virtual_account')
                    ->where('lead_id', $data['lead']->id)
                    ->first();
                if ($checkVirtualAccount) {
                    $data['accountDetails']['account_number'] = $checkVirtualAccount->accountNumber;
                    $data['accountDetails']['bank_name'] = $checkVirtualAccount->bankName;
                    $data['accountDetails']['name'] = $checkVirtualAccount->beneficiary;
                    $data['accountDetails']['ifsc'] = $checkVirtualAccount->ifsc;
                }
            }
            DB::commit();
        } catch (Exception $e) {
            return redirect()->back()->with('error', 'error while Connecting With System');
        }


        $data['paybleAmt'] = round($data['calculation']['totalPayable']);

        if ($data['paybleAmt'] <= 0) {
            return redirect('/')->with('warning', 'We are unable to process this request.');
        }
        $data['str'] = Crypt::encryptString(json_encode(['lead_id' => $data['lead']->id, 'mode' => 'cartPay', 'amount' => $data['paybleAmt']]));


        if ($hostdata->enableRazorPayVirtualAccount && $hostdata->razorpay_key_id && $hostdata->razorpay_secret_key) {

            if ($checkVirtualAccount) {
                if (date_diff(new DateTime(($checkVirtualAccount->datetime)), new DateTime())->days > 59) {
                    $razorpay = new Razorpay;
                    $data['accountDetails'] = $razorpay->virtualAccount($data['lead']->id);
                }
            } else {
                $razorpay = new Razorpay;
                $data['accountDetails'] = $razorpay->virtualAccount($data['lead']->id);
            }
        }
        return view('userpages.invoice.payment', $data);
    }

    public function payUpi(Request $request)
    {
        $data['invoice'] = Invoice::find(1);
        return view('userpages.invoice.payment_upi', $data);
    }
    public function payQr(Request $request)
    {
        if ($request->PaymentKey != null) {
            try {
                $str = json_decode(Crypt::decryptString($request->PaymentKey), true);
            } catch (Exception $e) {
                $data = ['status' => false, 'message' => 'Request Fail.(Invalid Key.)'];
                return response()->json($data);
            }
        }
        try {
            $invoice = null;
            if (isset($str['mode']) && $str['mode'] == 'addFund') {
                $invoice = null;
            } else {
                if (isset($str['invoice_id']) && $str['invoice_id'] != null) {
                    $inv_id = $str['invoice_id'];
                } else {
                    $inv = generateInvoice($str['lead_id'] ?? auth()->id());
                    if ($inv['status'] == true) {
                        $inv_id = $inv['id'];
                    } else {
                        return  response()->json($inv);
                    }
                }
                $invoice = Invoice::find($inv_id ?? null);
                if ($invoice) {
                    if ($invoice->isPaid == 1) {
                        return redirect()->route('alreadyPaid');
                    }
                }
            }
            $lead_id = $str['lead_id'] ?? $invoice->lead_id;
            $hostdata = hostData();
            $data['paybleAmt'] = $str['amount'];
            if ($hostdata->enableUpi && $hostdata->upi) {
                $proformaNumber = $invoice->id ?? $str['mode'];
                $data['upiPaymentLink'] = "upi://pay?pa=$hostdata->upi&pn=$hostdata->beneficiary&tn=$proformaNumber&am=" . $data['paybleAmt'] . "&cu=INR";
                $data['CompanyQr'] = QrCode::size(240)->style('round')
                    ->generate($data['upiPaymentLink']);
            }
            if ($hostdata->enablePaytmQr) {
                if ($hostdata->paytm_merchant_mid && $hostdata->paytm_merchant_key) {

                    $paytm = new Paytm();
                    $paytmQr = $paytm->qr($data['paybleAmt'], $lead_id, $inv_id ?? null);
                    if ($paytmQr['status']) {
                        $data['qrString'] = $paytmQr['qr'];
                        $data['paytmQrEnable'] = true;
                    } else {
                        $data['qrString'] = asset('media/images/blank.png');
                    }
                }
            }
            $data["displayQr"] = <<<DATA
            <h4 class="text-center countdown" style="color: #194DAB;">
                <span class="hour"></span>0
                <span class="fs-6">H</span> :
                <span class="minute">04</span>
                <span class="fs-6">M</span> :
                <span class="second">09</span>
                <span class="fs-6">S</span>
            </h4>
            <hr>
            DATA;
            $data["displayQr"] .= "<div class='d-flex flex-column'>";
            if ($hostdata->enableUpi && $hostdata->upi) {
                $data['displayQr'] .= '<div class="text-center m-auto">' . $data['CompanyQr'] . '</div>';
            }
            if ($hostdata->enablePaytmQr) {
                if ($hostdata->paytm_merchant_mid && $hostdata->paytm_merchant_key) {
                    $data["displayQr"] .= '<div class="text-center m-auto"><img src="' . $data['qrString'] . '" alt="QR CODE" id="qrimg" class="img-fluid  "
                    style="width: 250px;"></div>';
                }
            }
            $data["displayQr"] .= "</div>";
            $data['status'] = true;
            return response()->json($data);
        } catch (\Exception $e) {
            $data = [
                'status' => false,
                'message' => 'Request Fail. (System Error.)',
                'devErr' => $e->getMessage(),
            ];
            return response()->json($data);
            // return redirect()->back()->withErrors($e->getMessage())->withInput();
        }
    }
    public function chequePaid()
    {
        try {
            $lead = auth()->user();
            $inv = generateInvoice($lead->id ?? auth()->id());
            if ($inv['status'] == true) {
                $inv_id = $inv['id'];
            } else {
                return redirect()->back()->with('error', ErrMsg());
            }
            $data['cartViewData'] = CartView::where(['lead_id' => $lead->id, 'parentCart_id' => null])->with('addons')->get();
            if ($data['cartViewData']->count() == 0) {
                return redirect('/')->with("error", "Empty Cart.");
            }
            $data['invoice'] = Invoice::find($inv_id);
            $data['lead'] = $lead;
            $data['country'] = DB::table("country")->get();
            $data['calculation'] = getCartTotals($data['cartViewData']);
            $data['selectedAddress'] = LeadAddress::find(session()->get('address_id'));
            return view('userpages.invoice.cheque_paid', $data);
        } catch (Exception $e) {
            return redirect()->back()->with('error', ErrMsg());
        }
    }
    public function neftPaid()
    {
        $lead = auth()->user();
        $data['cartViewData'] = CartView::where(['lead_id' => $lead->id, 'parentCart_id' => null])->with('addons')->get();
        if ($data['cartViewData']->count() == 0) {
            return redirect('/')->with("error", "Empty Cart.");
        }
        $data['lead'] = $lead;
        $data['country'] = DB::table("country")->get();
        $data['calculation'] = getCartTotals($data['cartViewData']);
        $data['selectedAddress'] = LeadAddress::find(session()->get('address_id'));
        return view('userpages.invoice.neft_paid', $data);
    }
    public function thankOfflinePage(Request $request)
    {
        $data['invoice'] = Invoice::find(1);

        return view('userpages.invoice.cheque_neft_thankyou', $data);
    }
    public function thankNetPage(Request $request)
    {


        $data['invoice'] = Invoice::find(1);


        return view('userpages.invoice.upi_net_thankyou', $data);
    }

    // public function  upiRequestSend(Request $request) {}
    function paymentCaptured()
    {
        try {

            $invoice = null;

            if (isset($_GET['invoice_id'])) {
                $invId = Crypt::decryptString($_GET['invoice_id']);
                $data['invoice'] = Invoice::find($invId);
            }
            if ($data['invoice']) {
                return view('userpages.paymentInvoice.paymentCaptured', $data);
            } else {
                return view('userpages.paymentInvoice.paymentCaptured');
            }
        } catch (\Throwable $e) {
            return redirect('/')->with('info', 'Payment Status Will reflect in your account shortly.');
        }
    }
}
