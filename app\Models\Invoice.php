<?php

namespace App\Models;

use App\Models\InvoiceItems;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Invoice extends Model
{
    use HasFactory;
    protected $table = 'invoice';
    public $timestamps = false;
    public function items()
    {
        return $this->hasMany(InvoiceItems::class, 'invoice_id', 'id')->with('variant');
    }
    public function lead()
    {
        return $this->belongsTo(User::class, 'lead_id', 'id');
    }

    
}
