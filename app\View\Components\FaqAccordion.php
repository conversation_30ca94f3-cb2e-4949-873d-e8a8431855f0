<?php

namespace App\View\Components;

use Closure;
use Illuminate\Contracts\View\View;
use Illuminate\View\Component;

class FaqAccordion extends Component
{

    /**
     * Array of FAQ items.
     * Each item should be an associative array with 'question' and 'answer' keys.
     * 
     * Example:
     * [
     *     [
     *         'question' => 'What are the main features of WhatsApp Marketing?',
     *         'answer' => '1. Message API<br>2. Message Bot<br>3. Group Auto Send<br>4. Number Filter',
     *     ],
     *     [
     *         'question' => 'Can I create multiple channels in my account?',
     *         'answer' => 'Yes you can create unlimted number of channels in your account.',
     *     ],
     *     // ... more FAQ items ...
     * ]
     *
     * @var array
     */
    public array $faqs;
    public string $collapsedColor;
    public string $activeColor;
    /**
     * Create a new component instance.
     */
    public function __construct(array $faqs, string $collapsedColor = "#F6F6F6", string $activeColor = "#e13362")
    {
        $this->faqs = $faqs;
        $this->collapsedColor = $collapsedColor;
        $this->activeColor = $activeColor;
    }

    /**
     * Get the view / contents that represent the component.
     */
    public function render(): View|Closure|string
    {
        return view('components.global.faq-accordion');
    }
}
