* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

.font-sm-header {
    font-size: 15px;
}

@media screen and (max-width: 567px) {
    .font-sm-header {
        font-size: 12px;
    }
}

@media screen and (max-width: 375px) {
    .font-sm-header {
        font-size: 10px;
    }
}

.focusremove:focus-visible {
    outline: none;
}

.plan-card-box {
    border: 1.5px solid #DFDFDF;
    box-shadow: 0 .5rem 1rem 2px #0000001c;
}

.plan-card-box:hover {
    border: 1.5px solid #2D2DB0;
    box-shadow: 0 .5rem 1rem 4px #00000026;
}

/*
.plan-bg-img{
    background-image: url('/assets/rapbooster/images/Rectangle579.png');
    background-size: contain;
    background-repeat: no-repeat;
}

@media screen and (max-width: 1194px) {
    .plan-bg-img{
        background-size: auto;
        background-repeat: no-repeat;
    }
  } */

.half-background-image {
    width: 100%;
    /* Adjust width as needed */
    height: 80%;
    /* Adjust height as needed */
    position: relative;
    border: 1px solid black;
}

.half-background-image::after {
    border-radius: 15px 15px 0px 0px;
    content: "";
    /* Empty content for the pseudo-element */
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 260px;
    background-image: "/assets/rapbooster/images/Rectangle579.png";
    background-size: cover;
    background-repeat: no-repeat;
    /* Fills the entire div area */
    /* z-index: -1; */
    /* Ensures background appears behind content */
}

.bg-light-gray {
    background-color: #f3f4f6;
}

.global-header {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1040;
    background-color: #F5F5F5;
}

.home {
    gap: 1rem;
}

.first {
    gap: 1rem;
}

.second {
    gap: 1rem;
}

.hambur {
    display: none;
}

.second a {
    position: relative;
}

.home a {
    color: rgb(0, 0, 0);
    text-align: center;
    padding: 0px 5px;
}

.rotated {
    transform: rotate(180deg);
    transition: transform 0.5s ease;
}

.rotated0deg {
    transform: rotate(0deg);
    transition: transform 0.5s ease;
}

.c-dropdown {
    float: left;
    z-index: 1040;
    /* overflow: hidden; */
}

.c-dropdown .c-dropbtn {
    outline: none;
    color: rgb(69, 43, 43);
    background: inherit;
}

.home a:hover {
    color: black;
    text-decoration: none;
}

.c-dropdown:hover .c-dropbtn {
    color: #056CB8;
}

/* .topheading:hover {
    color: #005ce2 !important;
} */

.c-dropdown-content {
    display: none;
    left: 0;
    box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.2);
    z-index: 1060;
    padding: 0 !important;
    margin: 0 !important;
}

.c-dropdown.active .c-dropdown-content {
    display: block;
}

.division {
    gap: 5%;
}

.support1 {
    background: #F0FFFF;
}

.support2 {
    gap: 2%;
}

.flex-res .column {
    width: 250px !important;
}

.side-column {
    width: 5%;
    background-color: #577399;
}

.topbar a {
    transition: 0.5s ease;
}

.topbar a:hover {
    background-color: #dde0e5ad;

}


.column a:hover {
    background-image: linear-gradient(45deg, #eef3fb, transparent);
}

.icon-show .icon {
    transition: ease-in 1s;
}

.icon-show:hover .icon {
    display: block !important;
}

.verti-line {
    width: 2px;
    height: 45px;
    background-color: #30303063;
}

.row:after {
    content: "";
    display: table;
    clear: both;
}

.dropdown-menu {
    padding-top: 0rem !important;
}

/* .dp-img-container {
    padding: 0rem !important;
} */

ul>li {
    padding: 0rem !important;
}


.img-obj-shadow {
    filter: drop-shadow(0px 10px 15px #767679ef);
}


.div-img {
    height: 90px;
    width: 90px;
    /* box-shadow: 4px 4px 15px 0px rgba(0, 0, 0, 0.15); */
}

/* .dp-img {
    height: 90px;
    width: 90px;
    position: relative;
    top: 40px;
    left: 127px;
    border-radius: 50%;
    background: #969696;
    box-shadow: 4px 4px 15px 0px rgba(0, 0, 0, 0.15);
}

.dp-img img {
    width: 90px;
    height: 90px;
    border-radius: 50%;
    position: absolute;
    top: 50%;
    right: -50%;
    transform: translate(-50%, -50%);
} */

/* .btn[aria-expanded="true"] {
    background-color: transparent !important;
} */

.dropdown-menu {
    border-radius: 20px !important;
    width: 350px !important;
}

.title-para {
    color: #797979;
    font-size: 18px;
}

.mid-gap {
    gap: 0.7rem !important;
}

.bg-div {
    border-radius: 15px 15px 0px 0px !important;
}

.hover-effect .d-flex:hover {
    color: #0d6efd;
}

.dropdown-item {
    box-shadow: none !important;
}

/* li button:hover {
    background-color:  !important;
} */

li button:hover .icon {
    display: block !important;
    color: #797979;
}

/* .details-div p {
    padding: 0rem !important;
    margin: 0rem !important;
} */

@media screen and (max-width: 1199px) {
    .column a {
        height: 135px !important;
    }
}

@media screen and (max-width: 991px) {
    .home img {
        float: right;
        display: block;
    }

    .hambur {
        display: block !important;
        position: absolute;
        top: 5px;
        right: 5px;
        background: transparent;
        border: none;
    }

    .home {
        justify-content: space-between;
    }

    .first,
    .second {
        display: none;
    }

    .showhead {
        display: flex;
        flex-direction: column;
    }

    .homedirection {
        flex-direction: column;
        align-items: baseline;
    }
}

@media screen and (max-width: 991px) {
    .home.responsive {
        position: relative;
    }

    .home.responsive img {
        position: absolute;
        right: 0;
        top: 0;
    }

    .home.responsive a {
        float: none;
        display: block;
        text-align: left;
    }

    .home.responsive .c-dropdown {
        float: none;
    }

    .home.responsive .c-dropdown-content {
        position: relative;
    }

    .home.responsive .c-dropdown .c-dropbtn {
        display: block;
        width: 100%;
        text-align: left;
    }
}

/* .dropdown:hover .dropdown-menu {
    display: block;
    width: auto;
    height: auto;
    transition: 0.3s ease;
    box-shadow: 0px 2px 0px 0px rgba(193, 91, 91, 0.2);
} */

.dropdown>.dropdown-toggle:active {
    pointer-events: none;
}

.dropdown-item:hover {
    background-color: #EAF1FF !important;
    box-shadow: inset 2px 1px 4px #EAF1FF;
    transition: 0.3s ease;
    color: #212529;
}

.dropdown-item {
    color: #212529;
}

.call-btn:hover {
    background: none;
    transition: 0.3s ease;
}

.gray-dark-text {
    color: #626262 !important;
}

@media screen and (max-width: 991px) {
    .column {
        /* width: 100%; */
        height: auto;
    }

    .row .flex-res {
        gap: 0 !important;
    }

    .column a {
        height: 150px !important;
    }
}

.c-dropdown-content {
    animation: fade-in 0.3s;
}

@keyframes fade-in {
    from {
        opacity: 0;
    }

    to {
        opacity: 1;
    }
}

@media only screen and (max-width: 767px) {
    .flex-res {
        flex-direction: column;
    }

    .flex-res .column {
        width: 100% !important;
    }

    .row .flex-res {
        gap: 0 !important;
    }

    .c-dropdown-content {
        max-height: 400px;
        overflow-y: auto;
    }

    .support2 {
        flex-direction: column;
        row-gap: 1rem !important;
    }

    .column a {
        height: auto !important;
    }
}

.show {
    display: block !important;
}

.head-btn[aria-expanded="true"] {
    background-color: transparent !important;
}


/* billing address box css */
.ba-name {
    font-size: 20px !important;
    font-weight: 500 !important;
}

.ba-company {
    font-size: 18px !important;
    font-weight: 500 !important;
}

.ba-address {
    font-size: 16px !important;

}

@media screen and (max-width: 567px) {
    .ba-name {
        font-size: 16px !important;

    }

    .ba-company {
        font-size: 15px !important;

    }

}

@media screen and (max-width: 375px) {
    .ba-name {
        font-size: 16px !important;

    }

    .ba-company {
        font-size: 15px !important;

    }

}


/* product view  */

.product-view-box {
    .product-name {
        font-size: 30px;
    }

    .product-details {
        font-size: 18px;
        color: gray;

    }

    .mrp {
        color: rgb(162, 161, 161);
    }

    .selling {
        color: rgb(44, 44, 44);
    }

    .variant {
        background: white;
        border: 1.5px solid rgb(194, 194, 194, .3);
        font-size: 14px;

        .variant-mrp {
            font-size: 15px;
            font-weight: 400;
            color: rgb(109, 109, 109);
        }

        .variant-selling {
            font-size: 16px;
            font-weight: 500;
            color: rgb(68, 68, 68);
        }
    }

    .variant:hover {
        background: white;
        border: 1.5px solid rgb(123, 123, 242, .8);
        box-shadow: 1.5px 1.5px 6px rgba(128, 128, 128, 0.523);
    }

    .variant-active {
        border: 1.8px solid skyblue;
        box-shadow: 0px 1px 3px 1px #87ceeb80;
        color: black !important;
    }

    .selectedUnit-div-active {
        border: 1.8px solid rgb(123, 123, 242, .9) !important;
    }
}


/* image slider product view */
#thumbnail-slider .splide__slide {
    opacity: 0.6;
    cursor: pointer;
}

#thumbnail-slider .splide__slide.is-active {
    opacity: 1;
}

#thumbnail-slider img {
    width: auto;
    height: 100px;
    object-fit: cover;
}

#main-slider img {
    width: 100%;
    height: auto;
}

/* ******************** */
#main-slider .splide__slide img {
    width: 100%;
    height: auto;
    object-fit: contain;
    /* prevent cutting */
}

#thumbnail-slider .splide__slide img {
    width: 100%;
    height: 100px;
    object-fit: cover;
}

/* ******************** */


.no-swiper {
    position: relative;
    width: 100%;
    height: 100%;
    z-index: 1;
    display: flex;
    transition-property: transform;
    box-sizing: content-box;
}


.swiper {
    width: 100%;
    height: 100%;
}

.swiper-slide {
    text-align: center;
    font-size: 18px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.swiper-slide img {
    display: block;
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.products-box-item-wrapper {
    padding: 5px;
}

.products-box-item {
    background: rgba(255, 255, 255, 0.6);
    box-shadow: 1px 1px 5px 0 rgba(31, 38, 135, 0.37);
    backdrop-filter: blur(9px);
    -webkit-backdrop-filter: blur(9px);
    border: 1.8px solid #fff;
    position: relative;

    z-index: 1;

}

.products-box-item:hover {
    background: rgba(255, 255, 255) !important;
}



.product-details {
    padding: 10px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.product-detail-name {
    font-weight: 600;
    font-size: 21px;
}


.product-detail-price-mrp {
    color: #999;
    font-size: 15px;

}

.product-detail-price-selling {
    font-size: 22px;

}


/* required css for product and  */
