@extends('layout.app-blank')
@php
$host = request()->getHttpHost();
$hostProps = allDomains()[$host];

$theme = $hostProps['theme'];
@endphp
@section('siteIcon', asset($hostProps['icon']))
@section('script')
@endsection
@section('css')
<style>
    .change-scroll::-webkit-scrollbar {
        width: 6.5px;
    }

    .change-scroll::-webkit-scrollbar-track {
        background: #c1c1c100;
    }

    .change-scroll::-webkit-scrollbar-thumb {
        border-radius: 3px;
        background: #C1C1C1;
    }

    .change-scroll::-webkit-scrollbar-thumb:hover {
        background: #9f9f9f;
    }

    .change-scroll::-webkit-scrollbar-button {
        display: none;
    }
</style>
<link rel="stylesheet" href="{{ asset('assets/userpages/customer.css') }}">

<style>
    body {
        margin: 0;
        padding: 0;
        min-height: 100vh;
        display: flex;
        flex-direction: column;
    }

    .footer {
        /* position: fixed; */
        left: 0;
        bottom: 0;
        width: 100%;
        color: black;
        text-align: center;
    }
</style>
@endsection
@section('title', 'Checkout')
@section('main')
@include('userpages.invoice.components.header')
{{-- @yield('breadcrumb') --}}
<div class="container p-0" style=" flex: 1;margin-top: 80px;">
    <div class="">
        @include('components.core.alerts')
    </div>
    @yield('webpage')

</div>
@include('userpages.invoice.components.footer')
@endsection
