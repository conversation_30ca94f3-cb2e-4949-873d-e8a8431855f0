<?php

namespace App\Models;

use App\Models\Product;
use Illuminate\Database\Eloquent\Model;

use Illuminate\Database\Eloquent\Factories\HasFactory;


class ProductVariant extends Model
{
    use HasFactory;

    protected $table = 'product_variant';

    public function product()
    {
        return $this->belongsTo(Product::class, 'product_id', 'id')->with(['images','category']);
    }

    //hasmany ProductVariantQtyRange
    public function ranges()
    {
        // id, product_variant_id, minQty, maxQty, sellingPrice, isOnWebsite, isActive
        return $this->hasMany(ProductVariantQtyRange::class, 'product_variant_id', 'id')->where(['isOnWebsite' => 1, 'isActive' => 1])->orderBy('minQty');
    }
    public function minRange()
    {
        return $this->hasOne(ProductVariantQtyRange::class, 'product_variant_id', 'id')->where(['isOnWebsite' => 1, 'isActive' => 1])->orderBy('minQty');
    }


     public function maxRange()
    {
        return $this->hasOne(ProductVariantQtyRange::class, 'product_variant_id', 'id')->where(['isOnWebsite' => 1, 'isActive' => 1])->orderBy('minQty','DESC');
    }
}
