<?php

namespace App\Http\Controllers;

use App\Models\KnowladgeBaseArtical;
use App\Models\KnowladgeBaseCategory;
use Exception;
use Illuminate\Http\Request;

class KnowladgeBaseController extends Controller
{
    public function ArticalView($article)
    {
        try {
            $data['article'] = KnowladgeBaseArtical::find($article);
            $data['openArticals'] = KnowladgeBaseArtical::where('isActive', 1)->where('isOnAll', 1)->get();
            $data['forAllCategory'] = KnowladgeBaseCategory::where('kbParentCategory_id', NULL)->where('isActive', 1)->get();
            return view("global.viewArtical", $data);
        } catch (Exception $e) {
            return redirect('/')->with("error", ErrMsg());
        }
    }
    public function ArticalCate($Cate, $parentCategory = null)
    {
        try {
            $data['parentCategory'] = KnowladgeBaseCategory::find($parentCategory);
            $data['category'] = KnowladgeBaseCategory::find($Cate);
            $data['Subcategories'] = KnowladgeBaseCategory::where('id', '=', $Cate)->with('SubCategories', 'Articals')->first();
            if ($data['Subcategories']) {
                $host = request()->getHttpHost();
                $data['allCats'] = KnowladgeBaseCategory::where('domains', 'Like', "%{$host}%")->with('SubCategories', 'Articals')->get();
                $data['forAllCategory'] = KnowladgeBaseCategory::where('kbParentCategory_id', NULL)->where('isActive', 1)->get();
                return  view("global.viewCate", $data);
            }
            return redirect('/')->with('error', 'invalid Arguments.');
        } catch (Exception $e) {
            return redirect('/')->with("error", ErrMsg());
        }
    }
}
