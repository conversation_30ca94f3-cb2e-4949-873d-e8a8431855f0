@extends('userpages.paymentInvoice.components.main')
@section('webpage')
    <div class="">
        <style>
            ::placeholder {
                color: gray !important;
                opacity: .2;
                /* Firefox */
            }
        </style>

        <div class="row">
            <div class="col-lg-8  col-sm-12   col-md-12 mt-3 ">
                @include('userpages.paymentInvoice.card_cart')
                <div class="bg-white">
                    <div class="d-flex align-items-center justify-content-between gap-3 px-3 mb-3 p-2"
                        style="background-color: #EAF1FF;border-radius:7px 7px 0 0;">
                        <div class="d-flex align-items-center gap-3">
                            <span class="align-items-center justify-content-center badge d-flex rounded-circle text-white"
                                style="height: 30px;width: 30px;background-color:#194DAB;">2</span>
                            <h4 style="color: #194DAB;">Billing Address</h4>
                        </div>
                    </div>
                    <div class="p-3">
                        <form action="{{ route('pi.address', ['invoice' => $invoice->id]) }}" method="post">
                            @csrf
                            <input type="hidden" name="invoice_id" value="{{ Crypt::encrypt($invoice->id) }}">
                            <div class="row">
                                {{-- <div class="col-lg-6"> --}}
                                <div class="col-12">

                                    <label class="form-label support-label">Name: </label>
                                    <div class="mb-3">

                                        <input type="text" name="name" class="form-control fieldInput"
                                            placeholder="Name" value="{{ old('name', $invoice->name) }}">
                                        @error('name')
                                            <div class="text-danger">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-6">
                                    <label class="form-label support-label">GST No.:</label>
                                    <div class="mb-3">
                                        <input type="text" name="gst" class="form-control fieldInput"
                                            placeholder="Ex: 08AAABB74092Z0" value="{{ old('gst', $invoice->gst) }}">
                                        @error('gst')
                                            <div class="text-danger">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-6">
                                    <label class="form-label support-label">Business Name:</label>
                                    <div class="mb-3">
                                        <input type="text" name="company" class="form-control fieldInput"
                                            value="{{ old('company', $invoice->company) }}">
                                        @error('company')
                                            <div class="text-danger">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-6">
                                    <label class="form-label support-label">Mobile Number: </label>
                                    <div class="mb-3">
                                        <input type="number" name="mobile" class="form-control fieldInput"
                                            value="{{ old('mobile', $invoice->mobile) }}">
                                        @error('mobile')
                                            <div class="text-danger">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-6">
                                    <label class="form-label support-label">Email Address: </label>
                                    <div class="mb-3">
                                        <input type="text" name="email" class="form-control fieldInput"
                                            value="{{ old('email', $invoice->email) }}">
                                        @error('email')
                                            <div class="text-danger">{{ $message }}</div>
                                        @enderror
                                    </div>

                                </div>
                                <div class="col-12">
                                    <label class="form-label support-label">Billing Address:</label>
                                    <div class="mb-3">
                                        <textarea class="form-control fieldInput" name="address" placeholder="Enter address here..." rows="3">{{ old('address', $invoice->address) }}</textarea>
                                        @error('address')
                                            <div class="text-danger">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-6">
                                    <label class="form-label support-label">PIN/ZIP Code: </label>
                                    <div class="mb-3">
                                        <input type="text" id="addpincode" name="pinCode" class="form-control fieldInput"
                                            placeholder="Pin" value="{{ old('pinCode', $invoice->pincode) }}">
                                        @error('pinCode')
                                            <div class="text-danger">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-6">
                                    <label class="form-label support-label">City: </label>
                                    <div class="mb-3">
                                        <input type="text" id="addcity" name="city" class="form-control fieldInput"
                                            placeholder="City" value="{{ old('city', $invoice->city) }}">
                                        @error('city')
                                            <div class="text-danger">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-6">
                                    <label class="form-label support-label">State: </label>
                                    <div class="mb-3">
                                        <input type="text" id="addstate" name="state" class="form-control fieldInput"
                                            placeholder="State" value="{{ old('state', $invoice->state) }}">
                                        @error('state')
                                            <div class="text-danger">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-6">
                                    <label class="form-label support-label">Country: </label>
                                    <div class="mb-3">
                                        <input type="text" id="addcountry" name="country"
                                            class="form-control fieldInput" placeholder="Country"
                                            value="{{ old('country', $invoice->country) }}">
                                        @error('country')
                                            <div class="text-danger">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>


                                {{-- </div> --}}
                                <hr>
                                <div class="align-items-center justify-content-end d-flex">
                                    <a href="#">
                                        <button
                                            class="rounded-pill text-white ps-4 pe-4 pt-1 pb-1 d-flex align-items-center gap-2 btn-bg-blue">
                                            Continue
                                            <i class="fa-solid fa-chevron-right"></i>
                                        </button>
                                    </a>
                                </div>
                            </div>

                        </form>
                    </div>




                </div>
            </div>
            <div class="col-lg-4 col-md-12 col-sm-12">
                @include('userpages.cart.cartCalculations')

            </div>
        </div>
    </div>
@endsection
@section('PAGE-script')
    <script>
        $('#addpincode').change(async function() {
            var pincode = $(this).val();

            try {
                let res = await fetch("https://api.postalpincode.in/pincode/" + pincode);
                let response = await res.json();
                var postData = response[0]['PostOffice'][0];

                $('#addcountry').val(postData.Country);
                $('#addstate').val(postData.State);
                $('#addcity').val(postData.District);
            } catch (error) {
                console.log(error);
            }
        });
    </script>
@endsection
