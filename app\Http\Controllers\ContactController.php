<?php

namespace App\Http\Controllers;

use Exception;
use Illuminate\Http\Request;
use App\Models\WebsiteContactForm;
use Illuminate\Support\Facades\Auth;

class ContactController extends Controller
{
    public function submit(Request $request)
    {
        $url = 'https://www.google.com/recaptcha/api/siteverify';
        $data = array(

            'secret' => env('RECAPTCHA_SECRET_KEY'),
            'response' => $request->token,
            'remoteip' => $_SERVER['REMOTE_ADDR']
        );

        $options = array(
            'http' => array(
                'header'  => "Content-type: application/x-www-form-urlencoded\r\n",
                'method'  => 'POST',
                'content' => http_build_query($data)
            )
        );

        $context  = stream_context_create($options);
        $response = file_get_contents($url, false, $context);
        $res = json_decode($response, true);
        if ($res['success']) {
            $recaptcha_result = 1;
        } else {
            $recaptcha_result = 0;
        }
        $request->validate(
            [
                'mobile' => 'required_if:email,null|max:15',
                'message' => 'required',
                'email' => 'required_if:mobile,null|nullable|email',

            ],
            [],
            [
                'noOfUsers' => 'possible Number of customers',
                'mobile' => 'Mobile',
                'message' => 'Message',
                'email' => 'E-mail',
                'subject' => 'Category'
            ]
        );
        try {
            $form = new WebsiteContactForm;
            $form->hostName = request()->getHttpHost();
            $form->name = $request->name  ?? null;
            if ($request->has('partnerType') && $request->partnerType) {
                $form->isPartnerForm = 1;
                $form->partnerProgram = implode(",", $request->partnerType);
                $form->business = $request->company;
            }
            $form->message = $request->message ?? null;
            $form->isRecaptchaVerified = $recaptcha_result;
            $form->noOfUsers = $request->noOfUsers ?? null;
            $form->productCategory_id = $request->productCategory_id ?? null;
            $form->email = $request->email ?? null;
            $form->loggedInLead_id = Auth::id() ?? null;
            $form->mobile = $request->code . $request->mobile ?? null;
            $form->subject = $request->subject  ?? null;
            $form->save();
        } catch (Exception $e) {
            return redirect()->back()->with('error', "Whoops! We're having technical difficulties. Contact support for help.")->withInput();
        }
        return redirect()->back()->with('success', "Contact request sent! We'll reach out soon");
    }
}
