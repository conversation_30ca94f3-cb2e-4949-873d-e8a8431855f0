<nav class="navbar navbar-expand-lg bg-light shadow-sm pt-3  mt-3">
    <div class="container-lg d-flex justify-content-between align-items-center">

        <a class="navbar-brand fs-3 fw-bold" href="{{ route('andro.transactional-home') }}">
            Transactional Services
        </a>

        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav"
            aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
            <span class="navbar-toggler-icon"></span>
        </button>

        <div class="collapse navbar-collapse justify-content-end" id="navbarNav">
            <ul class="navbar-nav gap-3"> 
                <li class="nav-item fs-4">
                    <a class="nav-link text-orange hover-text-primary fs-5 {{ str_contains(Route::currentRouteName(), 'andro.transactional-features') ? 'active fw-bold text-primary' : '' }}"
                        href="{{ route('andro.transactional-features') }}"
                        style="{{ str_contains(Route::currentRouteName(), 'andro.transactional-features') ? 'color: #e13362 !important;' : '' }}">Featured</a>
                </li>
                <li class="nav-item fs-4">
                    <a class="nav-link text-orange hover-text-primary fs-5 {{ str_contains(Route::currentRouteName(), 'andro.transactional-howitworks') ? 'active fw-bold text-primary' : '' }}"
                        href="{{ route('andro.transactional-howitworks') }}"
                        style="{{ str_contains(Route::currentRouteName(), 'andro.transactional-howitworks') ? 'color: #e13362 !important;' : '' }}">
                        How It Works</a>
                </li>
                <li class="nav-item fs-4 mt-1">
                    <a href=""
                        class="text-decoration-none text-white pink-btn border-0 py-2 px-4 rounded-pill d-flex button-shadow schedule-nav"
                        style="background-color: #F25846; box-shadow: #F25846;">
                        <span class="schedule-text">Buy</span>
                    </a>
                </li>
            </ul>
        </div>

    </div>
</nav>
