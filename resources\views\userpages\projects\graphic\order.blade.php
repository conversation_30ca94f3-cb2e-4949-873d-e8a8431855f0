@extends('userpages.main')
@section('title', 'Orders')
@section('userpagesection')
    <div class="ms-lg-2 m-0">
        <div class="mb-3">
            <div class="bg-white pt-2 pb-2 pe-3 ps-3 d-flex justify-content-between header-btn-flex"
                style="border-radius: 7px;">
                <h4 class="heading-font">Graphic Project</h4>
                <div style="width: fit-content;">
                </div>
            </div>
        </div>
        @if ($graphicOrders->count() != 0)
            <div class="overflow-auto change-scroll bg-white" style="border-radius: 7px; height: 61vh;">
                <div class="table-responsive" style="max-width: 100vw;">
                    <table class="table text-center table-striped">
                        <thead style="--bs-table-bg: transparent;">
                            <tr class="text-nowrap">

                                <th scope="col">Service Name</th>
                                <th scope="col">Start Date</th>
                                <th scope="col">Total</th>
                                <th scope="col">Pending</th>
                                <th scope="col">Action</th>

                            </tr>
                        </thead>
                        <tbody>

                            @foreach ($graphicOrders as $order)
                                <tr>
                                    <td class="gray-dark-text">
                                        {{ $order->orderItem->name ?? '' }}
                                    </td>
                                    <td class="gray-dark-text text-nowrap">
                                        {{ $order->datetime ? humanDate($order->datetime) : '-' }}
                                    </td>

                                    <td class="gray-dark-text text-nowrap">
                                        {{ $order->noOfDesigns ?? '' }}
                                    </td>
                                    <td class="gray-dark-text text-break">
                                        {{ $order->noOfDesignsPending ?? '' }}
                                    </td>

                                    <td>
                                        <a href="{{ route('graphics.orders.view', ['order' => $order->id]) }}"
                                            style="background-color: #194DAB;"
                                            class="btn text-white btn-sm rounded-pill">Manage</a>

                                    </td>
                                </tr>
                            @endforeach
                        </tbody>

                    </table>
                </div>
            </div>
        @else
            <div class="d-flex align-items-center flex-column bg-white justify-content-center ">
                <img src="{{ asset('assets/userpages/images/billing.png') }}" alt="blank_page_img" class="img-fluid">
            </div>
        @endif
        @if ($graphicOrders->lastPage() > 1)
            <div class="mt-3 p-1 bg-white" style="border-radius: 7px;">
                <x-showPagination :data=$graphicOrders />
            </div>
        @endif
    </div>
@endsection
