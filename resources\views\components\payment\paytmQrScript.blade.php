@if ($hostdata->enablePaytmQr)
<script>
    $('#showQrBtn').on('click', function() {
        $('#qrLoader').show();
        $(this).prop('disabled', true);
        $.ajax({
            url: "{{ route('payment.qr') }}",
            type: "get",
            data: {
                'PaymentKey': "{{ $str }}"
            },
            success: function(response) {
                if (response.status == false) {
                    alert('{{ ErrMsg() }}')
                    $('#qrLoader').hide();
                } else {
                    $('#demoQrDiv').hide()
                    $('#MainQrDiv').html(response.displayQr)
                    $('#qrLoader').hide();
                    showQrTimer();

                }

            }
        });
    });


    function showQrTimer() {
        // var timer2 = "00:10";
        if (window.qrInterval) {
            clearInterval(window.qrInterval);
        }
        var timer2 = "5:01";
        window.qrInterval = setInterval(function() {
            var timer = timer2.split(':');
            var minutes = parseInt(timer[0], 10);
            var seconds = parseInt(timer[1], 10);
            --seconds;
            minutes = (seconds < 0) ? --minutes : minutes;
            seconds = (seconds < 0) ? 59 : seconds;
            seconds = (seconds < 10) ? '0' + seconds : seconds;
            $('.minute').html(minutes);
            $('.second').html(seconds);
            if (minutes < 0) clearInterval(window.qrInterval);
            if ((seconds <= 0) && (minutes <= 0)) clearInterval(window.qrInterval);
            timer2 = minutes + ':' + seconds;
            if (minutes === 0 && seconds === '00') {
                $('#showQrBtn').prop('disabled', false);
                $('#demoQrDiv').show()
                $('#MainQrDiv').hide()
            }
        }, 1000);
        $('#qrLoader').hide();
        $('#showQrBtn').text('Reload Qr Code').prop('disabled', false);
    };
</script>
@endif