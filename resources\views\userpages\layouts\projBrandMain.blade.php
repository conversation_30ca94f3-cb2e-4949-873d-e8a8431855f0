@extends('userpages.main')
@section('title', 'Project')



@section('userpagesection')
    {{-- <div class="row"> --}}
    {{-- <div class=" rounded-4 row d-flex justify-content-around">
        <div class="col-lg-3 d-none d-lg-inline-block">
            <div class="">
                @include('userpages.sideMenu')
            </div>
        </div>
        <div class="col-lg-9 mb-5 p-0">
        </div>
    </div> --}}
    @yield('proj-pages')

    {{-- <div class=" pb-5"> --}}


    {{-- <div class="d-flex justify-content-between bg-dark-gray rounded rounded-bottom-0 p-2"> --}}

    {{-- <div class=" align-items-sm-center d-flex overflow-auto">
                    <ul class=" nav nav-tabs flex-nowrap">
                        <li class="nav-item  bg-dark-gray  m-0"
                            style="max-height: 37px !important;min-height: 37px !important;">
                            <a aria-label="link" style="max-height: 36px !important;"
                                class="nav-link  bg-active-light-white   {{ request()->is('*roject*') ? 'border-0 active' : 'text-white' }}"
                                href="{{ route('graphic.projects') }}">Projects</a>
                        </li>
                        <li class="nav-item  bg-dark-gray  m-0"
                            style="max-height: 37px !important;min-height: 37px !important;">
                            <a aria-label="link" style="max-height: 36px !important;"
                                class="nav-link bg-active-light-white   {{ Route::is('brand*') ? 'active border-0 ' : ' text-white' }}"
                                href="{{ route('brand.index') }}">Brands</a>
                        </li>

                    </ul>
                </div> --}}
    {{-- @if (Route::is('graphic.projects') || Route::is('brand.index'))
                    <div class="text-end">
                        @include('userpages.components.searchBox')
                    </div>
                @endif --}}
    {{-- </div> --}}



    {{-- </div> --}}
    {{-- </div> --}}
@endsection
