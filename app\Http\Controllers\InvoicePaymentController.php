<?php

namespace App\Http\Controllers;

use App\Custom\Paytm;
use App\Custom\Razorpay;
use App\Models\Invoice;
use DateTime;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Validator;
use SimpleSoftwareIO\QrCode\Facades\QrCode;

class InvoicePaymentController extends Controller
{

    function details($invoice)
    {
        try {
            $data['invoice'] = Invoice::with('items', 'items.variant')->find($invoice);

            if (Auth::check() && $data['invoice']->lead_id != Auth::user()->id) {
                Auth::logout();
                Session::flush();
            }
            if ($data['invoice'] == null || $data['invoice']->isPaid == 1) {
                return redirect()->route('alreadyPaid');
            }
            $data['hostdata'] = hostData();
            $data['calculation'] = getInvoiceTotal($data['invoice']);
            if ($data['calculation']['totalPayable'] == 0) {
                return redirect('/')->with('info', 'Invalid Item.');
            }
            if ($data['invoice'] == null || $data['invoice']->isPaid == 1) {
                return redirect()->route('alreadyPaid');
            }


            return view('userpages.paymentInvoice.details', $data);
        } catch (Exception) {
            return redirect()->back()->with('error', ErrMsg());
        }
    }
    function addr($invoice)
    {
        $data['invoice'] = Invoice::with('items')->find($invoice);

        if (Auth::check() && $data['invoice']->lead_id != Auth::user()->id) {
            Auth::logout();
            Session::flush();
        }
        if ($data['invoice'] == null || $data['invoice']->isPaid == 1) {
            return redirect()->route('alreadyPaid');
        }
        $data['hostdata'] = hostData();
        $data['calculation'] = getInvoiceTotal($data['invoice']);
        if ($data['calculation']['totalPayable'] == 0) {
            return redirect('/')->with('info', 'Invalid Item.');
        }

        return view('userpages.paymentInvoice.addr', $data);
    }
    function payment($invoice)
    {
        $data['paytmGateWayEnable'] = null;
        try {
            $data['invoice'] = $invoice = Invoice::with('items')->find($invoice);

            if (Auth::check() && $data['invoice']->lead_id != Auth::user()->id) {
                Auth::logout();
                Session::flush();
            }
            if ($data['invoice'] == null || $data['invoice']->isPaid == 1) {
                return redirect()->route('alreadyPaid');
            }
            $data['calculation'] = getInvoiceTotal($data['invoice']);
            if ($data['calculation']['totalPayable'] == 0) {
                return redirect('/')->with('info', 'Invalid Item.');
            }
            if ($data['invoice']) {
                $data['hostdata'] = $hostdata = hostData();
                if ($hostdata->enableRazorPayVirtualAccount && $hostdata->razorpay_key_id &&  $hostdata->razorpay_secret_key) {
                    $checkVirtualAccount = DB::table('lead_company_wise_virtual_account')
                        ->where('lead_id',  $data['invoice']->lead_id)
                        ->first();
                    if ($checkVirtualAccount) {
                        $data['accountDetails']['account_number'] = $checkVirtualAccount->accountNumber;
                        $data['accountDetails']['bank_name'] = $checkVirtualAccount->bankName;
                        $data['accountDetails']['name'] = $checkVirtualAccount->beneficiary;
                        $data['accountDetails']['ifsc'] = $checkVirtualAccount->ifsc;
                    }
                }
                $data['paybleAmt'] = round($data['calculation']['totalPayable']);
                $data['str'] = Crypt::encryptString(json_encode(['lead_id' =>   $data['invoice']->lead_id, 'mode' => 'invoicePay', 'invoice_id' => $invoice->id, 'lead_id' => $invoice->lead_id, 'amount' => $data['paybleAmt']]));

                if ($hostdata->enableRazorPayVirtualAccount && $hostdata->razorpay_key_id &&  $hostdata->razorpay_secret_key) {

                    if ($checkVirtualAccount) {
                        if (date_diff(new DateTime(($checkVirtualAccount->datetime)), new DateTime())->days > 59) {
                            $razorpay = new Razorpay;
                            $data['accountDetails'] = $razorpay->virtualAccount($invoice->lead_id);
                        }
                    } else {
                        $razorpay = new Razorpay;
                        $data['accountDetails'] = $razorpay->virtualAccount($invoice->lead_id);
                    }
                }

                return view('userpages.paymentInvoice.payment', $data);
            } else {
                return back()->with('error', 'Invalid request')->withInput();
            }
        } catch (Exception $e) {
            return back()->with('error', ErrMsg())->withInput();
        }
    }

    function generateQr($invoice)
    {
        try {
            $data['invoice'] = $invoice = Invoice::with('items')->find($invoice);

            if (Auth::check() && $data['invoice']->lead_id != Auth::user()->id) {
                Auth::logout();
                Session::flush();
            }
            if ($data['invoice'] == null || $data['invoice']->isPaid == 1) {
                return redirect()->route('alreadyPaid');
            }
            $data['calculation'] = getInvoiceTotal($data['invoice']);
            if ($data['calculation']['totalPayable'] == 0) {
                return redirect('/')->with('info', 'Invalid Item.');
            }
            if ($data['invoice']) {
                $data['hostdata'] = $hostdata = hostData();
                $data['paybleAmt']  = $data['calculation']['totalPayable'];
                if ($hostdata->enableUpi && $hostdata->upi) {
                    $data['upiPaymentLink'] = "upi://pay?pa=$hostdata->upi&pn=$hostdata->beneficiary&tn=$invoice->proformaNumber&am=" . $data['paybleAmt'] . "&cu=INR";
                    $data['CompanyQr'] = QrCode::size(200)->style('round')
                        ->generate($data['upiPaymentLink']);
                }
                if ($hostdata->enablePaytmQr) {
                    if ($hostdata->paytm_merchant_mid && $hostdata->paytm_merchant_key) {

                        $paytm = new Paytm();
                        $paytmQr = $paytm->qr($data['paybleAmt'], $data['invoice']->lead_id, $data['invoice']->id);
                        if ($paytmQr['status']) {
                            $data['qrString'] =  $paytmQr['qr'];
                            $data['paytmQrEnable'] = true;
                        } else {
                            $data['qrString'] = asset('media/images/blank.png');
                        }
                    }
                }
                return view('userpages.paymentInvoice.payment_qr', $data);
            }
        } catch (Exception $e) {


            return back()->with('error', ErrMsg())->withInput();
        }
    }

    function chequePaid($invoice)
    {
        $data['invoice'] = Invoice::with('items')->find($invoice);

        if (Auth::check() && $data['invoice']->lead_id != Auth::user()->id) {
            Auth::logout();
            Session::flush();
        }
        if ($data['invoice'] == null || $data['invoice']->isPaid == 1) {
            return redirect()->route('alreadyPaid');
        }
        $data['hostdata'] = hostData();
        $data['calculation'] = getInvoiceTotal($data['invoice']);
        if ($data['calculation']['totalPayable'] == 0) {
            return redirect('/')->with('info', 'Invalid Item.');
        }
        return view('userpages.paymentInvoice.cheque_paid', $data);
    }
    function neftPaid($invoice)
    {
        $data['invoice'] = Invoice::find($invoice);

        if (Auth::check() && $data['invoice']->lead_id != Auth::user()->id) {
            Auth::logout();
            Session::flush();
        }
        if ($data['invoice'] == null || $data['invoice']->isPaid == 1) {
            return redirect()->route('alreadyPaid');
        }
        $data['hostdata'] = hostData();
        $data['calculation'] = getInvoiceTotal($data['invoice']);
        if ($data['calculation']['totalPayable'] == 0) {
            return redirect('/')->with('info', 'Invalid Item.');
        }
        return view('userpages.paymentInvoice.neft_paid', $data);
    }

    function addrUpdate(Request $request, $invoice)
    {

        $validator = Validator::make(
            $request->all(),
            [
                'name' => 'nullable',
                'gst' => 'nullable | regex : /^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/',
                'country' => 'nullable',
                'state' => 'nullable',
                // 'pinCode' => 'required',
            ],
            [
                'gst.regex' => 'GST is invalid',
                // 'country.required' => 'Country is required',
                // 'state.required' => 'State is required',
                // 'pinCode.required' => 'Pincode is required',
            ]
        );

        //return back if validation fails
        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }
        try {

            $invoice = Invoice::find(Crypt::decrypt($request->invoice_id));
            if ($invoice->isPaid == 1) {
                return redirect()->route('alreadyPaid');
            }
            if ($invoice) {
                $invoice->name = $request->name;
                $invoice->company = $request->company;
                $invoice->address = $request->address;
                $invoice->mobile = $request->mobile;
                $invoice->city = $request->city;
                $invoice->email = $request->email;
                $invoice->pincode = $request->pinCode;
                $invoice->state = $request->state;
                $invoice->country = $request->country;
                $invoice->gst = $request->gst;
                $invoice->update();
                return redirect()->route('pi.payment', ['invoice' => $invoice->id]);
            } else {
                return back()->with('error', 'Invalid request')->withInput();
            }
        } catch (Exception $e) {
            return back()->with('error', ErrMsg())->withInput();
        }
    }
}
