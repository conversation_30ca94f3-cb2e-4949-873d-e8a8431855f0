{{-- Category List Component --}}
@props(['categories', 'activeCategory', 'orderId', 'isMobile' => false])

<div class="p-3">
    <a href="{{ route('graphics.orders.edit', ['order' => $orderId]) }}" class="text-decoration-none text-dark">
        <h5 class="mb-3 {{ $isMobile ? 'd-none d-md-block' : 'd-none d-md-block' }}">
            <i class="fas fa-folder me-2"></i>Categories
        </h5>
    </a>
    
    @foreach ($categories as $category)
        @if ($category->subcategories->count())
            <div class="category-section mb-2">
                @php
                    $isExpanded = $activeCategory ? 
                        ($activeCategory == $category->id || in_array($activeCategory, $category->subcategories->pluck('id')->toArray())) : 
                        false;
                    $isCollapsed = !$isExpanded;
                    $isActive = $activeCategory && $category->id == $activeCategory;
                @endphp
                
                <div class="category-item p-2 rounded {{ $isCollapsed ? 'collapsed' : 'active' }} {{ $isActive ? 'active' : '' }}">
                    <a href="{{ route('graphics.orders.edit', ['order' => $orderId, 'category' => $category->id]) }}" 
                       class="text-decoration-none">
                        {{ $category->name }}
                    </a>
                    <div class="float-end d-flex gap-2">
                        <span class="badge bg-secondary">{{ $category->subcategories_count }}</span>
                        <div class="collapse-toggle {{ $isCollapsed ? 'collapsed' : '' }}" 
                             data-bs-toggle="collapse"
                             data-bs-target="#{{ $isMobile ? 'mobile-' : '' }}{{ $category->id }}-subcategories">
                            <i class="fas fa-chevron-down collapse-icon"></i>
                        </div>
                    </div>
                </div>
                
                <div class="collapse my-1 {{ $isExpanded ? 'show' : '' }}"
                     id="{{ $isMobile ? 'mobile-' : '' }}{{ $category->id }}-subcategories">
                    @foreach ($category->subcategories as $sub)
                        <a href="{{ route('graphics.orders.edit', ['order' => $orderId, 'category' => $sub->id]) }}" 
                           class="text-decoration-none text-dark">
                            <div class="subcategory-item p-2 rounded {{ $activeCategory && $sub->id == $activeCategory ? 'active' : '' }}"
                                 data-category="{{ $sub->name }}">
                                • {{ $sub->name }}
                                <span class="badge bg-light text-dark float-end">{{ $sub->designs_count }}</span>
                            </div>
                        </a>
                    @endforeach
                </div>
            </div>
        @else
            <a href="{{ route('graphics.orders.edit', ['order' => $orderId, 'category' => $category->id]) }}" 
               class="text-decoration-none text-dark">
                <div class="category-item p-2 rounded text-capitalize mb-2 {{ $activeCategory && $category->id == $activeCategory ? 'active' : '' }}"
                     data-category="{{ $category->name }}">
                    {{ $category->name }}
                    <span class="badge bg-light text-dark float-end">{{ $category->designs_count }}</span>
                </div>
            </a>
        @endif
    @endforeach
</div>
